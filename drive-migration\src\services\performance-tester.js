/**
 * Performance Testing Service
 * Test hiệu suất hệ thống với volume lớn và stability testing
 */

import os from 'os';
import { supabaseClient } from '../database/supabase.js';
import PerformanceOptimizer from './performance-optimizer.js';
import rateLimiter from './rate-limiter.js';

class PerformanceTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
        this.startTime = null;
        this.endTime = null;
        
        // Performance targets
        this.targets = {
            throughput: 500, // files per minute
            maxResponseTime: 5000, // 5 seconds
            maxMemoryUsage: 80, // 80% of available memory
            maxCpuUsage: 80, // 80% CPU usage
            errorRate: 1, // Max 1% error rate
            uptime: 99.9 // 99.9% uptime
        };

        console.log('🚀 Performance Tester initialized with targets:');
        console.log(`   📊 Throughput: ${this.targets.throughput} files/minute`);
        console.log(`   ⏱️ Response time: <${this.targets.maxResponseTime}ms`);
        console.log(`   💾 Memory usage: <${this.targets.maxMemoryUsage}%`);
        console.log(`   🔄 CPU usage: <${this.targets.maxCpuUsage}%`);
        console.log(`   ❌ Error rate: <${this.targets.errorRate}%`);
    }

    /**
     * Run comprehensive performance test suite
     */
    async runPerformanceTests() {
        try {
            console.log('🧪 Starting Performance Test Suite...\n');
            this.isRunning = true;
            this.startTime = Date.now();
            this.testResults = [];

            // Test 1: Throughput Test
            const throughputTest = await this.runThroughputTest();
            this.testResults.push(throughputTest);

            // Test 2: Load Test
            const loadTest = await this.runLoadTest();
            this.testResults.push(loadTest);

            // Test 3: Stress Test
            const stressTest = await this.runStressTest();
            this.testResults.push(stressTest);

            // Test 4: Memory Test
            const memoryTest = await this.runMemoryTest();
            this.testResults.push(memoryTest);

            // Test 5: Stability Test
            const stabilityTest = await this.runStabilityTest();
            this.testResults.push(stabilityTest);

            // Test 6: API Rate Limit Test
            const rateLimitTest = await this.runRateLimitTest();
            this.testResults.push(rateLimitTest);

            this.endTime = Date.now();
            this.isRunning = false;

            // Generate final report
            const report = this.generatePerformanceReport();
            
            console.log('\n🏁 Performance Test Suite Completed!');
            return report;

        } catch (error) {
            console.error('❌ Performance test suite failed:', error);
            this.isRunning = false;
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test throughput với target 500 files/minute
     */
    async runThroughputTest() {
        console.log('📊 Running Throughput Test...');
        
        const testStart = Date.now();
        const testFiles = 100; // Test với 100 files
        const targetTime = (testFiles / this.targets.throughput) * 60 * 1000; // Expected time in ms

        try {
            // Simulate file processing
            let processedFiles = 0;
            let errors = 0;
            const processingTimes = [];

            for (let i = 0; i < testFiles; i++) {
                const fileStart = Date.now();
                
                try {
                    // Simulate file processing time
                    const fileSize = Math.random() * 10 * 1024 * 1024; // Random size up to 10MB
                    const processingTime = this.simulateFileProcessing(fileSize);
                    
                    await new Promise(resolve => setTimeout(resolve, processingTime));
                    
                    processedFiles++;
                    processingTimes.push(Date.now() - fileStart);
                } catch (error) {
                    errors++;
                }
            }

            const testDuration = Date.now() - testStart;
            const actualThroughput = (processedFiles / (testDuration / 1000)) * 60; // files per minute
            const averageProcessingTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
            const errorRate = (errors / testFiles) * 100;

            const passed = actualThroughput >= this.targets.throughput && errorRate <= this.targets.errorRate;

            console.log(`   📊 Processed: ${processedFiles}/${testFiles} files`);
            console.log(`   🚀 Throughput: ${actualThroughput.toFixed(1)} files/minute (target: ${this.targets.throughput})`);
            console.log(`   ⏱️ Average processing time: ${averageProcessingTime.toFixed(1)}ms`);
            console.log(`   ❌ Error rate: ${errorRate.toFixed(2)}%`);
            console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);

            return {
                testName: 'Throughput Test',
                passed,
                metrics: {
                    processedFiles,
                    totalFiles: testFiles,
                    actualThroughput,
                    targetThroughput: this.targets.throughput,
                    averageProcessingTime,
                    errorRate,
                    testDuration
                }
            };

        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}\n`);
            return {
                testName: 'Throughput Test',
                passed: false,
                error: error.message
            };
        }
    }

    /**
     * Test load với concurrent users
     */
    async runLoadTest() {
        console.log('🔄 Running Load Test...');
        
        try {
            const concurrentUsers = 10;
            const filesPerUser = 20;
            const userPromises = [];

            for (let user = 0; user < concurrentUsers; user++) {
                userPromises.push(this.simulateUserLoad(user, filesPerUser));
            }

            const userResults = await Promise.all(userPromises);
            
            const totalFiles = concurrentUsers * filesPerUser;
            const totalProcessed = userResults.reduce((sum, result) => sum + result.processed, 0);
            const totalErrors = userResults.reduce((sum, result) => sum + result.errors, 0);
            const averageResponseTime = userResults.reduce((sum, result) => sum + result.averageResponseTime, 0) / concurrentUsers;
            const errorRate = (totalErrors / totalFiles) * 100;

            const passed = errorRate <= this.targets.errorRate && averageResponseTime <= this.targets.maxResponseTime;

            console.log(`   👥 Concurrent users: ${concurrentUsers}`);
            console.log(`   📊 Processed: ${totalProcessed}/${totalFiles} files`);
            console.log(`   ⏱️ Average response time: ${averageResponseTime.toFixed(1)}ms`);
            console.log(`   ❌ Error rate: ${errorRate.toFixed(2)}%`);
            console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);

            return {
                testName: 'Load Test',
                passed,
                metrics: {
                    concurrentUsers,
                    totalFiles,
                    totalProcessed,
                    totalErrors,
                    averageResponseTime,
                    errorRate
                }
            };

        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}\n`);
            return {
                testName: 'Load Test',
                passed: false,
                error: error.message
            };
        }
    }

    /**
     * Test stress với high load
     */
    async runStressTest() {
        console.log('💪 Running Stress Test...');
        
        try {
            const highLoad = 50; // High concurrent operations
            const stressPromises = [];

            for (let i = 0; i < highLoad; i++) {
                stressPromises.push(this.simulateStressOperation(i));
            }

            const stressResults = await Promise.all(stressPromises);
            
            const successfulOps = stressResults.filter(result => result.success).length;
            const failedOps = highLoad - successfulOps;
            const errorRate = (failedOps / highLoad) * 100;
            const averageTime = stressResults.reduce((sum, result) => sum + (result.duration || 0), 0) / highLoad;

            const passed = errorRate <= this.targets.errorRate * 2; // Allow higher error rate for stress test

            console.log(`   🔥 High load operations: ${highLoad}`);
            console.log(`   ✅ Successful: ${successfulOps}`);
            console.log(`   ❌ Failed: ${failedOps}`);
            console.log(`   ⏱️ Average time: ${averageTime.toFixed(1)}ms`);
            console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);

            return {
                testName: 'Stress Test',
                passed,
                metrics: {
                    highLoad,
                    successfulOps,
                    failedOps,
                    errorRate,
                    averageTime
                }
            };

        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}\n`);
            return {
                testName: 'Stress Test',
                passed: false,
                error: error.message
            };
        }
    }

    /**
     * Test memory usage
     */
    async runMemoryTest() {
        console.log('💾 Running Memory Test...');
        
        try {
            const initialMemory = process.memoryUsage();
            const memorySnapshots = [initialMemory];

            // Simulate memory-intensive operations
            const largeDataSets = [];
            for (let i = 0; i < 10; i++) {
                // Create large data set
                const largeData = new Array(100000).fill(0).map((_, index) => ({
                    id: index,
                    data: `large_data_item_${index}_${Math.random()}`
                }));
                
                largeDataSets.push(largeData);
                memorySnapshots.push(process.memoryUsage());
                
                // Small delay
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            const finalMemory = process.memoryUsage();
            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            const maxMemoryUsed = Math.max(...memorySnapshots.map(snap => snap.heapUsed));
            
            // Calculate memory usage percentage (assuming 1GB available)
            const availableMemory = 1024 * 1024 * 1024; // 1GB
            const memoryUsagePercent = (maxMemoryUsed / availableMemory) * 100;

            const passed = memoryUsagePercent <= this.targets.maxMemoryUsage;

            console.log(`   📈 Initial memory: ${this.formatBytes(initialMemory.heapUsed)}`);
            console.log(`   📊 Final memory: ${this.formatBytes(finalMemory.heapUsed)}`);
            console.log(`   📏 Memory increase: ${this.formatBytes(memoryIncrease)}`);
            console.log(`   💾 Max usage: ${memoryUsagePercent.toFixed(1)}%`);
            console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);

            // Cleanup
            largeDataSets.length = 0;
            global.gc && global.gc(); // Force garbage collection if available

            return {
                testName: 'Memory Test',
                passed,
                metrics: {
                    initialMemory: initialMemory.heapUsed,
                    finalMemory: finalMemory.heapUsed,
                    memoryIncrease,
                    maxMemoryUsed,
                    memoryUsagePercent
                }
            };

        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}\n`);
            return {
                testName: 'Memory Test',
                passed: false,
                error: error.message
            };
        }
    }

    /**
     * Test stability over time
     */
    async runStabilityTest() {
        console.log('⏳ Running Stability Test...');
        
        try {
            const testDuration = 30000; // 30 seconds
            const testStart = Date.now();
            let operations = 0;
            let errors = 0;
            const responseTimes = [];

            while (Date.now() - testStart < testDuration) {
                const opStart = Date.now();
                
                try {
                    // Simulate operation
                    await this.simulateOperation();
                    operations++;
                    responseTimes.push(Date.now() - opStart);
                } catch (error) {
                    errors++;
                }
                
                // Small delay between operations
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            const actualDuration = Date.now() - testStart;
            const errorRate = (errors / operations) * 100;
            const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            const operationsPerSecond = (operations / (actualDuration / 1000));

            const passed = errorRate <= this.targets.errorRate && averageResponseTime <= this.targets.maxResponseTime;

            console.log(`   ⏱️ Test duration: ${(actualDuration / 1000).toFixed(1)}s`);
            console.log(`   🔄 Operations: ${operations}`);
            console.log(`   📊 Ops/second: ${operationsPerSecond.toFixed(1)}`);
            console.log(`   ⏱️ Avg response time: ${averageResponseTime.toFixed(1)}ms`);
            console.log(`   ❌ Error rate: ${errorRate.toFixed(2)}%`);
            console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);

            return {
                testName: 'Stability Test',
                passed,
                metrics: {
                    testDuration: actualDuration,
                    operations,
                    errors,
                    errorRate,
                    averageResponseTime,
                    operationsPerSecond
                }
            };

        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}\n`);
            return {
                testName: 'Stability Test',
                passed: false,
                error: error.message
            };
        }
    }

    /**
     * Test API rate limiting
     */
    async runRateLimitTest() {
        console.log('🚦 Running Rate Limit Test...');
        
        try {
            const testRequests = 200; // Test with many requests
            const requestPromises = [];

            for (let i = 0; i < testRequests; i++) {
                requestPromises.push(this.simulateAPIRequest('google', i));
            }

            const results = await Promise.all(requestPromises);
            
            const successful = results.filter(r => r.success).length;
            const rateLimited = results.filter(r => r.rateLimited).length;
            const errors = results.filter(r => r.error && !r.rateLimited).length;
            
            const rateLimitHandling = rateLimited > 0; // Rate limiting should kick in
            const errorRate = (errors / testRequests) * 100;

            const passed = rateLimitHandling && errorRate <= this.targets.errorRate;

            console.log(`   📊 Total requests: ${testRequests}`);
            console.log(`   ✅ Successful: ${successful}`);
            console.log(`   🚦 Rate limited: ${rateLimited}`);
            console.log(`   ❌ Errors: ${errors}`);
            console.log(`   🔧 Rate limiting working: ${rateLimitHandling ? 'Yes' : 'No'}`);
            console.log(`   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);

            return {
                testName: 'Rate Limit Test',
                passed,
                metrics: {
                    testRequests,
                    successful,
                    rateLimited,
                    errors,
                    rateLimitHandling,
                    errorRate
                }
            };

        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}\n`);
            return {
                testName: 'Rate Limit Test',
                passed: false,
                error: error.message
            };
        }
    }

    /**
     * Simulate file processing time based on size
     */
    simulateFileProcessing(fileSize) {
        const baseTime = 100; // 100ms base
        const sizeTime = (fileSize / (1024 * 1024)) * 50; // 50ms per MB
        return Math.floor(baseTime + sizeTime);
    }

    /**
     * Simulate user load
     */
    async simulateUserLoad(userId, fileCount) {
        let processed = 0;
        let errors = 0;
        const responseTimes = [];

        for (let i = 0; i < fileCount; i++) {
            const start = Date.now();
            
            try {
                await this.simulateOperation();
                processed++;
                responseTimes.push(Date.now() - start);
            } catch (error) {
                errors++;
            }
        }

        const averageResponseTime = responseTimes.length > 0 ? 
            responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;

        return {
            userId,
            processed,
            errors,
            averageResponseTime
        };
    }

    /**
     * Simulate stress operation
     */
    async simulateStressOperation(opId) {
        const start = Date.now();
        
        try {
            // Simulate CPU-intensive operation
            const iterations = Math.floor(Math.random() * 10000) + 1000;
            let result = 0;
            for (let i = 0; i < iterations; i++) {
                result += Math.sqrt(i);
            }
            
            // Random delay
            await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
            
            return {
                opId,
                success: true,
                duration: Date.now() - start,
                result
            };
        } catch (error) {
            return {
                opId,
                success: false,
                duration: Date.now() - start,
                error: error.message
            };
        }
    }

    /**
     * Simulate general operation
     */
    async simulateOperation() {
        // Random processing time
        const processingTime = Math.random() * 200 + 50; // 50-250ms
        await new Promise(resolve => setTimeout(resolve, processingTime));
        
        // Random chance of error (1%)
        if (Math.random() < 0.01) {
            throw new Error('Simulated operation error');
        }
        
        return { success: true, processingTime };
    }

    /**
     * Simulate API request
     */
    async simulateAPIRequest(service, requestId) {
        try {
            // Check if rate limited
            if (!rateLimiter.canMakeRequest(service)) {
                return {
                    requestId,
                    success: false,
                    rateLimited: true
                };
            }
            
            // Record request
            rateLimiter.recordRequest(service);
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
            
            return {
                requestId,
                success: true,
                rateLimited: false
            };
        } catch (error) {
            return {
                requestId,
                success: false,
                rateLimited: false,
                error: error.message
            };
        }
    }

    /**
     * Generate performance test report
     */
    generatePerformanceReport() {
        const totalDuration = this.endTime - this.startTime;
        const passedTests = this.testResults.filter(test => test.passed).length;
        const totalTests = this.testResults.length;
        const overallPassed = passedTests === totalTests;

        const report = {
            success: overallPassed,
            summary: {
                totalTests,
                passedTests,
                failedTests: totalTests - passedTests,
                overallPassed,
                totalDuration,
                testDate: new Date().toISOString()
            },
            targets: this.targets,
            results: this.testResults,
            recommendations: this.generateRecommendations()
        };

        console.log('\n📊 Performance Test Report:');
        console.log(`   🧪 Total tests: ${totalTests}`);
        console.log(`   ✅ Passed: ${passedTests}`);
        console.log(`   ❌ Failed: ${totalTests - passedTests}`);
        console.log(`   ⏱️ Duration: ${(totalDuration / 1000).toFixed(1)}s`);
        console.log(`   🎯 Overall: ${overallPassed ? '✅ PASSED' : '❌ FAILED'}`);

        return report;
    }

    /**
     * Generate recommendations based on test results
     */
    generateRecommendations() {
        const recommendations = [];
        
        this.testResults.forEach(test => {
            if (!test.passed) {
                switch (test.testName) {
                    case 'Throughput Test':
                        recommendations.push('Consider optimizing file processing algorithms or increasing parallel processing');
                        break;
                    case 'Load Test':
                        recommendations.push('Implement better load balancing or increase server capacity');
                        break;
                    case 'Stress Test':
                        recommendations.push('Add circuit breakers and improve error handling under high load');
                        break;
                    case 'Memory Test':
                        recommendations.push('Optimize memory usage and implement better garbage collection');
                        break;
                    case 'Stability Test':
                        recommendations.push('Investigate and fix stability issues causing errors over time');
                        break;
                    case 'Rate Limit Test':
                        recommendations.push('Improve rate limiting implementation and error handling');
                        break;
                }
            }
        });

        if (recommendations.length === 0) {
            recommendations.push('All performance tests passed! System is ready for production.');
        }

        return recommendations;
    }

    /**
     * Format bytes to human readable
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get current system metrics
     */
    getSystemMetrics() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        return {
            memory: {
                used: memUsage.heapUsed,
                total: memUsage.heapTotal,
                external: memUsage.external,
                rss: memUsage.rss
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            uptime: process.uptime(),
            loadAverage: os.loadavg()
        };
    }
}

export default PerformanceTester;
