const express = require('express');
const cors = require('cors');
const path = require('path');
const WebSocket = require('ws');
const http = require('http');
const duplicateScanner = require('./src/duplicateScanner');
const fileManager = require('./src/fileManager');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Store active scan sessions
const activeSessions = new Map();

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('Client connected');
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            if (data.type === 'register') {
                activeSessions.set(data.sessionId, ws);
            }
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    });
    
    ws.on('close', () => {
        console.log('Client disconnected');
        // Remove from active sessions
        for (const [sessionId, socket] of activeSessions.entries()) {
            if (socket === ws) {
                activeSessions.delete(sessionId);
                break;
            }
        }
    });
});

// API Routes
app.post('/api/scan', async (req, res) => {
    try {
        const { directoryPath } = req.body;
        const sessionId = Date.now().toString();
        
        if (!directoryPath) {
            return res.status(400).json({ error: 'Directory path is required' });
        }

        // Start scanning in background
        const progressCallback = (progress) => {
            const ws = activeSessions.get(sessionId);
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'progress',
                    data: progress
                }));
            }
        };

        duplicateScanner.scanDirectory(directoryPath, progressCallback)
            .then(duplicates => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'complete',
                        data: duplicates
                    }));
                }
            })
            .catch(error => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'error',
                        data: { message: error.message }
                    }));
                }
            });

        res.json({ sessionId });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/delete', async (req, res) => {
    try {
        const { filePaths } = req.body;
        const sessionId = Date.now().toString();
        
        if (!filePaths || !Array.isArray(filePaths)) {
            return res.status(400).json({ error: 'File paths array is required' });
        }

        const progressCallback = (progress) => {
            const ws = activeSessions.get(sessionId);
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'deleteProgress',
                    data: progress
                }));
            }
        };

        fileManager.deleteFiles(filePaths, progressCallback)
            .then(result => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'deleteComplete',
                        data: result
                    }));
                }
            })
            .catch(error => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'deleteError',
                        data: { message: error.message }
                    }));
                }
            });

        res.json({ sessionId });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Open http://localhost:${PORT} to use the application`);
});
