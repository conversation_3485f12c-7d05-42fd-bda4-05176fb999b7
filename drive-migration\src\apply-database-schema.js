import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { supabaseClient } from './database/supabase.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Apply database schema to Supabase
 */
async function applyDatabaseSchema() {
    console.log('🗄️ Applying Database Schema to Supabase...\n');

    try {
        // Read schema file
        const schemaPath = path.join(__dirname, '../database/schema.sql');
        const schemaSQL = fs.readFileSync(schemaPath, 'utf8');

        console.log('📄 Schema file loaded successfully');
        console.log(`📊 Schema size: ${schemaSQL.length} characters`);
        console.log('');

        // Split schema into individual statements
        const statements = schemaSQL
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        console.log(`🔧 Found ${statements.length} SQL statements to execute`);
        console.log('');

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        // Execute each statement
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            // Skip comments and empty statements
            if (statement.startsWith('--') || statement.trim().length === 0) {
                continue;
            }

            try {
                console.log(`⚙️ Executing statement ${i + 1}/${statements.length}...`);
                
                // Extract table/object name for better logging
                const match = statement.match(/CREATE TABLE (\w+)|CREATE INDEX (\w+)|ALTER TABLE (\w+)/i);
                const objectName = match ? (match[1] || match[2] || match[3]) : 'unknown';
                
                console.log(`   📋 Object: ${objectName}`);

                const { error } = await supabaseClient.getServiceClient().rpc('exec_sql', {
                    sql: statement + ';'
                });

                if (error) {
                    // Some errors are expected (like table already exists)
                    if (error.message.includes('already exists') || 
                        error.message.includes('does not exist')) {
                        console.log(`   ⚠️ Warning: ${error.message}`);
                    } else {
                        console.log(`   ❌ Error: ${error.message}`);
                        errors.push({
                            statement: statement.substring(0, 100) + '...',
                            error: error.message
                        });
                        errorCount++;
                    }
                } else {
                    console.log(`   ✅ Success`);
                    successCount++;
                }

            } catch (error) {
                console.log(`   ❌ Exception: ${error.message}`);
                errors.push({
                    statement: statement.substring(0, 100) + '...',
                    error: error.message
                });
                errorCount++;
            }

            console.log('');
        }

        // Summary
        console.log('📊 Schema Application Summary:');
        console.log(`✅ Successful statements: ${successCount}`);
        console.log(`❌ Failed statements: ${errorCount}`);
        console.log('');

        if (errors.length > 0) {
            console.log('❌ Errors encountered:');
            errors.forEach((err, index) => {
                console.log(`${index + 1}. ${err.statement}`);
                console.log(`   Error: ${err.error}`);
                console.log('');
            });
        }

        // Test the applied schema
        console.log('🧪 Testing applied schema...');
        await testAppliedSchema();

    } catch (error) {
        console.error('❌ Failed to apply database schema:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

/**
 * Test the applied schema
 */
async function testAppliedSchema() {
    const tests = [
        {
            name: 'scan_sessions table',
            query: 'SELECT * FROM scan_sessions LIMIT 1'
        },
        {
            name: 'scanned_files table',
            query: 'SELECT * FROM scanned_files LIMIT 1'
        },
        {
            name: 'migration_tasks table',
            query: 'SELECT * FROM migration_tasks LIMIT 1'
        },
        {
            name: 'migration_items table',
            query: 'SELECT * FROM migration_items LIMIT 1'
        },
        {
            name: 'users table',
            query: 'SELECT * FROM users LIMIT 1'
        }
    ];

    for (const test of tests) {
        try {
            const { error } = await supabaseClient.getServiceClient().rpc('exec_sql', {
                sql: test.query
            });

            if (error) {
                console.log(`❌ ${test.name}: ${error.message}`);
            } else {
                console.log(`✅ ${test.name}: OK`);
            }
        } catch (error) {
            console.log(`❌ ${test.name}: ${error.message}`);
        }
    }
}

/**
 * Alternative method using direct SQL execution
 */
async function applySchemaAlternative() {
    console.log('🔄 Trying alternative schema application method...\n');

    try {
        // Read schema file
        const schemaPath = path.join(__dirname, '../database/schema.sql');
        const schemaSQL = fs.readFileSync(schemaPath, 'utf8');

        // Try to execute the entire schema at once
        const { data, error } = await supabaseClient.getServiceClient()
            .from('information_schema.tables')
            .select('table_name')
            .eq('table_schema', 'public');

        if (error) {
            console.log('❌ Cannot access database:', error.message);
            return;
        }

        console.log('✅ Database connection successful');
        console.log(`📋 Found ${data.length} existing tables`);
        
        // List existing tables
        if (data.length > 0) {
            console.log('📊 Existing tables:');
            data.forEach(table => {
                console.log(`  - ${table.table_name}`);
            });
        }

        console.log('');
        console.log('💡 To apply the full schema, please:');
        console.log('1. Go to your Supabase dashboard');
        console.log('2. Navigate to SQL Editor');
        console.log('3. Copy and paste the contents of database/schema.sql');
        console.log('4. Execute the SQL statements');
        console.log('');
        console.log('🔗 Schema file location: database/schema.sql');

    } catch (error) {
        console.error('❌ Alternative method failed:', error.message);
    }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    console.log('🚀 Database Schema Application Tool\n');
    
    applyDatabaseSchema()
        .then(() => {
            console.log('\n🏁 Schema application completed!');
            console.log('💡 If there were errors, try the alternative method or apply manually via Supabase dashboard');
        })
        .catch(error => {
            console.error('\n❌ Schema application failed:', error.message);
            console.log('\n🔄 Trying alternative method...');
            return applySchemaAlternative();
        });
}

export { applyDatabaseSchema, testAppliedSchema };
