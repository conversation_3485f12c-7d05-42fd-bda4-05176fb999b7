/**
 * Report Service - Tạo báo cáo migration chi tiết
 * Hỗ trợ export CSV và PDF
 */

import fs from 'fs/promises';
import path from 'path';
import PDFDocument from 'pdfkit';
import { createObjectCsvWriter } from 'csv-writer';
import { supabaseClient } from '../database/supabase.js';

class ReportService {
    constructor() {
        this.reportsDir = path.join(process.cwd(), 'reports');
        this.ensureReportsDirectory();
    }

    async ensureReportsDirectory() {
        try {
            await fs.mkdir(this.reportsDir, { recursive: true });
        } catch (error) {
            console.error('Error creating reports directory:', error);
        }
    }

    /**
     * Tạo báo cáo migration summary
     */
    async generateMigrationSummaryReport(migrationTaskId, format = 'csv') {
        try {
            console.log(`🔄 Generating ${format.toUpperCase()} migration summary report for task ${migrationTaskId}`);

            // Lấy thông tin migration task
            const { data: migrationTask, error: taskError } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .select('*')
                .eq('id', migrationTaskId)
                .single();

            if (taskError) throw taskError;

            // Lấy thống kê migration items
            const { data: stats, error: statsError } = await supabaseClient.getServiceClient()
                .rpc('get_migration_stats', { task_id: migrationTaskId });

            if (statsError) throw statsError;

            // Lấy chi tiết migration items
            const { data: items, error: itemsError } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .select('*')
                .eq('migration_task_id', migrationTaskId)
                .order('created_at', { ascending: true });

            if (itemsError) throw itemsError;

            const reportData = {
                migrationTask,
                stats: stats[0] || {},
                items: items || []
            };

            if (format === 'csv') {
                return await this.generateCSVReport(reportData, migrationTaskId);
            } else if (format === 'pdf') {
                return await this.generatePDFReport(reportData, migrationTaskId);
            } else {
                throw new Error(`Unsupported format: ${format}`);
            }

        } catch (error) {
            console.error('❌ Error generating migration report:', error);
            throw error;
        }
    }

    /**
     * Tạo báo cáo CSV chi tiết
     */
    async generateCSVReport(reportData, migrationTaskId) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `migration-report-${migrationTaskId}-${timestamp}.csv`;
        const filepath = path.join(this.reportsDir, filename);

        // Chuẩn bị dữ liệu cho CSV
        const csvData = reportData.items.map(item => ({
            'File ID': item.google_file_id,
            'File Name': item.file_name,
            'File Type': item.file_type,
            'File Size (bytes)': item.file_size || 0,
            'File Size (readable)': this.formatFileSize(item.file_size || 0),
            'Source Path': item.source_path,
            'Target Path': item.target_path,
            'Status': item.status,
            'Error Message': item.error_message || '',
            'Lark File Token': item.lark_file_token || '',
            'Created At': item.created_at,
            'Updated At': item.updated_at,
            'Processing Time (ms)': item.processing_time_ms || 0,
            'Retry Count': item.retry_count || 0
        }));

        // Tạo CSV writer
        const csvWriter = createObjectCsvWriter({
            path: filepath,
            header: [
                { id: 'File ID', title: 'File ID' },
                { id: 'File Name', title: 'File Name' },
                { id: 'File Type', title: 'File Type' },
                { id: 'File Size (bytes)', title: 'File Size (bytes)' },
                { id: 'File Size (readable)', title: 'File Size (readable)' },
                { id: 'Source Path', title: 'Source Path' },
                { id: 'Target Path', title: 'Target Path' },
                { id: 'Status', title: 'Status' },
                { id: 'Error Message', title: 'Error Message' },
                { id: 'Lark File Token', title: 'Lark File Token' },
                { id: 'Created At', title: 'Created At' },
                { id: 'Updated At', title: 'Updated At' },
                { id: 'Processing Time (ms)', title: 'Processing Time (ms)' },
                { id: 'Retry Count', title: 'Retry Count' }
            ]
        });

        await csvWriter.writeRecords(csvData);

        // Thêm summary vào đầu file
        const summaryData = this.generateSummaryText(reportData);
        const csvContent = await fs.readFile(filepath, 'utf8');
        const finalContent = `# Migration Report Summary\n${summaryData}\n\n${csvContent}`;
        await fs.writeFile(filepath, finalContent);

        console.log(`✅ CSV report generated: ${filename}`);
        return {
            filename,
            filepath,
            format: 'csv',
            size: (await fs.stat(filepath)).size
        };
    }

    /**
     * Tạo báo cáo PDF
     */
    async generatePDFReport(reportData, migrationTaskId) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `migration-report-${migrationTaskId}-${timestamp}.pdf`;
        const filepath = path.join(this.reportsDir, filename);

        const doc = new PDFDocument();
        const stream = (await import('fs')).createWriteStream(filepath);
        doc.pipe(stream);

        // Header
        doc.fontSize(20).text('Drive-to-Lark Migration Report', { align: 'center' });
        doc.moveDown();

        // Migration Task Info
        doc.fontSize(16).text('Migration Task Information', { underline: true });
        doc.fontSize(12);
        doc.text(`Task ID: ${reportData.migrationTask.id}`);
        doc.text(`Task Name: ${reportData.migrationTask.task_name || 'N/A'}`);
        doc.text(`Status: ${reportData.migrationTask.status}`);
        doc.text(`Created: ${new Date(reportData.migrationTask.created_at).toLocaleString()}`);
        doc.text(`Updated: ${new Date(reportData.migrationTask.updated_at).toLocaleString()}`);
        doc.moveDown();

        // Statistics
        doc.fontSize(16).text('Migration Statistics', { underline: true });
        doc.fontSize(12);
        const stats = reportData.stats;
        doc.text(`Total Files: ${stats.total_files || 0}`);
        doc.text(`Completed: ${stats.completed_files || 0}`);
        doc.text(`Failed: ${stats.failed_files || 0}`);
        doc.text(`In Progress: ${stats.in_progress_files || 0}`);
        doc.text(`Success Rate: ${stats.success_rate || 0}%`);
        doc.text(`Total Size: ${this.formatFileSize(stats.total_size || 0)}`);
        doc.moveDown();

        // File Details (first 50 files)
        doc.fontSize(16).text('File Details (First 50 files)', { underline: true });
        doc.fontSize(10);
        
        const itemsToShow = reportData.items.slice(0, 50);
        itemsToShow.forEach((item, index) => {
            if (doc.y > 700) { // New page if needed
                doc.addPage();
            }
            
            doc.text(`${index + 1}. ${item.file_name}`);
            doc.text(`   Status: ${item.status} | Size: ${this.formatFileSize(item.file_size || 0)}`);
            doc.text(`   Path: ${item.source_path || 'N/A'}`);
            if (item.error_message) {
                doc.text(`   Error: ${item.error_message}`);
            }
            doc.moveDown(0.5);
        });

        if (reportData.items.length > 50) {
            doc.text(`... and ${reportData.items.length - 50} more files`);
        }

        doc.end();

        return new Promise((resolve, reject) => {
            stream.on('finish', async () => {
                console.log(`✅ PDF report generated: ${filename}`);
                const stats = await fs.stat(filepath);
                resolve({
                    filename,
                    filepath,
                    format: 'pdf',
                    size: stats.size
                });
            });
            stream.on('error', reject);
        });
    }

    /**
     * Tạo summary text
     */
    generateSummaryText(reportData) {
        const task = reportData.migrationTask;
        const stats = reportData.stats;
        
        return `
Migration Task: ${task.task_name || task.id}
Status: ${task.status}
Created: ${new Date(task.created_at).toLocaleString()}
Updated: ${new Date(task.updated_at).toLocaleString()}

Statistics:
- Total Files: ${stats.total_files || 0}
- Completed: ${stats.completed_files || 0}
- Failed: ${stats.failed_files || 0}
- In Progress: ${stats.in_progress_files || 0}
- Success Rate: ${stats.success_rate || 0}%
- Total Size: ${this.formatFileSize(stats.total_size || 0)}

Generated: ${new Date().toLocaleString()}
        `.trim();
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Lấy danh sách báo cáo đã tạo
     */
    async getAvailableReports() {
        try {
            const files = await fs.readdir(this.reportsDir);
            const reports = [];

            for (const file of files) {
                if (file.endsWith('.csv') || file.endsWith('.pdf')) {
                    const filepath = path.join(this.reportsDir, file);
                    const stats = await fs.stat(filepath);
                    reports.push({
                        filename: file,
                        filepath,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime
                    });
                }
            }

            return reports.sort((a, b) => b.created - a.created);
        } catch (error) {
            console.error('Error getting available reports:', error);
            return [];
        }
    }

    /**
     * Xóa báo cáo cũ (giữ lại 10 báo cáo gần nhất)
     */
    async cleanupOldReports(keepCount = 10) {
        try {
            const reports = await this.getAvailableReports();
            if (reports.length <= keepCount) return;

            const toDelete = reports.slice(keepCount);
            for (const report of toDelete) {
                await fs.unlink(report.filepath);
                console.log(`🗑️ Deleted old report: ${report.filename}`);
            }
        } catch (error) {
            console.error('Error cleaning up old reports:', error);
        }
    }
}

export default ReportService;
