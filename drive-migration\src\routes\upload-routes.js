import express from 'express';
import UploadSessionService from '../services/upload-session-service.js';
import { formatError } from '../utils/error-utils.js';

const router = express.Router();
const uploadSessionService = new UploadSessionService();

/**
 * Upload Routes
 * API endpoints cho quản lý upload sessions và upload files lên Lark Drive
 */

// GET /api/upload/sessions - <PERSON><PERSON><PERSON> s<PERSON>ch upload sessions
router.get('/sessions', async (req, res) => {
    try {
        const { page = 1, limit = 10, status, user_email } = req.query;
        
        const result = await uploadSessionService.getSessions({
            page: parseInt(page),
            limit: parseInt(limit),
            status,
            userEmail: user_email
        });
        
        res.json(result);
    } catch (error) {
        console.error('Error getting upload sessions:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to get upload sessions',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// GET /api/upload/sessions/:id - <PERSON><PERSON><PERSON> chi tiết upload session
router.get('/sessions/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const session = await uploadSessionService.getSession(id);
        
        if (!session) {
            return res.status(404).json({
                error: 'Upload session not found'
            });
        }
        
        res.json(session);
    } catch (error) {
        console.error('Error getting upload session:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to get upload session',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// POST /api/upload/sessions - Tạo upload session mới
router.post('/sessions', async (req, res) => {
    try {
        const {
            name,
            selectedUsers,
            rootFolderPath,
            larkTargetFolder,
            concurrentUploads = 10,
            maxRetries = 1,
            skipMimeTypes = [],
            maxFileSize = 104857600, // 100MB
            duplicateHandling = 'skip',
            batchSize = 50,
            bandwidthLimit,
            validateUpload = true,
            options = {}
        } = req.body;

        // Validation
        if (!name || !selectedUsers || !Array.isArray(selectedUsers) || selectedUsers.length === 0) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Name and selectedUsers are required'
            });
        }

        if (!rootFolderPath) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Root folder path is required'
            });
        }

        const sessionData = {
            name,
            selectedUsers,
            rootFolderPath,
            larkTargetFolder,
            concurrentUploads,
            maxRetries,
            skipMimeTypes,
            maxFileSize,
            duplicateHandling,
            batchSize,
            bandwidthLimit,
            validateUpload,
            options
        };

        const session = await uploadSessionService.createSession(sessionData);
        
        res.status(201).json(session);
    } catch (error) {
        console.error('Error creating upload session:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to create upload session',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// POST /api/upload/sessions/:id/start - Bắt đầu upload session
router.post('/sessions/:id/start', async (req, res) => {
    try {
        const { id } = req.params;
        
        const result = await uploadSessionService.startSession(id);
        
        res.json({
            message: 'Upload session started successfully',
            sessionId: id,
            ...result
        });
    } catch (error) {
        console.error('Error starting upload session:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to start upload session',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// POST /api/upload/sessions/:id/pause - Tạm dừng upload session
router.post('/sessions/:id/pause', async (req, res) => {
    try {
        const { id } = req.params;
        
        await uploadSessionService.pauseSession(id);
        
        res.json({
            message: 'Upload session paused successfully',
            sessionId: id
        });
    } catch (error) {
        console.error('Error pausing upload session:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to pause upload session',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// POST /api/upload/sessions/:id/resume - Tiếp tục upload session
router.post('/sessions/:id/resume', async (req, res) => {
    try {
        const { id } = req.params;
        
        const result = await uploadSessionService.resumeSession(id);
        
        res.json({
            message: 'Upload session resumed successfully',
            sessionId: id,
            ...result
        });
    } catch (error) {
        console.error('Error resuming upload session:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to resume upload session',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// POST /api/upload/sessions/:id/cancel - Hủy upload session
router.post('/sessions/:id/cancel', async (req, res) => {
    try {
        const { id } = req.params;
        
        await uploadSessionService.cancelSession(id);
        
        res.json({
            message: 'Upload session cancelled successfully',
            sessionId: id
        });
    } catch (error) {
        console.error('Error cancelling upload session:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to cancel upload session',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// GET /api/upload/sessions/:id/status - Lấy trạng thái upload session
router.get('/sessions/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        
        const status = await uploadSessionService.getSessionStatus(id);
        
        if (!status) {
            return res.status(404).json({
                error: 'Upload session not found'
            });
        }
        
        res.json(status);
    } catch (error) {
        console.error('Error getting upload session status:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to get upload session status',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// GET /api/upload/sessions/:id/items - Lấy danh sách upload items
router.get('/sessions/:id/items', async (req, res) => {
    try {
        const { id } = req.params;
        const { page = 1, limit = 50, status, user_email } = req.query;
        
        const result = await uploadSessionService.getSessionItems(id, {
            page: parseInt(page),
            limit: parseInt(limit),
            status,
            userEmail: user_email
        });
        
        res.json(result);
    } catch (error) {
        console.error('Error getting upload session items:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to get upload session items',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// GET /api/upload/users - Lấy danh sách users có file đã download
router.get('/users', async (req, res) => {
    try {
        const users = await uploadSessionService.getAvailableUsers();
        res.json(users);
    } catch (error) {
        console.error('Error getting available users:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to get available users',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

// GET /api/upload/stats - Lấy thống kê upload
router.get('/stats', async (req, res) => {
    try {
        const { session_id, user_email } = req.query;
        
        const stats = await uploadSessionService.getUploadStats({
            sessionId: session_id,
            userEmail: user_email
        });
        
        res.json(stats);
    } catch (error) {
        console.error('Error getting upload stats:', error);
        const errorInfo = formatError(error);
        res.status(500).json({
            error: 'Failed to get upload stats',
            message: errorInfo.message,
            details: errorInfo.details
        });
    }
});

export default router;
