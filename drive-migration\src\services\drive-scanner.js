import { googleDriveAPI } from "../api/google-drive-api.js";
import { supabaseClient } from "../database/supabase.js";

/**
 * Semaphore for controlling concurrency
 */
class Semaphore {
  constructor(maxConcurrency) {
    this.maxConcurrency = maxConcurrency;
    this.currentConcurrency = 0;
    this.queue = [];
  }

  async acquire() {
    return new Promise((resolve) => {
      if (this.currentConcurrency < this.maxConcurrency) {
        this.currentConcurrency++;
        resolve(() => this.release());
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release() {
    this.currentConcurrency--;
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      this.currentConcurrency++;
      next(() => this.release());
    }
  }
}

/**
 * Drive Scanner Service
 * Comprehensive scanning service for Google Drive with scope selection
 */
export class DriveScanner {
  constructor() {
    this.driveAPI = googleDriveAPI;
    this.supabase = supabaseClient;

    // Scanning configuration
    this.config = {
      maxDepth: 100,
      batchSize: 100,
      maxConcurrentRequests: 20,
      supportedMimeTypes: [
        "application/vnd.google-apps.document",
        "application/vnd.google-apps.spreadsheet",
        "application/vnd.google-apps.presentation",
        "application/vnd.google-apps.folder",
        "application/pdf",
        "image/jpeg",
        "image/png",
        "text/plain",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ],
    };

    // Scanning state
    this.scanState = {
      isScanning: false,
      totalFiles: 0,
      scannedFiles: 0,
      currentDepth: 0,
      totalUsers: 0,
      completedUsers: 0,
      failedUsers: 0,
      errors: [],
      startTime: null,
      estimatedTimeRemaining: null,
    };

    // Path cache for folder resolution
    this.pathCache = new Map();
    this.folderHierarchy = new Map();
  }

  /**
   * Start comprehensive Drive scan
   * @param {string[]} userEmails - Array of user emails
   * @param {object} options - Scan options
   * @returns {Promise<object>} Scan results
   */
  async startFullScan(userEmails, options = {}) {
    try {
      console.log(`🔍 Starting full Drive scan for: ${userEmails.join(", ")}`);

      this.scanState.isScanning = true;
      this.scanState.startTime = Date.now();
      this.scanState.errors = [];
      this.scanState.totalUsers = userEmails.length;
      this.scanState.completedUsers = 0;
      this.scanState.failedUsers = 0;

      const scanOptions = {
        includeSharedDrives: true,
        includeTrash: false,
        maxDepth: options.maxDepth || this.config.maxDepth,
        filterMimeTypes:
          options.filterMimeTypes || this.config.supportedMimeTypes,
        ...options,
      };

      // Create scan session in database
      const scanSession = await this.createScanSession(userEmails, scanOptions);

      // Process all users in parallel using Promise.all
      let results = { sessionId: scanSession.id, totalFiles: 0, totalSize: 0 };

      console.log(
        `🚀 Processing ${userEmails.length} users in TRUE PARALLEL mode...`
      );

      // Create a semaphore to control concurrency
      const semaphore = new Semaphore(this.config.maxConcurrentRequests);
      let completedUsers = 0;

      // Create promises for ALL users at once (true parallel)
      console.log(
        `⚡ Creating ${
          userEmails.length
        } concurrent promises at ${new Date().toISOString()}`
      );

      const allUserPromises = userEmails.map(async (email, index) => {
        const startTime = Date.now();
        console.log(
          `🔄 Promise ${index + 1}/${
            userEmails.length
          } created for ${email} at ${new Date().toISOString()}`
        );

        // Acquire semaphore before processing
        console.log(
          `⏳ ${email}: Waiting for semaphore... (queue position: ${semaphore.queue.length})`
        );
        const release = await semaphore.acquire();
        const acquireTime = Date.now();
        console.log(
          `🚀 ${email}: Got semaphore! Wait time: ${
            acquireTime - startTime
          }ms, Active: ${semaphore.currentConcurrency}/${
            semaphore.maxConcurrency
          }`
        );

        try {
          console.log(
            `📧 Starting scan for user: ${email} [${new Date().toLocaleTimeString()}] - Thread: ${
              index + 1
            }`
          );
          const scanStartTime = Date.now();

          const result = await this.scanFromRoot(
            email,
            scanOptions,
            scanSession.id
          );

          const scanDuration = Date.now() - scanStartTime;

          // Update progress atomically
          completedUsers++;
          this.scanState.completedUsers = completedUsers;

          console.log(
            `✅ Completed scan for user: ${email} (${
              result.totalFiles
            } files) - Scan time: ${scanDuration}ms - Progress: ${completedUsers}/${
              userEmails.length
            } [${new Date().toLocaleTimeString()}]`
          );

          return {
            success: true,
            email,
            result,
            scanDuration,
            waitTime: acquireTime - startTime,
          };
        } catch (error) {
          console.error(`❌ Error scanning user ${email}:`, error.message);
          this.scanState.errors.push({
            user: email,
            error: error.message,
            timestamp: new Date().toISOString(),
          });
          this.scanState.failedUsers++;
          completedUsers++;
          this.scanState.completedUsers = completedUsers;

          console.log(
            `⚠️ Failed scan for user: ${email} - Progress: ${completedUsers}/${
              userEmails.length
            } [${new Date().toLocaleTimeString()}]`
          );

          return { success: false, email, error: error.message };
        } finally {
          // Always release semaphore
          console.log(
            `🔓 ${email}: Releasing semaphore. Active will be: ${
              semaphore.currentConcurrency - 1
            }/${semaphore.maxConcurrency}`
          );
          release();
        }
      });

      // Wait for ALL users to complete (true parallel processing)
      console.log(
        `⏳ Waiting for all ${userEmails.length} users to complete...`
      );
      const allResults = await Promise.allSettled(allUserPromises);

      // Process results
      console.log(`📊 Processing results from ${allResults.length} users...`);
      for (const promiseResult of allResults) {
        if (
          promiseResult.status === "fulfilled" &&
          promiseResult.value.success
        ) {
          const { result } = promiseResult.value;
          results.totalFiles += result.totalFiles || 0;
          results.totalSize += result.totalSize || 0;
        }
      }

      console.log(
        `🎯 All users processed! Total: ${results.totalFiles} files, ${results.totalSize} bytes`
      );

      // Final update scan session with complete results
      await this.updateScanSession(scanSession.id, {
        status: "completed",
        total_files: results.totalFiles,
        total_size: results.totalSize,
        scan_duration: Date.now() - this.scanState.startTime,
        completed_at: new Date().toISOString(),
      });

      this.scanState.isScanning = false;

      console.log(
        `✅ TRUE PARALLEL scan completed: ${results.totalFiles} files found from ${this.scanState.completedUsers}/${userEmails.length} users (${this.scanState.failedUsers} failed)`
      );
      console.log(
        `⏱️ Total scan duration: ${Math.round(
          (Date.now() - this.scanState.startTime) / 1000
        )}s`
      );

      return {
        ...results,
        completedUsers: this.scanState.completedUsers,
        failedUsers: this.scanState.failedUsers,
        totalUsers: userEmails.length,
        errors: this.scanState.errors,
        scanDuration: Date.now() - this.scanState.startTime,
        concurrencyUsed: this.config.maxConcurrentRequests,
      };
    } catch (error) {
      this.scanState.isScanning = false;
      console.error("❌ Error during full scan:", error.message);
      throw new Error(`Drive scan failed: ${error.message}`);
    }
  }

  /**
   * Scan from root directory
   * @param {string} userEmail - User email
   * @param {object} options - Scan options
   * @param {string} sessionId - Scan session ID
   * @returns {Promise<object>} Scan results
   */
  async scanFromRoot(userEmail, options, sessionId) {
    const allFiles = [];
    const folderQueue = [{ id: "root", path: "/", depth: 0 }];
    const processedFolders = new Set();
    let totalFilesDiscovered = 0;

    console.log(`🚀 Starting scanFromRoot for user: ${userEmail}`);
    console.log(
      `📋 Options: maxDepth=${options.maxDepth}, batchSize=${this.config.batchSize}`
    );

    while (folderQueue.length > 0) {
      const currentFolder = folderQueue.shift();

      if (
        processedFolders.has(currentFolder.id) ||
        currentFolder.depth >= options.maxDepth
      ) {
        console.log(
          `⏭️ Skipping folder ${currentFolder.path}: ${
            processedFolders.has(currentFolder.id)
              ? "already processed"
              : "max depth reached"
          }`
        );
        continue;
      }

      processedFolders.add(currentFolder.id);
      this.scanState.currentDepth = Math.max(
        this.scanState.currentDepth,
        currentFolder.depth
      );

      try {
        console.log(
          `📁 Scanning folder: ${currentFolder.path} (depth: ${currentFolder.depth}, queue: ${folderQueue.length} remaining)`
        );

        // Get ALL files in current folder (with pagination)
        const folderFiles = await this.scanFolder(
          userEmail,
          currentFolder,
          options
        );

        console.log(
          `📊 Found ${folderFiles.length} files in folder: ${currentFolder.path}`
        );

        // Process each file
        const existingFileIds = await this.getScannedFileIdsInChunks(
          folderFiles.map((f) => f.id),
          100
        );

        const newFiles = folderFiles.filter(
          (file) => !existingFileIds.includes(file.id)
        );

        console.log(
          `🆕 ${newFiles.length} new files (${
            folderFiles.length - newFiles.length
          } already in database)`
        );

        for (const file of newFiles) {
          // Add path information
          file.fullPath = this.buildFilePath(currentFolder.path, file.name);
          file.depth = currentFolder.depth;
          file.scanSessionId = sessionId;

          // Store file in database
          await this.storeScannedFile(file, sessionId);

          allFiles.push(file);
          totalFilesDiscovered++;

          // If it's a folder, add to queue for scanning
          if (file.mimeType === "application/vnd.google-apps.folder") {
            folderQueue.push({
              id: file.id,
              path: file.fullPath,
              depth: currentFolder.depth + 1,
            });
            console.log(
              `📂 Added subfolder to queue: ${file.fullPath} (depth: ${
                currentFolder.depth + 1
              })`
            );
          }
        }

        this.scanState.scannedFiles += newFiles.length;

        // Update progress
        await this.updateScanProgress(sessionId, {
          scanned_files: this.scanState.scannedFiles,
          current_depth: this.scanState.currentDepth,
          folders_processed: processedFolders.size,
        });

        console.log(
          `✅ Completed folder: ${currentFolder.path} | New files: ${newFiles.length} | Total files: ${totalFilesDiscovered} | Folders in queue: ${folderQueue.length}`
        );
      } catch (error) {
        console.error(
          `❌ Error scanning folder ${currentFolder.path}:`,
          error.message
        );
        this.scanState.errors.push({
          folder: currentFolder.path,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }

      // Small delay to respect rate limits
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log(`🎯 Scan completed for ${userEmail}:`);
    console.log(`   📄 Total files discovered: ${totalFilesDiscovered}`);
    console.log(`   📊 Max depth reached: ${this.scanState.currentDepth}`);

    return {
      totalFiles: allFiles.length,
      totalSize: allFiles.reduce(
        (sum, file) => sum + (parseInt(file.size) || 0),
        0
      ),
      folders: allFiles.filter(
        (f) => f.mimeType === "application/vnd.google-apps.folder"
      ).length,
      documents: allFiles.filter((f) => f.mimeType.includes("google-apps"))
        .length,
      maxDepth: this.scanState.currentDepth,
      errors: this.scanState.errors,
      files: allFiles,
    };
  }

  async getScannedFileIdsInChunks(allFileIds, CHUNK_SIZE) {
    const scannedFileIds = [];

    for (let i = 0; i < allFileIds.length; i += CHUNK_SIZE) {
      const chunk = allFileIds.slice(i, i + CHUNK_SIZE);

      console.log(
        `Processing chunk ${i / CHUNK_SIZE + 1} with ${chunk.length} IDs...`
      );

      const { data, error } = await this.supabase
        .getServiceClient()
        .from("scanned_files")
        .select("file_id")
        .in("file_id", chunk);

      if (error) {
        console.error("Error fetching chunk:", error.message);
        // Xử lý lỗi: có thể throw error hoặc tiếp tục với chunk tiếp theo
        continue;
      }

      if (data) {
        scannedFileIds.push(...data.map((item) => item.file_id));
      }
    }
    return scannedFileIds;
  }

  /**
   * Scan specific folder
   * @param {string} userEmail - User email
   * @param {object} folder - Folder info
   * @param {object} options - Scan options
   * @returns {Promise<Array>} Files in folder
   */
  async scanFolder(userEmail, folder, options) {
    const query =
      folder.id === "root"
        ? `'${folder.id}' in parents and trashed=false`
        : `'${folder.id}' in parents and trashed=false`;

    const baseOptions = {
      q: query,
      fields:
        "nextPageToken, files(id, name, mimeType, size, parents, createdTime, modifiedTime, owners, permissions, webViewLink, iconLink, thumbnailLink, description)",
      supportsAllDrives: true,
      includeItemsFromAllDrives: true,
      pageSize: this.config.batchSize,
    };

    try {
      let allFiles = [];
      let nextPageToken = null;
      let pageCount = 0;

      do {
        const scanOptions = {
          ...baseOptions,
          ...(nextPageToken && { pageToken: nextPageToken }),
        };

        const response = await this.driveAPI.listFiles(userEmail, scanOptions);
        const files = response.files || [];

        allFiles = allFiles.concat(files);
        nextPageToken = response.nextPageToken;
        pageCount++;

        console.log(
          `� Page ${pageCount} - Folder ${folder.path || folder.id}: ${
            files.length
          } files found (Total so far: ${allFiles.length})`
        );

        // Add small delay between pages to respect rate limits
        if (nextPageToken) {
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      } while (nextPageToken);

      // Log final scanning progress
      console.log(
        `📁 ✅ Completed scanning folder ${folder.path || folder.id}: ${
          allFiles.length
        } total files found across ${pageCount} pages`
      );

      // If no files found in root, provide diagnostic info
      if (allFiles.length === 0 && folder.id === "root") {
        console.log(`⚠️ No files found in root folder for ${userEmail}`);
        console.log(`💡 This could mean:`);
        console.log(`   1. User has no files in their Drive`);
        console.log(`   2. All files are in shared drives`);
        console.log(`   3. Domain-wide delegation is not configured correctly`);
        console.log(`   4. User is not in the organization`);
      }

      return allFiles;
    } catch (error) {
      console.error(
        `❌ Error scanning folder ${folder.path || folder.id}:`,
        error.message
      );

      // Provide specific error guidance
      if (error.message.includes("unauthorized_client")) {
        console.log(
          `💡 Fix: Enable domain-wide delegation for service account in Google Admin Console`
        );
      } else if (error.message.includes("insufficient permission")) {
        console.log(
          `💡 Fix: Ensure user ${userEmail} is in the organization and has Drive access`
        );
      }

      throw error;
    }
  }

  /**
   * Scan and retrieve all users from Google Drive domain
   * @returns {Promise<Array>} List of users
   * @throws {Error} If scanning fails
   */
  async scanAllUsers() {
    try {
      const allUsers = await this.driveAPI.getAllUsers();

      console.log(`📁 Found ${allUsers.length} users in domain`);

      if (allUsers.length === 0) {
        console.warn("⚠️ No users found in the domain");
        console.log("💡 Possible reasons:");
        console.log("   1. No users exist in the domain");
        console.log("   2. Domain-wide delegation is not configured correctly");
        console.log("   3. Service account lacks necessary permissions");
        console.log("   4. API access is restricted");
        return [];
      }

      return allUsers;
    } catch (error) {
      console.error("❌ Error scanning all users:", error.message);

      // Provide specific error guidance
      if (error.message.includes("unauthorized_client")) {
        console.log(
          "💡 Fix: Enable domain-wide delegation for service account in Google Admin Console"
        );
      } else if (error.message.includes("insufficient permission")) {
        console.log(
          "💡 Fix: Ensure the service account has the correct scopes and permissions"
        );
      }

      throw new Error(`Failed to scan all users: ${error.message}`);
    }
  }

  /**
   * Build full file path
   * @param {string} parentPath - Parent folder path
   * @param {string} fileName - File name
   * @returns {string} Full path
   */
  buildFilePath(parentPath, fileName) {
    if (parentPath === "/") {
      return `/${fileName}`;
    }
    return `${parentPath}/${fileName}`;
  }

  /**
   * Create scan session in database
   * @param {string[]} userEmails - Array of user emails
   * @param {object} options - Scan options
   * @returns {Promise<object>} Scan session
   */
  async createScanSession(userEmails, options) {
    const { data, error } = await this.supabase
      .getServiceClient()
      .from("scan_sessions")
      .insert({
        user_email: userEmails.join(", "),
        scan_type: "full_drive",
        scan_options: options,
        status: "running",
        started_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create scan session: ${error.message}`);
    }

    return data;
  }

  /**
   * Update scan session
   * @param {string} sessionId - Session ID
   * @param {object} updates - Updates to apply
   */
  async updateScanSession(sessionId, updates) {
    const { error } = await this.supabase
      .getServiceClient()
      .from("scan_sessions")
      .update(updates)
      .eq("id", sessionId);

    if (error) {
      console.error("❌ Error updating scan session:", error.message);
    }
  }

  /**
   * Update scan progress
   * @param {string} sessionId - Session ID
   * @param {object} progress - Progress data
   */
  async updateScanProgress(sessionId, progress) {
    const { error } = await this.supabase
      .getServiceClient()
      .from("scan_sessions")
      .update({
        ...progress,
        updated_at: new Date().toISOString(),
      })
      .eq("id", sessionId);

    if (error) {
      console.error("❌ Error updating scan progress:", error.message);
    }
  }

  /**
   * Store scanned file in database
   * @param {object} file - File data
   * @param {string} sessionId - Session ID
   */
  async storeScannedFile(file, sessionId) {
    const { error } = await this.supabase
      .getServiceClient()
      .from("scanned_files")
      .insert({
        scan_session_id: sessionId,
        file_id: file.id,
        name: file.name,
        mime_type: file.mimeType,
        size: parseInt(file.size) || 0,
        full_path: file.fullPath,
        depth: file.depth,
        parents: file.parents,
        created_time: file.createdTime,
        modified_time: file.modifiedTime,
        owners: file.owners,
        permissions: file.permissions,
        web_view_link: file.webViewLink,
        metadata: {
          iconLink: file.iconLink,
          thumbnailLink: file.thumbnailLink,
          description: file.description,
        },
        user_email: file.owners[0].emailAddress,
        domain: process.env.GOOGLE_DOMAIN,
      });

    if (error) {
      console.error("❌ Error storing scanned file:", error.message);
      console.error(file.owners[0].emailAddress);
    }
  }

  /**
   * Get scan status
   * @returns {object} Current scan state
   */
  getScanStatus() {
    return {
      ...this.scanState,
      duration: this.scanState.startTime
        ? Date.now() - this.scanState.startTime
        : 0,
    };
  }

  /**
   * Stop current scan
   */
  stopScan() {
    this.scanState.isScanning = false;
    console.log("🛑 Drive scan stopped by user");
  }

  /**
   * Test method to verify true parallel execution
   * This will show if multiple users are REALLY being processed simultaneously
   */
  async testConcurrency(userEmails) {
    console.log(
      "🧪 TESTING TRUE CONCURRENCY - This will prove parallel execution!"
    );
    console.log("=" * 80);

    const semaphore = new Semaphore(this.config.maxConcurrentRequests);
    const startTimes = new Map();
    const endTimes = new Map();
    const activeUsers = new Set();

    const testPromises = userEmails.map(async (email, index) => {
      const release = await semaphore.acquire();

      try {
        startTimes.set(email, Date.now());
        activeUsers.add(email);

        console.log(
          `🚀 [${new Date().toISOString()}] STARTED: ${email} (Active users: ${Array.from(
            activeUsers
          ).join(", ")})`
        );

        // Simulate work with different durations to see overlap
        const workDuration = 2000 + index * 500; // 2s, 2.5s, 3s, etc.
        await new Promise((resolve) => setTimeout(resolve, workDuration));

        endTimes.set(email, Date.now());
        activeUsers.delete(email);

        console.log(
          `✅ [${new Date().toISOString()}] FINISHED: ${email} after ${workDuration}ms (Still active: ${
            Array.from(activeUsers).join(", ") || "none"
          })`
        );

        return { email, duration: workDuration };
      } finally {
        release();
      }
    });

    console.log(`⏳ Waiting for all ${userEmails.length} test promises...`);
    const results = await Promise.allSettled(testPromises);

    console.log("\n📊 CONCURRENCY TEST RESULTS:");
    console.log("=" * 50);

    // Analyze overlapping execution
    const timeline = [];
    for (const [email, startTime] of startTimes) {
      const endTime = endTimes.get(email);
      timeline.push({ email, start: startTime, end: endTime });
    }

    // Sort by start time
    timeline.sort((a, b) => a.start - b.start);

    let maxConcurrent = 0;
    let currentConcurrent = 0;
    const events = [];

    // Create start/end events
    for (const item of timeline) {
      events.push({ time: item.start, type: "start", email: item.email });
      events.push({ time: item.end, type: "end", email: item.email });
    }

    // Sort events by time
    events.sort((a, b) => a.time - b.time);

    // Calculate max concurrency
    for (const event of events) {
      if (event.type === "start") {
        currentConcurrent++;
        maxConcurrent = Math.max(maxConcurrent, currentConcurrent);
      } else {
        currentConcurrent--;
      }
    }

    console.log(`📈 Maximum concurrent executions: ${maxConcurrent}`);
    console.log(
      `🎯 Expected max concurrent: ${Math.min(
        this.config.maxConcurrentRequests,
        userEmails.length
      )}`
    );
    console.log(`🏆 TRUE PARALLEL: ${maxConcurrent > 1 ? "YES ✅" : "NO ❌"}`);

    if (maxConcurrent > 1) {
      console.log(
        "🎉 CONFIRMED: Multiple users are being processed SIMULTANEOUSLY!"
      );
    } else {
      console.log(
        "⚠️ WARNING: Processing appears to be sequential, not parallel!"
      );
    }

    return {
      maxConcurrentAchieved: maxConcurrent,
      expectedConcurrent: Math.min(
        this.config.maxConcurrentRequests,
        userEmails.length
      ),
      isTrueParallel: maxConcurrent > 1,
      timeline,
    };
  }
}

// Export singleton instance
export const driveScanner = new DriveScanner();
