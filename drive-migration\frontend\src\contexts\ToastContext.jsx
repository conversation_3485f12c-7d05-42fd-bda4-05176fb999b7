import React, { createContext, useContext } from 'react';
import { useToast as useToastHook, ToastContainer } from '../components/Toast';

const ToastContext = createContext();

export const ToastProvider = ({ children }) => {
    const toastMethods = useToastHook();

    return (
        <ToastContext.Provider value={toastMethods}>
            {children}
            <ToastContainer 
                toasts={toastMethods.toasts} 
                onRemoveToast={toastMethods.removeToast} 
            />
        </ToastContext.Provider>
    );
};

export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};
