import { supabaseClient } from './supabase.js';

/**
 * Migration Service
 * High-level service để quản lý migration operations
 */
export class MigrationService {
    constructor() {
        this.db = supabaseClient;
    }

    /**
     * Khởi tạo migration task mới
     */
    async initializeMigrationTask(ownerEmail, scope, path = null, config = {}) {
        try {
            // Tạo task
            const taskResult = await this.db.createMigrationTask({
                owner_email: ownerEmail,
                scope,
                path,
                config: {
                    max_retries: 3,
                    batch_size: 10,
                    ...config
                }
            });

            if (!taskResult.success) {
                throw new Error(taskResult.error);
            }

            const task = taskResult.data;

            // Log khởi tạo
            await this.db.createLog({
                task_id: task.id,
                level: 'info',
                message: `Migration task initialized for ${ownerEmail}`,
                details: { scope, path, config }
            });

            return {
                success: true,
                task
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Bắt đầu migration task
     */
    async startMigrationTask(taskId) {
        try {
            const result = await this.db.updateMigrationTask(taskId, {
                status: 'running',
                started_at: new Date().toISOString()
            });

            if (!result.success) {
                throw new Error(result.error);
            }

            await this.db.createLog({
                task_id: taskId,
                level: 'info',
                message: 'Migration task started'
            });

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Hoàn thành migration task
     */
    async completeMigrationTask(taskId, stats = {}) {
        try {
            const result = await this.db.updateMigrationTask(taskId, {
                status: 'completed',
                finished_at: new Date().toISOString(),
                progress: 100,
                ...stats
            });

            if (!result.success) {
                throw new Error(result.error);
            }

            await this.db.createLog({
                task_id: taskId,
                level: 'info',
                message: 'Migration task completed',
                details: stats
            });

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Đánh dấu migration task thất bại
     */
    async failMigrationTask(taskId, errorMessage) {
        try {
            const result = await this.db.updateMigrationTask(taskId, {
                status: 'failed',
                finished_at: new Date().toISOString(),
                error_message: errorMessage
            });

            if (!result.success) {
                throw new Error(result.error);
            }

            await this.db.createLog({
                task_id: taskId,
                level: 'error',
                message: 'Migration task failed',
                details: { error: errorMessage }
            });

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cập nhật tiến trình migration task
     */
    async updateTaskProgress(taskId, progress, stats = {}) {
        try {
            const updates = {
                progress: Math.min(100, Math.max(0, progress)),
                ...stats
            };

            const result = await this.db.updateMigrationTask(taskId, updates);

            if (!result.success) {
                throw new Error(result.error);
            }

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Thêm file vào migration queue
     */
    async addFileToMigration(taskId, fileInfo) {
        try {
            const itemData = {
                task_id: taskId,
                src_file_id: fileInfo.id,
                src_file_name: fileInfo.name,
                src_file_path: fileInfo.path || fileInfo.name,
                src_parent_id: fileInfo.parents?.[0] || null,
                file_type: fileInfo.mimeType,
                size: parseInt(fileInfo.size) || 0,
                status: 'pending',
                metadata: {
                    webViewLink: fileInfo.webViewLink,
                    modifiedTime: fileInfo.modifiedTime,
                    createdTime: fileInfo.createdTime,
                    owners: fileInfo.owners,
                    permissions: fileInfo.permissions
                }
            };

            const result = await this.db.createMigrationItem(itemData);

            if (!result.success) {
                throw new Error(result.error);
            }

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cập nhật trạng thái file migration
     */
    async updateFileStatus(itemId, status, updates = {}) {
        try {
            const updateData = {
                status,
                ...updates
            };

            if (status === 'downloading') {
                updateData.started_at = new Date().toISOString();
            } else if (status === 'completed') {
                updateData.completed_at = new Date().toISOString();
            }

            const result = await this.db.updateMigrationItem(itemId, updateData);

            if (!result.success) {
                throw new Error(result.error);
            }

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Đánh dấu file migration thất bại
     */
    async markFileAsFailed(itemId, errorCode, errorMessage, shouldRetry = true) {
        try {
            const item = await this.db.getMigrationItem(itemId);
            if (!item.success) {
                throw new Error(item.error);
            }

            const currentRetries = item.data.retries || 0;
            const maxRetries = item.data.max_retries || 3;

            const updates = {
                error_code: errorCode,
                error_message: errorMessage,
                retries: currentRetries + 1
            };

            if (!shouldRetry || currentRetries >= maxRetries) {
                updates.status = 'failed';
            } else {
                updates.status = 'pending'; // Retry
            }

            const result = await this.db.updateMigrationItem(itemId, updates);

            if (!result.success) {
                throw new Error(result.error);
            }

            // Log lỗi
            await this.db.createLog({
                task_id: item.data.task_id,
                item_id: itemId,
                level: 'error',
                message: `File migration failed: ${errorMessage}`,
                details: {
                    error_code: errorCode,
                    retries: currentRetries + 1,
                    will_retry: shouldRetry && currentRetries < maxRetries
                }
            });

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Lấy thống kê migration task
     */
    async getTaskStatistics(taskId) {
        try {
            const taskResult = await this.db.getMigrationTask(taskId);
            if (!taskResult.success) {
                throw new Error(taskResult.error);
            }

            const itemsResult = await this.db.getMigrationItems(taskId);
            if (!itemsResult.success) {
                throw new Error(itemsResult.error);
            }

            const items = itemsResult.data;
            const stats = {
                total_files: items.length,
                completed_files: items.filter(item => item.status === 'completed').length,
                failed_files: items.filter(item => item.status === 'failed').length,
                pending_files: items.filter(item => item.status === 'pending').length,
                in_progress_files: items.filter(item => ['downloading', 'uploading'].includes(item.status)).length,
                total_size: items.reduce((sum, item) => sum + (item.size || 0), 0),
                transferred_size: items
                    .filter(item => item.status === 'completed')
                    .reduce((sum, item) => sum + (item.size || 0), 0)
            };

            // Tính progress
            if (stats.total_files > 0) {
                stats.progress = Math.round((stats.completed_files / stats.total_files) * 100);
            } else {
                stats.progress = 0;
            }

            return {
                success: true,
                data: {
                    task: taskResult.data,
                    statistics: stats,
                    items: items
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Lấy danh sách files cần retry
     */
    async getFilesForRetry(taskId) {
        try {
            const result = await this.db.getMigrationItems(taskId, 'pending');
            
            if (!result.success) {
                throw new Error(result.error);
            }

            // Lọc những file có retries > 0 (đã fail trước đó)
            const retryFiles = result.data.filter(item => (item.retries || 0) > 0);

            return {
                success: true,
                data: retryFiles
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cleanup completed tasks (xóa data cũ)
     */
    async cleanupCompletedTasks(olderThanDays = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

            // Xóa logs cũ trước
            await this.db.serviceClient
                .from('migration_logs')
                .delete()
                .lt('created_at', cutoffDate.toISOString());

            // Xóa tasks completed cũ
            const { data, error } = await this.db.serviceClient
                .from('migration_tasks')
                .delete()
                .eq('status', 'completed')
                .lt('finished_at', cutoffDate.toISOString());

            if (error) throw error;

            return {
                success: true,
                message: `Cleaned up tasks older than ${olderThanDays} days`,
                deleted_count: data?.length || 0
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Export singleton instance
export const migrationService = new MigrationService();
