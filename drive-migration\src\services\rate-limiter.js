/**
 * Rate Limiter Service - Quản lý giới hạn API calls
 * Tôn trọng rate limits của Google Drive và Lark APIs
 */

import { supabaseClient } from '../database/supabase.js';

class RateLimiter {
    constructor() {
        // Google Drive API limits
        this.googleLimits = {
            queriesPerSecond: 100,
            queriesPerMinute: 1000,
            queriesPerDay: 1000000,
            uploadBandwidth: 750 * 1024 * 1024, // 750 MB/s
            downloadBandwidth: 750 * 1024 * 1024 // 750 MB/s
        };

        // Lark API limits
        this.larkLimits = {
            queriesPerSecond: 50,
            queriesPerMinute: 1000,
            uploadBandwidth: 100 * 1024 * 1024, // 100 MB/s
            downloadBandwidth: 100 * 1024 * 1024 // 100 MB/s
        };

        // Rate limiting windows
        this.windows = {
            google: {
                second: { requests: 0, resetTime: 0 },
                minute: { requests: 0, resetTime: 0 },
                day: { requests: 0, resetTime: 0 }
            },
            lark: {
                second: { requests: 0, resetTime: 0 },
                minute: { requests: 0, resetTime: 0 }
            }
        };

        // Request queues
        this.queues = {
            google: [],
            lark: []
        };

        // Processing flags
        this.processing = {
            google: false,
            lark: false
        };

        console.log('🚦 Rate Limiter initialized with API limits:');
        console.log(`   📊 Google Drive: ${this.googleLimits.queriesPerSecond}/s, ${this.googleLimits.queriesPerMinute}/min`);
        console.log(`   📊 Lark: ${this.larkLimits.queriesPerSecond}/s, ${this.larkLimits.queriesPerMinute}/min`);
    }

    /**
     * Check if request can be made immediately
     */
    canMakeRequest(service) {
        const now = Date.now();
        const limits = service === 'google' ? this.googleLimits : this.larkLimits;
        const window = this.windows[service];

        // Reset windows if needed
        this.resetWindowsIfNeeded(service, now);

        // Check per-second limit
        if (window.second.requests >= limits.queriesPerSecond) {
            return false;
        }

        // Check per-minute limit
        if (window.minute.requests >= limits.queriesPerMinute) {
            return false;
        }

        // Check daily limit for Google
        if (service === 'google' && window.day.requests >= limits.queriesPerDay) {
            return false;
        }

        return true;
    }

    /**
     * Reset rate limiting windows if time has passed
     */
    resetWindowsIfNeeded(service, now) {
        const window = this.windows[service];

        // Reset second window
        if (now >= window.second.resetTime) {
            window.second.requests = 0;
            window.second.resetTime = now + 1000;
        }

        // Reset minute window
        if (now >= window.minute.resetTime) {
            window.minute.requests = 0;
            window.minute.resetTime = now + 60000;
        }

        // Reset day window for Google
        if (service === 'google' && now >= window.day.resetTime) {
            window.day.requests = 0;
            window.day.resetTime = now + 86400000; // 24 hours
        }
    }

    /**
     * Record a request
     */
    recordRequest(service) {
        const window = this.windows[service];
        window.second.requests++;
        window.minute.requests++;
        
        if (service === 'google') {
            window.day.requests++;
        }
    }

    /**
     * Make rate-limited request
     */
    async makeRequest(service, requestFunction, priority = 'normal') {
        return new Promise((resolve, reject) => {
            const request = {
                service,
                function: requestFunction,
                priority,
                resolve,
                reject,
                timestamp: Date.now()
            };

            // Add to appropriate queue
            this.queues[service].push(request);
            
            // Sort queue by priority
            this.sortQueue(service);
            
            // Start processing if not already running
            if (!this.processing[service]) {
                this.processQueue(service);
            }
        });
    }

    /**
     * Sort queue by priority
     */
    sortQueue(service) {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        
        this.queues[service].sort((a, b) => {
            const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
            if (priorityDiff !== 0) return priorityDiff;
            
            // If same priority, sort by timestamp (FIFO)
            return a.timestamp - b.timestamp;
        });
    }

    /**
     * Process request queue
     */
    async processQueue(service) {
        this.processing[service] = true;
        
        while (this.queues[service].length > 0) {
            const request = this.queues[service].shift();
            
            try {
                // Wait if rate limit exceeded
                await this.waitForRateLimit(service);
                
                // Record the request
                this.recordRequest(service);
                
                // Execute the request
                const result = await request.function();
                request.resolve(result);
                
            } catch (error) {
                // Handle rate limit errors
                if (this.isRateLimitError(error)) {
                    console.log(`⏳ Rate limit hit for ${service}, backing off...`);
                    
                    // Put request back in queue
                    this.queues[service].unshift(request);
                    
                    // Wait before retrying
                    await this.backoff(service, error);
                    continue;
                }
                
                request.reject(error);
            }
        }
        
        this.processing[service] = false;
    }

    /**
     * Wait for rate limit to allow request
     */
    async waitForRateLimit(service) {
        while (!this.canMakeRequest(service)) {
            const waitTime = this.calculateWaitTime(service);
            console.log(`⏳ Rate limit reached for ${service}, waiting ${waitTime}ms`);
            await this.sleep(waitTime);
        }
    }

    /**
     * Calculate wait time until next request can be made
     */
    calculateWaitTime(service) {
        const now = Date.now();
        const window = this.windows[service];
        
        // Find the earliest reset time
        const resetTimes = [
            window.second.resetTime,
            window.minute.resetTime
        ];
        
        if (service === 'google') {
            resetTimes.push(window.day.resetTime);
        }
        
        const nextReset = Math.min(...resetTimes.filter(time => time > now));
        return Math.max(100, nextReset - now); // Minimum 100ms wait
    }

    /**
     * Check if error is rate limit related
     */
    isRateLimitError(error) {
        const rateLimitIndicators = [
            'rate limit',
            'quota exceeded',
            'too many requests',
            'throttled',
            '429',
            'RATE_LIMIT_EXCEEDED'
        ];
        
        const errorMessage = error.message?.toLowerCase() || '';
        const errorCode = error.code?.toString() || '';
        
        return rateLimitIndicators.some(indicator => 
            errorMessage.includes(indicator) || errorCode.includes(indicator)
        );
    }

    /**
     * Exponential backoff for rate limit errors
     */
    async backoff(service, error) {
        // Extract retry-after header if available
        let retryAfter = this.extractRetryAfter(error);
        
        if (!retryAfter) {
            // Default exponential backoff
            const baseDelay = service === 'google' ? 1000 : 2000;
            const jitter = Math.random() * 1000;
            retryAfter = baseDelay + jitter;
        }
        
        console.log(`⏳ Backing off for ${service}: ${retryAfter}ms`);
        await this.sleep(retryAfter);
    }

    /**
     * Extract retry-after value from error
     */
    extractRetryAfter(error) {
        // Check for Retry-After header
        if (error.response?.headers?.['retry-after']) {
            const retryAfter = parseInt(error.response.headers['retry-after']);
            return retryAfter * 1000; // Convert seconds to milliseconds
        }
        
        // Check for rate limit reset time in error message
        const resetMatch = error.message?.match(/reset.*?(\d+)/i);
        if (resetMatch) {
            const resetTime = parseInt(resetMatch[1]);
            return Math.max(1000, resetTime - Date.now());
        }
        
        return null;
    }

    /**
     * Sleep utility
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get rate limiter statistics
     */
    getStats() {
        const now = Date.now();
        
        return {
            google: {
                limits: this.googleLimits,
                current: {
                    second: this.windows.google.second.requests,
                    minute: this.windows.google.minute.requests,
                    day: this.windows.google.day.requests
                },
                queue: {
                    length: this.queues.google.length,
                    processing: this.processing.google
                },
                nextReset: {
                    second: Math.max(0, this.windows.google.second.resetTime - now),
                    minute: Math.max(0, this.windows.google.minute.resetTime - now),
                    day: Math.max(0, this.windows.google.day.resetTime - now)
                }
            },
            lark: {
                limits: this.larkLimits,
                current: {
                    second: this.windows.lark.second.requests,
                    minute: this.windows.lark.minute.requests
                },
                queue: {
                    length: this.queues.lark.length,
                    processing: this.processing.lark
                },
                nextReset: {
                    second: Math.max(0, this.windows.lark.second.resetTime - now),
                    minute: Math.max(0, this.windows.lark.minute.resetTime - now)
                }
            }
        };
    }

    /**
     * Adjust rate limits based on observed API behavior
     */
    adjustLimits(service, newLimits) {
        if (service === 'google') {
            this.googleLimits = { ...this.googleLimits, ...newLimits };
        } else if (service === 'lark') {
            this.larkLimits = { ...this.larkLimits, ...newLimits };
        }
        
        console.log(`🔧 Adjusted rate limits for ${service}:`, newLimits);
    }

    /**
     * Clear all queues (emergency stop)
     */
    clearQueues() {
        this.queues.google.forEach(req => req.reject(new Error('Queue cleared')));
        this.queues.lark.forEach(req => req.reject(new Error('Queue cleared')));
        
        this.queues.google = [];
        this.queues.lark = [];
        
        console.log('🛑 All request queues cleared');
    }

    /**
     * Get queue status
     */
    getQueueStatus() {
        return {
            google: {
                length: this.queues.google.length,
                processing: this.processing.google,
                oldestRequest: this.queues.google.length > 0 ? 
                    Date.now() - this.queues.google[0].timestamp : 0
            },
            lark: {
                length: this.queues.lark.length,
                processing: this.processing.lark,
                oldestRequest: this.queues.lark.length > 0 ? 
                    Date.now() - this.queues.lark[0].timestamp : 0
            }
        };
    }

    /**
     * Estimate wait time for new request
     */
    estimateWaitTime(service, priority = 'normal') {
        const queueLength = this.queues[service].length;
        const limits = service === 'google' ? this.googleLimits : this.larkLimits;
        
        // Estimate based on current queue and rate limits
        const requestsPerSecond = limits.queriesPerSecond * 0.8; // Conservative estimate
        const estimatedWait = queueLength / requestsPerSecond * 1000;
        
        return Math.max(0, estimatedWait);
    }
}

// Export singleton instance
export default new RateLimiter();
