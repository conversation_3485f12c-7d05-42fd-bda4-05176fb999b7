import { supabaseClient } from './database/supabase.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Kiểm tra database setup trên Supabase server
 */
async function checkDatabaseSetup() {
    console.log('🔍 Checking Supabase Database Setup...\n');
    console.log('=' .repeat(60));

    try {
        const client = supabaseClient.getServiceClient();
        
        // 1. Kiểm tra connection
        console.log('\n1. Testing database connection...');
        const { data: connectionTest, error: connectionError } = await client
            .from('migration_tasks')
            .select('*')
            .limit(1);
            
        if (connectionError) {
            console.log('❌ Database connection failed:', connectionError.message);
            
            // Nếu bảng không tồn tại, thử tạo schema
            if (connectionError.message.includes('does not exist') || 
                connectionError.message.includes('relation') ||
                connectionError.code === '42P01') {
                console.log('\n🔧 Tables do not exist. Creating database schema...');
                await createDatabaseSchema();
                return;
            }
            
            return;
        }
        
        console.log('✅ Database connection successful');

        // 2. <PERSON><PERSON><PERSON> tra tất cả tables
        console.log('\n2. Checking all required tables...');
        const requiredTables = [
            'users',
            'scan_sessions',
            'scanned_files',
            'migration_tasks', 
            'migration_items',
            'permission_mappings',
            'migration_logs'
        ];

        const tableResults = {};
        
        for (const table of requiredTables) {
            try {
                const { data, error } = await client
                    .from(table)
                    .select('*')
                    .limit(1);
                    
                if (error) {
                    console.log(`❌ Table ${table}: ${error.message}`);
                    tableResults[table] = false;
                } else {
                    console.log(`✅ Table ${table}: OK`);
                    tableResults[table] = true;
                }
            } catch (err) {
                console.log(`❌ Table ${table}: ${err.message}`);
                tableResults[table] = false;
            }
        }

        // 3. Kiểm tra view
        console.log('\n3. Checking views...');
        try {
            const { data: viewData, error: viewError } = await client
                .from('migration_task_stats')
                .select('*')
                .limit(1);
                
            if (viewError) {
                console.log(`❌ View migration_task_stats: ${viewError.message}`);
            } else {
                console.log('✅ View migration_task_stats: OK');
            }
        } catch (err) {
            console.log(`❌ View migration_task_stats: ${err.message}`);
        }

        // 4. Kiểm tra functions
        console.log('\n4. Checking functions...');
        try {
            const { data: funcData, error: funcError } = await client
                .rpc('update_updated_at_column');
                
            // Function sẽ fail vì không có context, nhưng nếu tồn tại thì error sẽ khác
            if (funcError && !funcError.message.includes('NEW')) {
                console.log(`❌ Function update_updated_at_column: ${funcError.message}`);
            } else {
                console.log('✅ Function update_updated_at_column: OK');
            }
        } catch (err) {
            console.log(`❌ Function update_updated_at_column: ${err.message}`);
        }

        // 5. Tổng kết
        const missingTables = Object.entries(tableResults)
            .filter(([table, exists]) => !exists)
            .map(([table]) => table);
            
        if (missingTables.length > 0) {
            console.log('\n❌ Missing tables:', missingTables.join(', '));
            console.log('\n🔧 Creating missing database schema...');
            await createDatabaseSchema();
        } else {
            console.log('\n✅ All database components are properly set up!');
        }

    } catch (error) {
        console.error('❌ Database setup check failed:', error);
        
        // Nếu có lỗi connection, thử tạo schema
        console.log('\n🔧 Attempting to create database schema...');
        await createDatabaseSchema();
    }
}

/**
 * Tạo database schema trên Supabase
 */
async function createDatabaseSchema() {
    console.log('\n🔧 Creating database schema on Supabase...');
    
    try {
        // Đọc schema file
        const fs = await import('fs');
        const path = await import('path');
        const { fileURLToPath } = await import('url');
        
        const __filename = fileURLToPath(import.meta.url);
        const __dirname = path.dirname(__filename);
        const schemaPath = path.join(__dirname, '../database/schema.sql');
        
        if (!fs.existsSync(schemaPath)) {
            console.log('❌ Schema file not found:', schemaPath);
            return;
        }
        
        const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
        
        // Tách schema thành các statements riêng biệt
        const statements = schemaSQL
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📝 Found ${statements.length} SQL statements to execute`);
        
        // Execute từng statement
        const client = supabaseClient.getServiceClient();
        let successCount = 0;
        let errorCount = 0;
        
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            try {
                console.log(`\n${i + 1}/${statements.length}: Executing statement...`);
                
                // Sử dụng rpc để execute raw SQL
                const { data, error } = await client.rpc('exec_sql', { 
                    sql_query: statement + ';' 
                });
                
                if (error) {
                    // Một số lỗi có thể bỏ qua (như table already exists)
                    if (error.message.includes('already exists') || 
                        error.message.includes('does not exist')) {
                        console.log(`⚠️ Warning: ${error.message}`);
                    } else {
                        console.log(`❌ Error: ${error.message}`);
                        errorCount++;
                    }
                } else {
                    console.log('✅ Success');
                    successCount++;
                }
                
            } catch (err) {
                console.log(`❌ Exception: ${err.message}`);
                errorCount++;
            }
        }
        
        console.log(`\n📊 Schema creation summary:`);
        console.log(`   ✅ Successful: ${successCount}`);
        console.log(`   ❌ Errors: ${errorCount}`);
        
        if (errorCount === 0) {
            console.log('\n🎉 Database schema created successfully!');
        } else {
            console.log('\n⚠️ Schema creation completed with some errors. Manual intervention may be required.');
        }
        
    } catch (error) {
        console.error('❌ Failed to create database schema:', error);
        console.log('\n💡 Manual steps required:');
        console.log('1. Go to Supabase Dashboard > SQL Editor');
        console.log('2. Copy content from database/schema.sql');
        console.log('3. Execute the SQL manually');
    }
}

// Run check if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    checkDatabaseSetup().catch(error => {
        console.error('❌ Check failed:', error);
        process.exit(1);
    });
}

export { checkDatabaseSetup, createDatabaseSchema };
