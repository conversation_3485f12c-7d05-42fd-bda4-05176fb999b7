import React, { useState, useEffect, useRef } from 'react';
import { apiGet } from '../utils/apiUtils';
import './UploadProgress.css';

/**
 * Upload Progress Component
 * <PERSON> <PERSON><PERSON><PERSON> t<PERSON> t<PERSON> upload real-time với:
 * - Overall progress và file-by-file progress
 * - Control buttons (pause/resume/cancel)
 * - Performance metrics và ETA
 * - Recent files list và error notifications
 */
const UploadProgress = ({ session, onPause, onResume, onCancel, onCompleted, onBack }) => {
    const [currentSession, setCurrentSession] = useState(session);
    const [uploadItems, setUploadItems] = useState([]);
    const [recentFiles, setRecentFiles] = useState([]);
    const [errors, setErrors] = useState([]);
    const [metrics, setMetrics] = useState({
        speed: 0,
        eta: null,
        throughput: 0
    });
    const [isConnected, setIsConnected] = useState(true);
    
    const intervalRef = useRef(null);
    const startTimeRef = useRef(Date.now());

    useEffect(() => {
        startTimeRef.current = Date.now();
        startPolling();
        
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [session.id]);

    /**
     * Start polling for session status
     */
    const startPolling = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
        }

        intervalRef.current = setInterval(async () => {
            try {
                await updateSessionStatus();
                await updateUploadItems();
                setIsConnected(true);
            } catch (error) {
                console.error('Error polling upload status:', error);
                setIsConnected(false);
            }
        }, 2000); // Poll every 2 seconds
    };

    /**
     * Update session status
     */
    const updateSessionStatus = async () => {
        try {
            const sessionStatus = await apiGet(`/api/upload/sessions/${session.id}/status`);
            setCurrentSession(sessionStatus);

            // Calculate metrics
            calculateMetrics(sessionStatus);

            // Check if completed
            if (sessionStatus.status === 'completed' || sessionStatus.status === 'failed' || sessionStatus.status === 'cancelled') {
                if (intervalRef.current) {
                    clearInterval(intervalRef.current);
                }
                onCompleted(sessionStatus);
            }
        } catch (error) {
            console.error('Error updating session status:', error);
        }
    };

    /**
     * Update upload items
     */
    const updateUploadItems = async () => {
        try {
            const response = await apiGet(`/api/upload/sessions/${session.id}/items?limit=20&status=uploaded,failed`);
            const items = response.items || [];
            
            setUploadItems(items);
            
            // Update recent files (last 10 completed)
            const recent = items
                .filter(item => item.status === 'uploaded' || item.status === 'failed')
                .sort((a, b) => new Date(b.upload_completed_at) - new Date(a.upload_completed_at))
                .slice(0, 10);
            
            setRecentFiles(recent);

            // Update errors
            const errorItems = items.filter(item => item.status === 'failed');
            setErrors(errorItems);
        } catch (error) {
            console.error('Error updating upload items:', error);
        }
    };

    /**
     * Calculate performance metrics
     */
    const calculateMetrics = (sessionData) => {
        const now = Date.now();
        const elapsed = (now - startTimeRef.current) / 1000; // seconds
        
        if (elapsed > 0 && sessionData.uploaded_size > 0) {
            const speed = sessionData.uploaded_size / elapsed; // bytes per second
            const remaining = sessionData.total_size - sessionData.uploaded_size;
            const eta = speed > 0 ? remaining / speed : null;
            const throughput = sessionData.uploaded_files / elapsed; // files per second

            setMetrics({
                speed: speed,
                eta: eta,
                throughput: throughput
            });
        }
    };

    /**
     * Format file size
     */
    const formatFileSize = (bytes) => {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    /**
     * Format speed
     */
    const formatSpeed = (bytesPerSecond) => {
        return formatFileSize(bytesPerSecond) + '/s';
    };

    /**
     * Format ETA
     */
    const formatETA = (seconds) => {
        if (!seconds || seconds === Infinity) return 'N/A';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    /**
     * Get status icon
     */
    const getStatusIcon = (status) => {
        switch (status) {
            case 'uploaded': return '✅';
            case 'failed': return '❌';
            case 'uploading': return '⏳';
            case 'pending': return '⏸️';
            case 'skipped': return '⏭️';
            default: return '❓';
        }
    };

    /**
     * Get status color
     */
    const getStatusColor = (status) => {
        switch (status) {
            case 'running': return '#3b82f6';
            case 'completed': return '#10b981';
            case 'failed': return '#ef4444';
            case 'cancelled': return '#6b7280';
            case 'paused': return '#f59e0b';
            default: return '#6b7280';
        }
    };

    const progressPercentage = currentSession.total_files > 0 
        ? Math.round((currentSession.uploaded_files / currentSession.total_files) * 100)
        : 0;

    const sizeProgressPercentage = currentSession.total_size > 0
        ? Math.round((currentSession.uploaded_size / currentSession.total_size) * 100)
        : 0;

    return (
        <div className="upload-progress">
            {/* Header */}
            <div className="progress-header">
                <div className="session-info">
                    <h2>📤 {currentSession.name}</h2>
                    <div className="session-meta">
                        <span className={`status-badge status-${currentSession.status}`}>
                            {currentSession.status.toUpperCase()}
                        </span>
                        <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
                            <div className={`status-dot ${isConnected ? 'green' : 'red'}`}></div>
                            {isConnected ? 'Connected' : 'Disconnected'}
                        </div>
                    </div>
                </div>
                
                <div className="progress-controls">
                    {currentSession.status === 'running' && (
                        <button onClick={onPause} className="btn btn-warning">
                            ⏸️ Tạm dừng
                        </button>
                    )}
                    {currentSession.status === 'paused' && (
                        <button onClick={onResume} className="btn btn-success">
                            ▶️ Tiếp tục
                        </button>
                    )}
                    {(currentSession.status === 'running' || currentSession.status === 'paused') && (
                        <button onClick={onCancel} className="btn btn-danger">
                            🚫 Hủy
                        </button>
                    )}
                    <button onClick={onBack} className="btn btn-secondary">
                        ← Quay lại
                    </button>
                </div>
            </div>

            {/* Overall Progress */}
            <div className="overall-progress">
                <div className="progress-stats">
                    <div className="stat-item">
                        <div className="stat-value">{currentSession.uploaded_files || 0}</div>
                        <div className="stat-label">Đã upload</div>
                    </div>
                    <div className="stat-item">
                        <div className="stat-value">{currentSession.total_files || 0}</div>
                        <div className="stat-label">Tổng files</div>
                    </div>
                    <div className="stat-item">
                        <div className="stat-value">{currentSession.failed_files || 0}</div>
                        <div className="stat-label">Lỗi</div>
                    </div>
                    <div className="stat-item">
                        <div className="stat-value">{formatFileSize(currentSession.uploaded_size || 0)}</div>
                        <div className="stat-label">Đã upload</div>
                    </div>
                </div>

                <div className="progress-bars">
                    <div className="progress-bar-container">
                        <div className="progress-bar-header">
                            <span>Tiến trình files: {progressPercentage}%</span>
                            <span>{currentSession.uploaded_files || 0} / {currentSession.total_files || 0}</span>
                        </div>
                        <div className="progress-bar">
                            <div 
                                className="progress-fill"
                                style={{ 
                                    width: `${progressPercentage}%`,
                                    backgroundColor: getStatusColor(currentSession.status)
                                }}
                            ></div>
                        </div>
                    </div>

                    <div className="progress-bar-container">
                        <div className="progress-bar-header">
                            <span>Tiến trình dung lượng: {sizeProgressPercentage}%</span>
                            <span>{formatFileSize(currentSession.uploaded_size || 0)} / {formatFileSize(currentSession.total_size || 0)}</span>
                        </div>
                        <div className="progress-bar">
                            <div 
                                className="progress-fill"
                                style={{ 
                                    width: `${sizeProgressPercentage}%`,
                                    backgroundColor: getStatusColor(currentSession.status)
                                }}
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Performance Metrics */}
            {currentSession.status === 'running' && (
                <div className="performance-metrics">
                    <div className="metric-item">
                        <div className="metric-label">Tốc độ</div>
                        <div className="metric-value">{formatSpeed(metrics.speed)}</div>
                    </div>
                    <div className="metric-item">
                        <div className="metric-label">ETA</div>
                        <div className="metric-value">{formatETA(metrics.eta)}</div>
                    </div>
                    <div className="metric-item">
                        <div className="metric-label">Throughput</div>
                        <div className="metric-value">{metrics.throughput.toFixed(2)} files/s</div>
                    </div>
                </div>
            )}

            {/* Recent Files */}
            {recentFiles.length > 0 && (
                <div className="recent-files">
                    <h3>📋 Files gần đây</h3>
                    <div className="files-list">
                        {recentFiles.map((file, index) => (
                            <div key={`${file.id}_${index}`} className="file-item">
                                <div className="file-info">
                                    <span className="file-icon">{getStatusIcon(file.status)}</span>
                                    <div className="file-details">
                                        <div className="file-name">{file.file_name}</div>
                                        <div className="file-meta">
                                            {formatFileSize(file.file_size)} • {file.user_email}
                                            {file.upload_duration && (
                                                <span> • {(file.upload_duration / 1000).toFixed(1)}s</span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="file-timestamp">
                                    {new Date(file.upload_completed_at).toLocaleTimeString()}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Errors */}
            {errors.length > 0 && (
                <div className="upload-errors">
                    <h3>❌ Lỗi upload ({errors.length})</h3>
                    <div className="errors-list">
                        {errors.slice(0, 10).map((error, index) => (
                            <div key={`${error.id}_${index}`} className="error-item">
                                <div className="error-info">
                                    <div className="error-file">{error.file_name}</div>
                                    <div className="error-message">{error.error_message}</div>
                                    <div className="error-meta">
                                        {error.user_email} • Retry: {error.retry_count || 0}
                                    </div>
                                </div>
                                <div className="error-timestamp">
                                    {new Date(error.upload_completed_at).toLocaleTimeString()}
                                </div>
                            </div>
                        ))}
                    </div>
                    {errors.length > 10 && (
                        <div className="errors-more">
                            Và {errors.length - 10} lỗi khác...
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default UploadProgress;
