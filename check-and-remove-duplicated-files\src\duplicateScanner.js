const fs = require('fs-extra');
const path = require('path');

class DuplicateScanner {
    constructor() {
        this.duplicateGroups = [];
        this.processedFiles = 0;
        this.totalFiles = 0;
        this.currentDirectory = '';
        this.isPaused = false;
        this.isStopped = false;
        this.pausePromise = null;
        this.pauseResolve = null;
    }

    async scanDirectory(rootPath, progressCallback, maxWorkers = 4) {
        try {
            this.duplicateGroups = [];
            this.processedFiles = 0;
            this.totalFiles = 0;
            this.currentDirectory = '';
            this.progressCallback = progressCallback;
            this.isPaused = false;
            this.isStopped = false;

            // First pass: count total files
            await this.countFiles(rootPath);

            if (progressCallback) {
                progressCallback({
                    phase: 'counting',
                    message: `Found ${this.totalFiles} files to process`,
                    processed: 0,
                    total: this.totalFiles
                });
            }

            // Get all directories for parallel processing
            const allDirectories = await this.getAllDirectories(rootPath);

            if (progressCallback) {
                progressCallback({
                    phase: 'preparing',
                    message: `Found ${allDirectories.length} directories, starting ${maxWorkers} workers`,
                    processed: 0,
                    total: this.totalFiles
                });
            }

            // Process directories in parallel
            await this.processDirectoriesInParallel(allDirectories, maxWorkers);

            return this.duplicateGroups;
        } catch (error) {
            throw new Error(`Error scanning directory: ${error.message}`);
        }
    }

    async countFiles(dirPath) {
        try {
            const items = await fs.readdir(dirPath);

            for (const item of items) {
                const fullPath = path.join(dirPath, item);

                // Skip system directories and protected folders
                if (this.shouldSkipPath(item)) {
                    continue;
                }

                try {
                    const stats = await fs.stat(fullPath);

                    if (stats.isDirectory()) {
                        await this.countFiles(fullPath);
                    } else if (stats.isFile()) {
                        this.totalFiles++;
                    }
                } catch (statError) {
                    console.warn(`Warning: Could not access ${fullPath}: ${statError.message}`);
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not access ${dirPath}: ${error.message}`);
        }
    }

    async scanDirectoryRecursive(dirPath, progressCallback) {
        try {
            this.currentDirectory = dirPath;

            if (progressCallback) {
                progressCallback({
                    phase: 'scanning',
                    message: `Scanning: ${dirPath}`,
                    processed: this.processedFiles,
                    total: this.totalFiles,
                    currentDir: dirPath
                });
            }

            const items = await fs.readdir(dirPath);
            const files = [];
            const subdirectories = [];

            // Separate files and directories
            for (const item of items) {
                const fullPath = path.join(dirPath, item);

                // Skip system directories and protected folders
                if (this.shouldSkipPath(item)) {
                    continue;
                }

                try {
                    const stats = await fs.stat(fullPath);

                    if (stats.isDirectory()) {
                        subdirectories.push(fullPath);
                    } else if (stats.isFile()) {
                        files.push({
                            name: item,
                            path: fullPath,
                            size: stats.size
                        });
                        this.processedFiles++;
                    }
                } catch (statError) {
                    console.warn(`Warning: Could not access ${fullPath}: ${statError.message}`);
                }
            }

            // Check for duplicates in current directory
            await this.findDuplicatesInDirectory(files, progressCallback);

            // Recursively scan subdirectories
            for (const subdir of subdirectories) {
                await this.scanDirectoryRecursive(subdir, progressCallback);
            }

        } catch (error) {
            console.warn(`Warning: Could not scan ${dirPath}: ${error.message}`);
        }
    }

    async findDuplicatesInDirectory(files, workerId) {
        // Group files by potential duplicate patterns
        const potentialDuplicates = new Map();

        // First pass: identify all potential base names
        for (const file of files) {
            const baseName = this.extractBaseName(file.name);
            if (baseName) {
                if (!potentialDuplicates.has(baseName)) {
                    potentialDuplicates.set(baseName, []);
                }
                potentialDuplicates.get(baseName).push(file);
            }
        }

        // Check each group for actual duplicates
        for (const [, fileGroup] of potentialDuplicates) {
            if (fileGroup.length > 1) {
                // Verify this is actually a duplicate pattern
                if (this.isValidDuplicateGroup(fileGroup)) {
                    await this.checkForDuplicates(fileGroup, workerId);
                }
            }
        }
    }

    shouldSkipPath(itemName) {
        // Skip system directories and protected folders
        const systemFolders = [
            'System Volume Information',
            '$RECYCLE.BIN',
            'pagefile.sys',
            'hiberfil.sys',
            'swapfile.sys',
            'DumpStack.log.tmp',
            'Config.Msi'
        ];

        // Check if it's a system folder
        if (systemFolders.some(folder => itemName.toLowerCase().includes(folder.toLowerCase()))) {
            return true;
        }

        // Skip hidden system files/folders (starting with $)
        if (itemName.startsWith('$')) {
            return true;
        }

        return false;
    }

    extractBaseName(fileName) {
        // Extract file name and extension
        const lastDotIndex = fileName.lastIndexOf('.');
        let nameWithoutExt, extension;

        if (lastDotIndex > 0) {
            nameWithoutExt = fileName.substring(0, lastDotIndex);
            extension = fileName.substring(lastDotIndex);
        } else {
            nameWithoutExt = fileName;
            extension = '';
        }

        // Check if this file has a _number suffix
        const numberedMatch = nameWithoutExt.match(/^(.+)_(\d+)$/);
        if (numberedMatch) {
            // This is a numbered file (e.g., "document_1")
            return numberedMatch[1] + extension;
        }

        // This might be the original file, return as potential base name
        return nameWithoutExt + extension;
    }

    isValidDuplicateGroup(fileGroup) {
        // Check if we have a valid duplicate pattern
        // We need either:
        // 1. Multiple files with _number suffix
        // 2. One original file + one or more numbered files

        let hasNumbered = false;

        for (const file of fileGroup) {
            const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.') || file.name.length);

            if (nameWithoutExt.match(/_\d+$/)) {
                hasNumbered = true;
                break; // Found numbered file, that's enough
            }
        }

        // Valid if we have numbered files
        return hasNumbered;
    }

    async getAllDirectories(rootPath) {
        const directories = [rootPath];
        const allDirs = [];

        while (directories.length > 0) {
            const currentDir = directories.shift();
            allDirs.push(currentDir);

            try {
                const items = await fs.readdir(currentDir);

                for (const item of items) {
                    if (this.shouldSkipPath(item)) continue;

                    const fullPath = path.join(currentDir, item);
                    try {
                        const stats = await fs.stat(fullPath);
                        if (stats.isDirectory()) {
                            directories.push(fullPath);
                        }
                    } catch (error) {
                        // Skip inaccessible directories
                    }
                }
            } catch (error) {
                console.warn(`Warning: Could not read directory ${currentDir}: ${error.message}`);
            }
        }

        return allDirs;
    }

    async processDirectoriesInParallel(directories, maxWorkers) {
        const workers = [];
        const directoryQueue = [...directories];

        const processNextDirectory = async (workerId) => {
            while (directoryQueue.length > 0) {
                const directory = directoryQueue.shift();
                if (!directory) break;

                try {
                    await this.scanSingleDirectory(directory, workerId);
                } catch (error) {
                    console.warn(`Worker ${workerId} error processing ${directory}: ${error.message}`);
                }
            }
        };

        // Start workers
        for (let i = 0; i < Math.min(maxWorkers, directories.length); i++) {
            workers.push(processNextDirectory(i + 1));
        }

        // Wait for all workers to complete
        await Promise.all(workers);
    }

    async scanSingleDirectory(dirPath, workerId) {
        try {
            // Check if scan should be paused or stopped
            await this.checkPauseOrStop();

            if (this.progressCallback) {
                this.progressCallback({
                    phase: 'scanning',
                    message: `Worker ${workerId} scanning: ${path.basename(dirPath)}`,
                    processed: this.processedFiles,
                    total: this.totalFiles,
                    currentDir: dirPath,
                    workerId
                });
            }

            const items = await fs.readdir(dirPath);
            const files = [];

            // Collect files in current directory
            for (const item of items) {
                if (this.shouldSkipPath(item)) continue;

                const fullPath = path.join(dirPath, item);
                try {
                    const stats = await fs.stat(fullPath);
                    if (stats.isFile()) {
                        files.push({
                            name: item,
                            path: fullPath,
                            size: stats.size
                        });
                        this.processedFiles++;
                    }
                } catch (error) {
                    // Skip inaccessible files
                }
            }

            // Check for duplicates in current directory
            await this.findDuplicatesInDirectory(files, workerId);

        } catch (error) {
            console.warn(`Warning: Could not scan ${dirPath}: ${error.message}`);
        }
    }

    pauseScan() {
        this.isPaused = true;
        if (!this.pausePromise) {
            this.pausePromise = new Promise(resolve => {
                this.pauseResolve = resolve;
            });
        }

        if (this.progressCallback) {
            this.progressCallback({
                phase: 'paused',
                message: 'Quét đã được tạm dừng',
                processed: this.processedFiles,
                total: this.totalFiles
            });
        }
    }

    resumeScan() {
        this.isPaused = false;
        if (this.pauseResolve) {
            this.pauseResolve();
            this.pausePromise = null;
            this.pauseResolve = null;
        }

        if (this.progressCallback) {
            this.progressCallback({
                phase: 'resumed',
                message: 'Tiếp tục quét...',
                processed: this.processedFiles,
                total: this.totalFiles
            });
        }
    }

    stopScan() {
        this.isStopped = true;
        this.isPaused = false;
        if (this.pauseResolve) {
            this.pauseResolve();
            this.pausePromise = null;
            this.pauseResolve = null;
        }

        if (this.progressCallback) {
            this.progressCallback({
                phase: 'stopped',
                message: 'Quét đã được dừng',
                processed: this.processedFiles,
                total: this.totalFiles
            });
        }
    }

    async checkPauseOrStop() {
        if (this.isStopped) {
            throw new Error('Scan stopped by user');
        }

        if (this.isPaused && this.pausePromise) {
            await this.pausePromise;
        }

        if (this.isStopped) {
            throw new Error('Scan stopped by user');
        }
    }

    getBaseName(fileName) {
        return this.extractBaseName(fileName);
    }

    // Simplified duplicate detection - only by name pattern and size
    // No hash checking to improve performance

    async checkForDuplicates(fileGroup, workerId) {
        // Group by size first
        const sizeGroups = new Map();

        for (const file of fileGroup) {
            if (!sizeGroups.has(file.size)) {
                sizeGroups.set(file.size, []);
            }
            sizeGroups.get(file.size).push(file);
        }

        // Simple duplicate detection based on name pattern and size only
        for (const [size, sameSize] of sizeGroups) {
            if (sameSize.length > 1) {
                await this.checkPauseOrStop(); // Check pause/stop

                if (this.progressCallback) {
                    this.progressCallback({
                        phase: 'checking',
                        message: `Worker ${workerId} checking duplicates: ${sameSize[0].name}`,
                        processed: this.processedFiles,
                        total: this.totalFiles,
                        currentFile: sameSize[0].name,
                        workerId
                    });
                }

                // Consider files with same name pattern and size as duplicates
                this.addDuplicateGroup(sameSize, size, 'name-size-based', workerId);
            }
        }
    }

    addDuplicateGroup(duplicates, size, hash, workerId) {
        const duplicateGroup = {
            hash,
            size,
            directory: path.dirname(duplicates[0].path),
            files: duplicates.map(f => ({
                name: f.name,
                path: f.path,
                size: f.size,
                isOriginal: this.isOriginalFile(f.name, duplicates)
            })),
            detectionMethod: hash === 'name-size-based' ? 'name-size' : 'other'
        };

        this.duplicateGroups.push(duplicateGroup);

        // Send real-time duplicate found notification
        if (this.progressCallback) {
            this.progressCallback({
                phase: 'duplicate_found',
                message: `Found ${duplicates.length} duplicate files (${duplicateGroup.detectionMethod})`,
                duplicateGroup: duplicateGroup,
                workerId
            });
        }
    }

    isOriginalFile(fileName, allFiles) {
        // The original file is the one without _number suffix
        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.') || fileName.length);
        const hasNumberSuffix = nameWithoutExt.match(/_\d+$/);

        if (!hasNumberSuffix) {
            return true; // This is likely the original
        }

        // If all files have number suffix, the one with lowest number is original
        const numbers = allFiles
            .map(f => {
                const nameWithoutExt = f.name.substring(0, f.name.lastIndexOf('.') || f.name.length);
                const match = nameWithoutExt.match(/_(\d+)$/);
                return match ? parseInt(match[1]) : 0;
            })
            .filter(n => n > 0);

        if (numbers.length === allFiles.length) {
            const currentNumber = parseInt(nameWithoutExt.match(/_(\d+)$/)?.[1] || '0');
            return currentNumber === Math.min(...numbers);
        }

        return false;
    }

    // Hash calculation removed for performance - using name pattern and size only
}

module.exports = new DuplicateScanner();
