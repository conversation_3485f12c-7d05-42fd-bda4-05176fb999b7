#!/usr/bin/env node

import dotenv from 'dotenv';
import fs from 'fs';
import { GoogleAuth } from './src/auth/google-auth.js';

// Load environment variables
dotenv.config();

// Override environment variables for testing
const domain = process.env.GOOGLE_DOMAIN || 'osp.com.vn';
const serviceAccountPath = process.env.GOOGLE_SERVICE_ACCOUNT_PATH || './google-service-account.json';
const adminEmail = process.env.GOOGLE_ADMIN_EMAIL || `admin@${domain}`;

// Check if service account file exists
if (!fs.existsSync(serviceAccountPath)) {
    console.log(`⚠️ Service account file not found: ${serviceAccountPath}`);
    console.log('Using default service account file instead.');
    process.env.GOOGLE_SERVICE_ACCOUNT_PATH = './google-service-account.json';
}

console.log('🔧 Domain Configuration Test');
console.log('='.repeat(50));

console.log('\n🔧 Environment Variables:');
console.log(`   GOOGLE_DOMAIN: ${process.env.GOOGLE_DOMAIN || 'not set (using default)'}`);
console.log(`   GOOGLE_SERVICE_ACCOUNT_PATH: ${process.env.GOOGLE_SERVICE_ACCOUNT_PATH || 'not set (using default)'}`);
console.log(`   GOOGLE_ADMIN_EMAIL: ${process.env.GOOGLE_ADMIN_EMAIL || 'not set (using default)'}`);

async function testDomainConfiguration() {
    try {
        // Initialize Google Auth
        const googleAuth = new GoogleAuth();

        console.log('\n📋 Configuration Summary:');
        console.log(`   Domain: ${googleAuth.getDomain()}`);
        console.log(`   Admin Email: ${googleAuth.getAdminEmail()}`);
        console.log(`   Service Account: ${googleAuth.serviceAccountEmail}`);
        console.log(`   Service Account Path: ${googleAuth.keyPath}`);

        // Test connection with admin email
        console.log('\n🔍 Testing connection with admin email...');
        const adminEmail = googleAuth.getAdminEmail();
        const testResult = await googleAuth.testConnection(adminEmail);

        if (testResult.success) {
            console.log('✅ Connection test successful!');
            console.log(`   Connected as: ${testResult.connectedAs}`);
            console.log(`   Domain-wide delegation: ${testResult.domainWideDelegationWorking ? '✅ Working' : '❌ Not working'}`);
            console.log(`   Available permissions: ${testResult.permissions.join(', ')}`);
        } else {
            console.log('❌ Connection test failed!');
            console.log('   Errors:');
            testResult.errors.forEach(error => {
                console.log(`     - ${error}`);
            });
        }

        // Test domain-wide delegation validation
        console.log('\n🔍 Testing domain-wide delegation validation...');
        const validationResult = await googleAuth.validateDomainWideDelegation();

        if (validationResult.success) {
            console.log('✅ Domain-wide delegation validation successful!');
        } else {
            console.log('❌ Domain-wide delegation validation failed!');
            console.log('   Errors:');
            validationResult.errors.forEach(error => {
                console.log(`     - ${error}`);
            });
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
testDomainConfiguration()
    .then(() => {
        console.log('\n✅ Domain configuration test completed!');
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    });
