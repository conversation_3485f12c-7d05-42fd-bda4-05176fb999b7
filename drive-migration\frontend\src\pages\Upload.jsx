import React, { useState, useEffect } from 'react';
import { useToast } from '../contexts/ToastContext';
import { apiGet, apiPost, formatError } from '../utils/apiUtils';
import UploadConfigForm from '../components/UploadConfigForm';
import UploadProgress from '../components/UploadProgress';
import UploadReport from '../components/UploadReport';
import './Upload.css';

/**
 * Upload Page Component
 * Trang chính cho upload files lên Lark Drive
 * Bao gồm: cấu hình session, theo dõi tiến trình, báo cáo kết quả
 */
const Upload = () => {
    const [currentStep, setCurrentStep] = useState('config'); // config, uploading, report
    const [users, setUsers] = useState([]);
    const [sessions, setSessions] = useState([]);
    const [currentSession, setCurrentSession] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const { showSuccess, showError, showWarning } = useToast();

    // Load initial data
    useEffect(() => {
        loadUsers();
        loadSessions();
    }, []);

    /**
     * Load available users (users with downloaded files)
     */
    const loadUsers = async () => {
        try {
            setLoading(true);
            const response = await apiGet('/api/upload/users');
            setUsers(response || []);
        } catch (error) {
            console.error('Error loading users:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tải danh sách users: ${errorInfo.message}`);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Load upload sessions
     */
    const loadSessions = async () => {
        try {
            const response = await apiGet('/api/upload/sessions?limit=20');
            setSessions(response.sessions || []);
        } catch (error) {
            console.error('Error loading sessions:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tải danh sách sessions: ${errorInfo.message}`);
        }
    };

    /**
     * Handle create new upload session
     */
    const handleCreateSession = async (sessionData) => {
        try {
            setLoading(true);
            setError(null);

            console.log('Creating upload session:', sessionData);

            const session = await apiPost('/api/upload/sessions', sessionData);
            
            showSuccess(`Tạo upload session "${session.name}" thành công!`);
            setCurrentSession(session);
            
            // Reload sessions list
            await loadSessions();
            
            return session;
        } catch (error) {
            console.error('Error creating upload session:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi tạo upload session: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
            throw error;
        } finally {
            setLoading(false);
        }
    };

    /**
     * Handle start upload session
     */
    const handleStartSession = async (sessionId) => {
        try {
            setLoading(true);
            setError(null);

            console.log('Starting upload session:', sessionId);

            await apiPost(`/api/upload/sessions/${sessionId}/start`);
            
            showSuccess('Bắt đầu upload thành công!');
            setCurrentStep('uploading');
            
            // Update current session
            const sessionStatus = await apiGet(`/api/upload/sessions/${sessionId}/status`);
            setCurrentSession(sessionStatus);
            
        } catch (error) {
            console.error('Error starting upload session:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi bắt đầu upload: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
        } finally {
            setLoading(false);
        }
    };

    /**
     * Handle pause upload session
     */
    const handlePauseSession = async (sessionId) => {
        try {
            await apiPost(`/api/upload/sessions/${sessionId}/pause`);
            showSuccess('Tạm dừng upload thành công!');
            
            // Update session status
            const sessionStatus = await apiGet(`/api/upload/sessions/${sessionId}/status`);
            setCurrentSession(sessionStatus);
        } catch (error) {
            console.error('Error pausing upload session:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tạm dừng upload: ${errorInfo.message}`);
        }
    };

    /**
     * Handle resume upload session
     */
    const handleResumeSession = async (sessionId) => {
        try {
            await apiPost(`/api/upload/sessions/${sessionId}/resume`);
            showSuccess('Tiếp tục upload thành công!');
            
            // Update session status
            const sessionStatus = await apiGet(`/api/upload/sessions/${sessionId}/status`);
            setCurrentSession(sessionStatus);
        } catch (error) {
            console.error('Error resuming upload session:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tiếp tục upload: ${errorInfo.message}`);
        }
    };

    /**
     * Handle cancel upload session
     */
    const handleCancelSession = async (sessionId) => {
        try {
            await apiPost(`/api/upload/sessions/${sessionId}/cancel`);
            showSuccess('Hủy upload thành công!');
            
            // Update session status
            const sessionStatus = await apiGet(`/api/upload/sessions/${sessionId}/status`);
            setCurrentSession(sessionStatus);
            setCurrentStep('report');
        } catch (error) {
            console.error('Error cancelling upload session:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi hủy upload: ${errorInfo.message}`);
        }
    };

    /**
     * Handle view existing session
     */
    const handleViewSession = async (sessionId) => {
        try {
            setLoading(true);
            const session = await apiGet(`/api/upload/sessions/${sessionId}`);
            setCurrentSession(session);
            
            if (session.status === 'running') {
                setCurrentStep('uploading');
            } else if (session.status === 'completed' || session.status === 'failed' || session.status === 'cancelled') {
                setCurrentStep('report');
            } else {
                setCurrentStep('config');
            }
        } catch (error) {
            console.error('Error loading session:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tải session: ${errorInfo.message}`);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Handle back to config
     */
    const handleBackToConfig = () => {
        setCurrentStep('config');
        setCurrentSession(null);
        setError(null);
    };

    /**
     * Handle upload completed
     */
    const handleUploadCompleted = (session) => {
        setCurrentSession(session);
        setCurrentStep('report');
        showSuccess('Upload hoàn thành!');
    };

    return (
        <div className="upload-page">
            <div className="upload-header">
                <h1>📤 Upload to Lark Drive</h1>
                <p>Upload files từ local storage lên Lark Drive</p>
            </div>

            {/* Step Indicator */}
            <div className="step-indicator">
                <div className={`step ${currentStep === 'config' ? 'active' : currentStep !== 'config' ? 'completed' : ''}`}>
                    1. Cấu hình Upload
                </div>
                <div className={`step ${currentStep === 'uploading' ? 'active' : currentStep === 'report' ? 'completed' : ''}`}>
                    2. Đang Upload
                </div>
                <div className={`step ${currentStep === 'report' ? 'active' : ''}`}>
                    3. Báo cáo
                </div>
            </div>

            {/* Error Display */}
            {error && (
                <div className="error-display">
                    <div className="error-content">
                        <h3>❌ Có lỗi xảy ra</h3>
                        <p>{error.message}</p>
                        <button onClick={() => setError(null)} className="btn btn-secondary">
                            Đóng
                        </button>
                    </div>
                </div>
            )}

            {/* Step Content */}
            <div className="step-content">
                {currentStep === 'config' && (
                    <UploadConfigForm
                        users={users}
                        sessions={sessions}
                        onCreateSession={handleCreateSession}
                        onStartSession={handleStartSession}
                        onViewSession={handleViewSession}
                        loading={loading}
                        currentSession={currentSession}
                    />
                )}

                {currentStep === 'uploading' && currentSession && (
                    <UploadProgress
                        session={currentSession}
                        onPause={() => handlePauseSession(currentSession.id)}
                        onResume={() => handleResumeSession(currentSession.id)}
                        onCancel={() => handleCancelSession(currentSession.id)}
                        onCompleted={handleUploadCompleted}
                        onBack={handleBackToConfig}
                    />
                )}

                {currentStep === 'report' && currentSession && (
                    <UploadReport
                        session={currentSession}
                        onBack={handleBackToConfig}
                        onNewUpload={() => {
                            setCurrentStep('config');
                            setCurrentSession(null);
                        }}
                    />
                )}
            </div>
        </div>
    );
};

export default Upload;
