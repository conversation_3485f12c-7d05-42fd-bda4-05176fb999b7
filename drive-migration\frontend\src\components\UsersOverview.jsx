import React, { useState, useEffect } from 'react';
import './UsersOverview.css';

const mockUsers = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    lastActivity: '2 hours ago',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'User',
    lastActivity: '1 day ago',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'User',
    lastActivity: '5 hours ago',
  },
];

const UsersOverview = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setUsers(mockUsers);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return <div>Loading users...</div>;
  }

  return (
    <div className="users-overview">
      <h2>Users Overview</h2>
      <div className="user-list">
        {users.map((user) => (
          <div key={user.id} className="user-card">
            <div className="user-info">
              <span className="user-name">{user.name}</span>
              <span className="user-email">{user.email}</span>
            </div>
            <div className="user-details">
              <span className="user-role">{user.role}</span>
              <span className="user-last-activity">{user.lastActivity}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UsersOverview;
