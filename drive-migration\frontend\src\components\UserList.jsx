import React from 'react';
import './UserList.css';

function UserList({ users, selectedUser, onUserSelect, loading, viewMode = 'grid' }) {
    if (loading) {
        return (
            <div className="user-list-loading">
                <div className="loading-spinner"></div>
                <p><PERSON><PERSON> tải danh sách người dùng...</p>
            </div>
        );
    }

    if (users.length === 0) {
        return (
            <div className="user-list-empty">
                <div className="empty-icon">👥</div>
                <p><PERSON>h<PERSON>ng tìm thấy người dùng nào</p>
            </div>
        );
    }

    const getUserInitials = (email, name) => {
        if (name) {
            return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
        }
        return email.split('@')[0].slice(0, 2).toUpperCase();
    };

    const getUserDisplayName = (user) => {
        return user.name || user.email.split('@')[0];
    };

    const getUserStats = (user) => {
        return {
            files: user.total_files || 0,
            size: formatFileSize(user.total_size || 0),
            lastActive: user.last_active ? new Date(user.last_active).toLocaleDateString('vi-VN') : 'Chưa rõ'
        };
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    return (
        <div className={`user-list ${viewMode}`}>
            {users.map(user => {
                const isSelected = selectedUser?.email === user.email;
                const stats = getUserStats(user);
                const initials = getUserInitials(user.email, user.name);
                const displayName = getUserDisplayName(user);

                return (
                    <div
                        key={user.email}
                        className={`user-item ${isSelected ? 'selected' : ''}`}
                        onClick={() => onUserSelect(user)}
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                onUserSelect(user);
                            }
                        }}
                    >
                        <div className="user-avatar">
                            {user.avatar ? (
                                <img src={user.avatar} alt={displayName} />
                            ) : (
                                <div className="user-initials">{initials}</div>
                            )}
                            {isSelected && <div className="selection-indicator">✓</div>}
                        </div>

                        <div className="user-info">
                            <div className="user-primary">
                                <div className="user-name" title={displayName}>
                                    {displayName}
                                </div>
                                <div className="user-email" title={user.email}>
                                    {user.email}
                                </div>
                            </div>

                            <div className="user-stats">
                                <div className="stat-item">
                                    <span className="stat-icon">📁</span>
                                    <span className="stat-value">{stats.files}</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-icon">💾</span>
                                    <span className="stat-value">{stats.size}</span>
                                </div>
                                {viewMode === 'list' && (
                                    <div className="stat-item">
                                        <span className="stat-icon">📅</span>
                                        <span className="stat-value">{stats.lastActive}</span>
                                    </div>
                                )}
                            </div>

                            {user.status && (
                                <div className={`user-status status-${user.status}`}>
                                    {user.status === 'active' && '🟢 Hoạt động'}
                                    {user.status === 'inactive' && '🟡 Không hoạt động'}
                                    {user.status === 'blocked' && '🔴 Bị chặn'}
                                </div>
                            )}
                        </div>

                        {viewMode === 'grid' && (
                            <div className="user-actions">
                                <button
                                    className="btn-action"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        // Handle quick action
                                        console.log('Quick action for', user.email);
                                    }}
                                    title="Hành động nhanh"
                                >
                                    ⚡
                                </button>
                            </div>
                        )}
                    </div>
                );
            })}
        </div>
    );
}

export default UserList;
