import { realtimeService } from './services/realtime-service.js';
import { migrationEngine } from './services/migration-engine.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Test Realtime Service
 * Script để test Supabase Realtime functionality
 */

async function testRealtimeService() {
    console.log('🧪 Testing Realtime Service...\n');

    try {
        // Test 1: Health Check
        console.log('1️⃣ Testing health check...');
        const healthCheck = await realtimeService.healthCheck();
        console.log('Health check result:', healthCheck);
        console.log('✅ Health check completed\n');

        // Test 2: Create Migration Channel
        console.log('2️⃣ Testing migration channel creation...');
        const testMigrationId = 'test_migration_' + Date.now();
        const channelInfo = realtimeService.createMigrationChannel(testMigrationId);
        console.log('Channel created:', channelInfo.migrationId);
        console.log('✅ Channel creation completed\n');

        // Test 3: Subscribe to Progress
        console.log('3️⃣ Testing progress subscription...');
        const unsubscribe = realtimeService.subscribeMigrationProgress(
            testMigrationId,
            (payload) => {
                console.log('📨 Received update:', payload.type, payload.timestamp);
            }
        );
        console.log('✅ Subscription completed\n');

        // Test 4: Broadcast Updates
        console.log('4️⃣ Testing broadcast updates...');
        
        // Broadcast status change
        await realtimeService.broadcastStatusChange(testMigrationId, 'running', {
            previousStatus: 'pending',
            reason: 'Test migration started'
        });
        console.log('📤 Status change broadcasted');

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Broadcast batch progress
        await realtimeService.broadcastBatchProgress(testMigrationId, {
            processedFiles: 5,
            totalFiles: 10,
            successfulFiles: 4,
            failedFiles: 1,
            progress: 50,
            estimatedTimeRemaining: 30
        });
        console.log('📤 Batch progress broadcasted');

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Broadcast file progress
        await realtimeService.broadcastFileProgress(testMigrationId, {
            fileId: 'test_file_123',
            fileName: 'test_document.pdf',
            phase: 'uploading',
            progress: 75,
            speed: 1024
        });
        console.log('📤 File progress broadcasted');

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Broadcast error
        await realtimeService.broadcastError(testMigrationId, {
            severity: 'warning',
            fileId: 'test_file_456',
            fileName: 'problematic_file.docx',
            errorMessage: 'File size too large',
            retryable: true
        });
        console.log('📤 Error broadcasted');

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Broadcast completion
        await realtimeService.broadcastMigrationComplete(testMigrationId, {
            success: true,
            totalFiles: 10,
            successfulFiles: 9,
            failedFiles: 1,
            duration: 120000
        });
        console.log('📤 Migration completion broadcasted');

        console.log('✅ Broadcast tests completed\n');

        // Test 5: Get Channel Presence
        console.log('5️⃣ Testing channel presence...');
        const presence = realtimeService.getChannelPresence(testMigrationId);
        console.log('Channel presence:', presence);
        console.log('✅ Presence test completed\n');

        // Test 6: Get Statistics
        console.log('6️⃣ Testing statistics...');
        const stats = realtimeService.getStats();
        console.log('Realtime stats:', stats);
        console.log('✅ Statistics test completed\n');

        // Wait before cleanup
        console.log('⏳ Waiting 5 seconds before cleanup...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Cleanup
        console.log('7️⃣ Testing cleanup...');
        unsubscribe();
        await realtimeService.cleanupChannel(testMigrationId);
        console.log('✅ Cleanup completed\n');

        console.log('🎉 All realtime tests passed!');

    } catch (error) {
        console.error('❌ Realtime test failed:', error.message);
        console.error(error.stack);
    }
}

async function testMigrationEngineIntegration() {
    console.log('\n🧪 Testing Migration Engine + Realtime Integration...\n');

    try {
        // Mock migration data
        const testUserEmail = '<EMAIL>';
        const testSessionId = 'test_session_' + Date.now();
        const testOptions = {
            mapPermissions: true,
            targetRootFolder: 'Test_Migration',
            preserveFolderStructure: true
        };

        console.log('1️⃣ Testing migration engine realtime integration...');
        
        // Subscribe to migration progress
        const migrationId = `migration_${testSessionId}_${Date.now()}`;
        console.log('Migration ID:', migrationId);

        const unsubscribe = realtimeService.subscribeMigrationProgress(
            migrationId,
            (payload) => {
                console.log(`📨 Migration update [${payload.type}]:`, {
                    migrationId: payload.migrationId,
                    timestamp: payload.timestamp,
                    data: payload.type === 'batch_progress' ? 
                        `${payload.processedFiles}/${payload.totalFiles} files` :
                        payload.type === 'file_progress' ?
                        `${payload.fileName} - ${payload.phase}` :
                        payload.type === 'error' ?
                        `Error: ${payload.errorMessage}` :
                        'Status update'
                });
            }
        );

        // Simulate migration progress updates
        console.log('2️⃣ Simulating migration progress...');

        // Start migration
        await realtimeService.broadcastStatusChange(migrationId, 'running', {
            previousStatus: 'pending',
            reason: 'Migration started'
        });

        // Simulate file processing
        const testFiles = [
            { name: 'document1.pdf', phase: 'downloading' },
            { name: 'document1.pdf', phase: 'uploading' },
            { name: 'document1.pdf', phase: 'completed' },
            { name: 'spreadsheet1.xlsx', phase: 'downloading' },
            { name: 'spreadsheet1.xlsx', phase: 'uploading' },
            { name: 'spreadsheet1.xlsx', phase: 'completed' },
            { name: 'presentation1.pptx', phase: 'downloading' },
            { name: 'presentation1.pptx', phase: 'failed' }
        ];

        let processedFiles = 0;
        let successfulFiles = 0;
        let failedFiles = 0;

        for (let i = 0; i < testFiles.length; i++) {
            const file = testFiles[i];
            
            // Broadcast file progress
            await realtimeService.broadcastFileProgress(migrationId, {
                fileId: `file_${i}`,
                fileName: file.name,
                phase: file.phase,
                progress: file.phase === 'completed' ? 100 : 
                         file.phase === 'failed' ? 0 :
                         Math.random() * 100,
                speed: 1024 * Math.random()
            });

            // Update counters
            if (file.phase === 'completed') {
                processedFiles++;
                successfulFiles++;
            } else if (file.phase === 'failed') {
                processedFiles++;
                failedFiles++;
                
                // Broadcast error
                await realtimeService.broadcastError(migrationId, {
                    severity: 'error',
                    fileId: `file_${i}`,
                    fileName: file.name,
                    errorMessage: 'Simulated error for testing',
                    retryable: true
                });
            }

            // Broadcast batch progress
            if (file.phase === 'completed' || file.phase === 'failed') {
                await realtimeService.broadcastBatchProgress(migrationId, {
                    processedFiles,
                    totalFiles: 3, // 3 unique files
                    successfulFiles,
                    failedFiles,
                    progress: (processedFiles / 3) * 100,
                    estimatedTimeRemaining: (3 - processedFiles) * 10
                });
            }

            // Wait between updates
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Complete migration
        await realtimeService.broadcastMigrationComplete(migrationId, {
            success: true,
            totalFiles: 3,
            successfulFiles,
            failedFiles,
            duration: 15000
        });

        console.log('✅ Migration simulation completed\n');

        // Wait before cleanup
        console.log('⏳ Waiting 3 seconds before cleanup...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Cleanup
        unsubscribe();
        await realtimeService.cleanupChannel(migrationId);
        console.log('✅ Integration test cleanup completed\n');

        console.log('🎉 Migration engine integration test passed!');

    } catch (error) {
        console.error('❌ Integration test failed:', error.message);
        console.error(error.stack);
    }
}

async function runAllTests() {
    console.log('🚀 Starting Realtime Service Tests\n');
    console.log('=' .repeat(50));

    try {
        // Test basic realtime functionality
        await testRealtimeService();
        
        console.log('\n' + '=' .repeat(50));
        
        // Test migration engine integration
        await testMigrationEngineIntegration();
        
        console.log('\n' + '=' .repeat(50));
        console.log('🎉 All tests completed successfully!');
        
    } catch (error) {
        console.error('❌ Test suite failed:', error.message);
        process.exit(1);
    }

    // Final cleanup
    console.log('\n🧹 Final cleanup...');
    await realtimeService.cleanupAllChannels();
    console.log('✅ All channels cleaned up');
    
    process.exit(0);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

export { testRealtimeService, testMigrationEngineIntegration };
