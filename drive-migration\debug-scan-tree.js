import axios from 'axios';

async function testScanAndTree() {
    const baseUrl = 'http://localhost:3000/api';
    
    console.log('🔍 Testing scan and tree functionality...');
    
    try {
        // Step 1: Start a scan
        console.log('\n1. Starting scan...');
        const scanResponse = await axios.post(`${baseUrl}/scan/start`, {
            userEmail: '<EMAIL>',
            scope: 'folder_specific',
            folderId: 'root',
            maxDepth: 2
        });
        
        console.log('✅ Scan started:', scanResponse.data);
        const sessionId = scanResponse.data.sessionId;
        
        // Step 2: Wait for scan to complete
        console.log('\n2. Waiting for scan to complete...');
        let scanStatus;
        let attempts = 0;
        const maxAttempts = 30; // 30 seconds max
        
        do {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const statusResponse = await axios.get(`${baseUrl}/scan/status/${sessionId}`);
            scanStatus = statusResponse.data;
            
            console.log(`   Status: ${scanStatus.status}, Files: ${scanStatus.scanned_files}/${scanStatus.total_files}`);
            attempts++;
        } while (scanStatus.status === 'running' && attempts < maxAttempts);
        
        if (scanStatus.status !== 'completed') {
            throw new Error(`Scan did not complete. Final status: ${scanStatus.status}, Error: ${scanStatus.error_message}`);
        }
        
        console.log('✅ Scan completed successfully');
        
        // Step 3: Test tree API
        console.log('\n3. Testing tree API...');
        const treeResponse = await axios.get(`${baseUrl}/scan/files/tree?sessionId=${sessionId}`);
        
        console.log('✅ Tree data received:');
        console.log('   Total files:', treeResponse.data.totalFiles);
        console.log('   Tree nodes:', treeResponse.data.tree.length);
        console.log('   Stats:', JSON.stringify(treeResponse.data.stats, null, 2));
        
        // Print first few tree nodes for debugging
        if (treeResponse.data.tree.length > 0) {
            console.log('\n   Sample tree nodes:');
            treeResponse.data.tree.slice(0, 3).forEach((node, i) => {
                console.log(`   ${i + 1}. ${node.name} (${node.type}) - ${node.children?.length || 0} children`);
            });
        }
        
        return {
            sessionId,
            scanStatus,
            treeData: treeResponse.data
        };
        
    } catch (error) {
        console.error('❌ Error during test:', error.message);
        if (error.response) {
            console.error('   Response status:', error.response.status);
            console.error('   Response data:', error.response.data);
        }
        throw error;
    }
}

// Run the test
testScanAndTree()
    .then((result) => {
        console.log('\n🎉 Test completed successfully!');
    })
    .catch((error) => {
        console.error('\n💥 Test failed:', error.message);
        process.exit(1);
    });
