import React, { useState, useEffect } from 'react';
import DownloadConfigForm from '../components/DownloadConfigForm';
import DownloadProgress from '../components/DownloadProgress';
import DownloadReport from '../components/DownloadReport';
import { apiGet, apiPost, apiDelete } from '../utils/apiUtils';
import { useToast } from '../contexts/ToastContext';
import './Download.css';

const Download = () => {
    const [currentStep, setCurrentStep] = useState('config'); // config, progress, report
    const [sessions, setSessions] = useState([]);
    const [activeSession, setActiveSession] = useState(null);
    const [loading, setLoading] = useState(false);
    const [users, setUsers] = useState([]);
    const { showError, showSuccess, showWarning, showInfo } = useToast();

    useEffect(() => {
        loadSessions();
        loadUsers();
    }, []);

    /**
     * Load download sessions
     */
    const loadSessions = async () => {
        try {
            const response = await apiGet('/api/download/sessions');
            if (response.success) {
                setSessions(response.data);
            }
        } catch (error) {
            console.error('Error loading sessions:', error);
            showError('Failed to load download sessions');
        }
    };

    /**
     * Load users
     */
    const loadUsers = async () => {
        try {
            const response = await apiGet('/api/download/users');
            if (response.success) {
                setUsers(response.data);
            }
        } catch (error) {
            console.error('Error loading users:', error);
            showError('Failed to load users');
        }
    };

    /**
     * Create new download session
     */
    const handleCreateSession = async (config) => {
        try {
            setLoading(true);
            const response = await apiPost('/api/download/sessions', config);

            if (response.success) {
                setActiveSession(response.data);
                setCurrentStep('progress');
                await loadSessions();
                showSuccess('Download session created successfully');
            } else {
                showError(response.error || 'Failed to create download session');
            }
        } catch (error) {
            console.error('Error creating session:', error);
            showError('Failed to create download session');
        } finally {
            setLoading(false);
        }
    };

    /**
     * Start download session
     */
    const handleStartSession = async (sessionId) => {
        try {
            setLoading(true);
            const response = await apiPost(`/api/download/sessions/${sessionId}/start`);

            if (response.success) {
                await loadSessions();
                showSuccess('Download started successfully');
            } else {
                showError(response.error || 'Failed to start download');
            }
        } catch (error) {
            console.error('Error starting session:', error);
            showError('Failed to start download');
        } finally {
            setLoading(false);
        }
    };

    /**
     * Pause download session
     */
    const handlePauseSession = async (sessionId) => {
        try {
            const response = await apiPost(`/api/download/sessions/${sessionId}/pause`);

            if (response.success) {
                await loadSessions();
                showInfo('Download paused');
            } else {
                showError(response.error || 'Failed to pause download');
            }
        } catch (error) {
            console.error('Error pausing session:', error);
            showError('Failed to pause download');
        }
    };

    /**
     * Cancel download session
     */
    const handleCancelSession = async (sessionId) => {
        try {
            const response = await apiPost(`/api/download/sessions/${sessionId}/cancel`);

            if (response.success) {
                await loadSessions();
                showWarning('Download cancelled');
            } else {
                showError(response.error || 'Failed to cancel download');
            }
        } catch (error) {
            console.error('Error cancelling session:', error);
            showError('Failed to cancel download');
        }
    };

    /**
     * Delete download session
     */
    const handleDeleteSession = async (sessionId) => {
        if (!window.confirm('Are you sure you want to delete this session?')) {
            return;
        }

        try {
            const response = await apiDelete(`/api/download/sessions/${sessionId}`);

            if (response.success) {
                await loadSessions();
                showSuccess('Session deleted successfully');
            } else {
                showError(response.error || 'Failed to delete session');
            }
        } catch (error) {
            console.error('Error deleting session:', error);
            showError('Failed to delete session');
        }
    };

    /**
     * View session details
     */
    const handleViewSession = (session) => {
        setActiveSession(session);
        if (session.status === 'running') {
            setCurrentStep('progress');
        } else if (session.status === 'completed' || session.status === 'failed') {
            setCurrentStep('report');
        } else {
            setCurrentStep('config');
        }
    };

    /**
     * Go back to sessions list
     */
    const handleBackToSessions = () => {
        setActiveSession(null);
        setCurrentStep('config');
        loadSessions();
    };

    /**
     * Format file size
     */
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    /**
     * Format duration
     */
    const formatDuration = (seconds) => {
        if (!seconds) return '-';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    /**
     * Get status badge class
     */
    const getStatusBadgeClass = (status) => {
        switch (status) {
            case 'pending': return 'status-pending';
            case 'running': return 'status-running';
            case 'paused': return 'status-paused';
            case 'completed': return 'status-completed';
            case 'failed': return 'status-failed';
            case 'cancelled': return 'status-cancelled';
            default: return 'status-unknown';
        }
    };

    return (
        <div className="download-page">
            <div className="page-header">
                <h1>📥 Download Files from Google Drive</h1>
                <p>Download files from Google Drive to local storage with progress tracking</p>
            </div>

            {!activeSession ? (
                <div className="sessions-overview">
                    {/* Create New Session Button */}
                    <div className="actions-bar">
                        <button
                            className="btn btn-primary"
                            onClick={() => {
                                setActiveSession({ name: '', status: 'new' });
                                setCurrentStep('config');
                            }}
                        >
                            ➕ Create New Download Session
                        </button>
                        <button
                            className="btn btn-secondary"
                            onClick={loadSessions}
                        >
                            🔄 Refresh
                        </button>
                    </div>

                    {/* Sessions List */}
                    <div className="sessions-list">
                        <h2>Download Sessions</h2>

                        {sessions.length === 0 ? (
                            <div className="empty-state">
                                <p>No download sessions found. Create your first session to get started.</p>
                            </div>
                        ) : (
                            <div className="sessions-grid">
                                {sessions.map((session) => (
                                    <div key={session.id} className="session-card">
                                        <div className="session-header">
                                            <h3>{session.name}</h3>
                                            <span className={`status-badge ${getStatusBadgeClass(session.status)}`}>
                                                {session.status}
                                            </span>
                                        </div>

                                        <div className="session-stats">
                                            <div className="stat">
                                                <span className="label">Files:</span>
                                                <span className="value">
                                                    {session.downloaded_files || 0} / {session.total_files || 0}
                                                </span>
                                            </div>
                                            <div className="stat">
                                                <span className="label">Size:</span>
                                                <span className="value">
                                                    {formatFileSize(session.downloaded_size || 0)} / {formatFileSize(session.total_size || 0)}
                                                </span>
                                            </div>
                                            <div className="stat">
                                                <span className="label">Progress:</span>
                                                <span className="value">{session.progress_percentage || 0}%</span>
                                            </div>
                                            <div className="stat">
                                                <span className="label">Duration:</span>
                                                <span className="value">{formatDuration(session.duration_seconds)}</span>
                                            </div>
                                        </div>

                                        <div className="session-actions">
                                            <button
                                                className="btn btn-sm btn-primary"
                                                onClick={() => handleViewSession(session)}
                                            >
                                                👁️ View
                                            </button>

                                            {session.status === 'pending' && (
                                                <button
                                                    className="btn btn-sm btn-success"
                                                    onClick={() => handleStartSession(session.id)}
                                                    disabled={loading}
                                                >
                                                    ▶️ Start
                                                </button>
                                            )}

                                            {session.status === 'running' && (
                                                <button
                                                    className="btn btn-sm btn-warning"
                                                    onClick={() => handlePauseSession(session.id)}
                                                >
                                                    ⏸️ Pause
                                                </button>
                                            )}

                                            {(session.status === 'running' || session.status === 'paused') && (
                                                <button
                                                    className="btn btn-sm btn-danger"
                                                    onClick={() => handleCancelSession(session.id)}
                                                >
                                                    ❌ Cancel
                                                </button>
                                            )}

                                            {(session.status === 'completed' || session.status === 'failed' || session.status === 'cancelled') && (
                                                <button
                                                    className="btn btn-sm btn-danger"
                                                    onClick={() => handleDeleteSession(session.id)}
                                                >
                                                    🗑️ Delete
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            ) : (
                <div className="session-detail">
                    <div className="session-nav">
                        <button
                            className="btn btn-secondary"
                            onClick={handleBackToSessions}
                        >
                            ← Back to Sessions
                        </button>
                        <h2>{activeSession.name}</h2>
                    </div>

                    {currentStep === 'config' && (
                        <DownloadConfigForm
                            users={users}
                            onSubmit={handleCreateSession}
                            loading={loading}
                            initialData={activeSession}
                        />
                    )}

                    {currentStep === 'progress' && (
                        <DownloadProgress
                            session={activeSession}
                            onPause={() => handlePauseSession(activeSession.id)}
                            onCancel={() => handleCancelSession(activeSession.id)}
                            onComplete={() => setCurrentStep('report')}
                        />
                    )}

                    {currentStep === 'report' && (
                        <DownloadReport
                            session={activeSession}
                            onStartNew={() => setCurrentStep('config')}
                        />
                    )}
                </div>
            )}
        </div>
    );
};

export default Download;
