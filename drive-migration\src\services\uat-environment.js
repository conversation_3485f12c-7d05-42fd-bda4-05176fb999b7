/**
 * UAT Environment Service
 * Thiết lập và quản lý môi trường User Acceptance Testing
 */

import fs from 'fs/promises';
import path from 'path';
import { supabaseClient } from '../database/supabase.js';

class UATEnvironment {
    constructor() {
        this.environment = process.env.NODE_ENV || 'development';
        this.uatConfig = {
            maxTestUsers: 10,
            maxTestFiles: 1000,
            testDataRetentionDays: 7,
            enableDetailedLogging: true,
            enablePerformanceMonitoring: true,
            enableErrorTracking: true
        };
        
        console.log(`🧪 UAT Environment initialized for ${this.environment}`);
    }

    /**
     * Setup UAT environment
     */
    async setupUATEnvironment() {
        try {
            console.log('🚀 Setting up UAT Environment...\n');

            // 1. Validate environment configuration
            const configValidation = await this.validateEnvironmentConfig();
            if (!configValidation.valid) {
                throw new Error(`Environment validation failed: ${configValidation.error}`);
            }

            // 2. Setup test database schema
            const dbSetup = await this.setupTestDatabase();
            if (!dbSetup.success) {
                console.log('⚠️ Database setup warning:', dbSetup.error);
            }

            // 3. Create test data
            const testData = await this.createTestData();
            if (!testData.success) {
                console.log('⚠️ Test data creation warning:', testData.error);
            }

            // 4. Setup monitoring
            const monitoring = await this.setupMonitoring();
            if (!monitoring.success) {
                console.log('⚠️ Monitoring setup warning:', monitoring.error);
            }

            // 5. Create UAT documentation
            const docs = await this.generateUATDocumentation();
            if (!docs.success) {
                console.log('⚠️ Documentation generation warning:', docs.error);
            }

            console.log('✅ UAT Environment setup completed successfully!\n');
            
            return {
                success: true,
                environment: this.environment,
                config: this.uatConfig,
                setupResults: {
                    configValidation,
                    dbSetup,
                    testData,
                    monitoring,
                    docs
                }
            };

        } catch (error) {
            console.error('❌ UAT Environment setup failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Validate environment configuration
     */
    async validateEnvironmentConfig() {
        try {
            console.log('🔍 Validating environment configuration...');

            const requiredEnvVars = [
                'SUPABASE_URL',
                'SUPABASE_ANON_KEY',
                'SUPABASE_SERVICE_ROLE_KEY'
            ];

            const missingVars = [];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    missingVars.push(envVar);
                }
            }

            if (missingVars.length > 0) {
                return {
                    valid: false,
                    error: `Missing environment variables: ${missingVars.join(', ')}`
                };
            }

            // Test database connection
            const dbTest = await supabaseClient.testConnection();
            if (!dbTest.success) {
                return {
                    valid: false,
                    error: `Database connection failed: ${dbTest.error}`
                };
            }

            console.log('✅ Environment configuration valid');
            return {
                valid: true,
                environment: this.environment,
                databaseConnected: true
            };

        } catch (error) {
            return {
                valid: false,
                error: error.message
            };
        }
    }

    /**
     * Setup test database với sample data
     */
    async setupTestDatabase() {
        try {
            console.log('🗄️ Setting up test database...');

            // Create test migration task
            const testTask = {
                id: 'uat-test-migration-' + Date.now(),
                user_email: '<EMAIL>',
                status: 'pending',
                total_files: 50,
                processed_files: 0,
                successful_files: 0,
                failed_files: 0,
                total_size: 1024 * 1024 * 100, // 100MB
                processed_size: 0,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const { data: task, error: taskError } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .insert([testTask])
                .select()
                .single();

            if (taskError) {
                console.log('⚠️ Could not create test task:', taskError.message);
                return {
                    success: false,
                    error: taskError.message
                };
            }

            // Create sample migration items
            const sampleItems = this.generateSampleMigrationItems(task.id, 50);
            
            const { data: items, error: itemsError } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .insert(sampleItems)
                .select();

            if (itemsError) {
                console.log('⚠️ Could not create test items:', itemsError.message);
                return {
                    success: false,
                    error: itemsError.message
                };
            }

            console.log(`✅ Test database setup complete with ${items.length} sample items`);
            return {
                success: true,
                testTaskId: task.id,
                sampleItemsCount: items.length
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate sample migration items for testing
     */
    generateSampleMigrationItems(taskId, count) {
        const fileTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'image/jpeg',
            'image/png',
            'text/plain',
            'video/mp4'
        ];

        const statuses = ['pending', 'completed', 'failed'];
        const items = [];

        for (let i = 0; i < count; i++) {
            const fileType = fileTypes[Math.floor(Math.random() * fileTypes.length)];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const fileSize = Math.floor(Math.random() * 50 * 1024 * 1024); // Up to 50MB

            items.push({
                migration_task_id: taskId,
                google_file_id: `uat_test_file_${i + 1}`,
                google_file_name: `test-file-${i + 1}.${this.getFileExtension(fileType)}`,
                google_file_path: `/uat-test/folder-${Math.floor(i / 10)}/test-file-${i + 1}`,
                google_file_size: fileSize,
                file_type: fileType,
                status: status,
                lark_file_token: status === 'completed' ? `lark_token_${i + 1}` : null,
                error_message: status === 'failed' ? 'UAT test error simulation' : null,
                download_time: status === 'completed' ? Math.floor(Math.random() * 5000) : null,
                upload_time: status === 'completed' ? Math.floor(Math.random() * 3000) : null,
                retry_count: status === 'failed' ? Math.floor(Math.random() * 3) : 0,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });
        }

        return items;
    }

    /**
     * Get file extension from MIME type
     */
    getFileExtension(mimeType) {
        const extensions = {
            'application/pdf': 'pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
            'image/jpeg': 'jpg',
            'image/png': 'png',
            'text/plain': 'txt',
            'video/mp4': 'mp4'
        };
        return extensions[mimeType] || 'bin';
    }

    /**
     * Create test data for UAT
     */
    async createTestData() {
        try {
            console.log('📊 Creating UAT test data...');

            // Create test users
            const testUsers = [
                { email_google: '<EMAIL>', lark_userid: 'uat_admin_123', mapped: true },
                { email_google: '<EMAIL>', lark_userid: 'uat_user1_456', mapped: true },
                { email_google: '<EMAIL>', lark_userid: null, mapped: false }
            ];

            for (const user of testUsers) {
                await supabaseClient.createUserMapping(user.email_google, user.lark_userid);
            }

            console.log(`✅ Created ${testUsers.length} test users`);
            return {
                success: true,
                testUsersCount: testUsers.length
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Setup monitoring for UAT
     */
    async setupMonitoring() {
        try {
            console.log('📈 Setting up UAT monitoring...');

            // Create monitoring configuration
            const monitoringConfig = {
                enableRealTimeTracking: true,
                enablePerformanceMetrics: true,
                enableErrorLogging: true,
                enableUserActivityTracking: true,
                alertThresholds: {
                    errorRate: 5, // 5% error rate threshold
                    responseTime: 5000, // 5 second response time threshold
                    memoryUsage: 80 // 80% memory usage threshold
                }
            };

            // Save monitoring config
            await this.saveMonitoringConfig(monitoringConfig);

            console.log('✅ UAT monitoring setup complete');
            return {
                success: true,
                config: monitoringConfig
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Save monitoring configuration
     */
    async saveMonitoringConfig(config) {
        try {
            const configPath = path.join(process.cwd(), 'uat-monitoring-config.json');
            await fs.writeFile(configPath, JSON.stringify(config, null, 2));
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate UAT documentation
     */
    async generateUATDocumentation() {
        try {
            console.log('📚 Generating UAT documentation...');

            const uatGuide = this.createUATGuide();
            const testCases = this.createTestCases();
            const userManual = this.createUserManual();

            // Save documentation files
            const docsDir = path.join(process.cwd(), 'docs', 'uat');
            await fs.mkdir(docsDir, { recursive: true });

            await fs.writeFile(path.join(docsDir, 'uat-guide.md'), uatGuide);
            await fs.writeFile(path.join(docsDir, 'test-cases.md'), testCases);
            await fs.writeFile(path.join(docsDir, 'user-manual.md'), userManual);

            console.log('✅ UAT documentation generated');
            return {
                success: true,
                files: ['uat-guide.md', 'test-cases.md', 'user-manual.md']
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create UAT guide content
     */
    createUATGuide() {
        return `# Drive-to-Lark Migration UAT Guide

## Overview
This guide provides instructions for User Acceptance Testing of the Drive-to-Lark Migration system.

## Test Environment
- **Environment**: ${this.environment}
- **Max Test Users**: ${this.uatConfig.maxTestUsers}
- **Max Test Files**: ${this.uatConfig.maxTestFiles}
- **Data Retention**: ${this.uatConfig.testDataRetentionDays} days

## Test Scenarios

### 1. Authentication Testing
- Test Google Service Account authentication
- Test Lark App authentication
- Verify credential security

### 2. Drive Scanning Testing
- Test full drive scanning
- Test folder-specific scanning
- Verify file discovery accuracy

### 3. Migration Testing
- Test file migration process
- Test permission mapping
- Verify error handling

### 4. Reporting Testing
- Test report generation
- Test report download
- Verify report accuracy

### 5. Performance Testing
- Test with large file sets
- Verify throughput targets
- Test system stability

## Success Criteria
- All test cases pass
- Performance targets met
- User feedback positive
- System stability confirmed

Generated: ${new Date().toISOString()}
`;
    }

    /**
     * Create test cases content
     */
    createTestCases() {
        return `# UAT Test Cases

## Test Case 1: User Authentication
**Objective**: Verify user can authenticate with Google and Lark
**Steps**:
1. Navigate to authentication page
2. Upload Google Service Account credentials
3. Enter Lark App ID and Secret
4. Click "Test Connection"
**Expected**: Both connections successful

## Test Case 2: Drive Scanning
**Objective**: Verify system can scan Google Drive
**Steps**:
1. Complete authentication
2. Select "Full Drive Scan"
3. Start scanning process
4. Review discovered files
**Expected**: Files discovered and listed correctly

## Test Case 3: File Migration
**Objective**: Verify files can be migrated to Lark
**Steps**:
1. Select files for migration
2. Configure migration settings
3. Start migration process
4. Monitor progress
**Expected**: Files migrated successfully

## Test Case 4: Report Generation
**Objective**: Verify migration reports can be generated
**Steps**:
1. Complete a migration
2. Navigate to Reports section
3. Generate CSV and PDF reports
4. Download reports
**Expected**: Reports generated and downloadable

## Test Case 5: Error Handling
**Objective**: Verify system handles errors gracefully
**Steps**:
1. Simulate network errors
2. Test with invalid credentials
3. Test with corrupted files
**Expected**: Errors handled gracefully with clear messages

Generated: ${new Date().toISOString()}
`;
    }

    /**
     * Create user manual content
     */
    createUserManual() {
        return `# Drive-to-Lark Migration User Manual

## Getting Started

### 1. System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Google Workspace admin access
- Lark admin access
- Stable internet connection

### 2. Initial Setup
1. Obtain Google Service Account credentials
2. Get Lark App ID and Secret
3. Access the migration system
4. Complete authentication setup

### 3. Migration Process
1. **Authenticate**: Set up Google and Lark connections
2. **Scan**: Discover files in Google Drive
3. **Select**: Choose files and folders to migrate
4. **Configure**: Set migration options
5. **Migrate**: Start the migration process
6. **Monitor**: Track progress in real-time
7. **Report**: Generate and download reports

### 4. Best Practices
- Test with small file sets first
- Monitor system performance
- Review migration reports
- Keep credentials secure
- Plan migration during off-peak hours

### 5. Troubleshooting
- Check network connectivity
- Verify credentials are valid
- Review error messages
- Contact support if needed

### 6. Support
For technical support, contact the IT team with:
- Error messages
- Migration task ID
- Steps to reproduce issue

Generated: ${new Date().toISOString()}
`;
    }

    /**
     * Cleanup UAT environment
     */
    async cleanupUATEnvironment() {
        try {
            console.log('🧹 Cleaning up UAT environment...');

            // Remove test data older than retention period
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - this.uatConfig.testDataRetentionDays);

            // Clean up test migration tasks
            const { error: taskError } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .delete()
                .like('id', 'uat-test-%')
                .lt('created_at', cutoffDate.toISOString());

            if (taskError) {
                console.log('⚠️ Task cleanup warning:', taskError.message);
            }

            // Clean up test users
            const { error: userError } = await supabaseClient.getServiceClient()
                .from('users')
                .delete()
                .like('email_google', '<EMAIL>');

            if (userError) {
                console.log('⚠️ User cleanup warning:', userError.message);
            }

            console.log('✅ UAT environment cleanup completed');
            return { success: true };

        } catch (error) {
            console.error('❌ UAT cleanup failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get UAT environment status
     */
    async getUATStatus() {
        try {
            // Get test data counts
            const { data: tasks, error: taskError } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .select('*')
                .like('id', 'uat-test-%');

            const { data: users, error: userError } = await supabaseClient.getServiceClient()
                .from('users')
                .select('*')
                .like('email_google', '<EMAIL>');

            return {
                success: true,
                environment: this.environment,
                config: this.uatConfig,
                testData: {
                    migrationTasks: tasks?.length || 0,
                    testUsers: users?.length || 0
                },
                status: 'ready'
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

export default UATEnvironment;
