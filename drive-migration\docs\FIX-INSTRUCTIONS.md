# Fix for Google Drive Scan Issue

## Problem Summary
- Scan step 2 completes but finds 0 files
- Step 3 cannot display file tree because there are no files
- Root cause: Domain-wide delegation not configured

## Immediate Fix Required

### 1. Configure Domain-Wide Delegation

**In Google Cloud Console:**
1. Go to https://console.cloud.google.com/
2. Navigate to IAM & Admin → Service Accounts
3. Find service account: `<EMAIL>`
4. Click on it → Details tab → Advanced settings
5. Enable "Google Workspace Domain-wide Delegation"
6. Note the Client ID: `110056378231532510280`

**In Google Admin Console:**
1. Go to https://admin.google.com/
2. Navigate to Security → Access and data control → API Controls
3. Click "Manage Domain Wide Delegation"
4. Click "Add new"
5. Enter Client ID: `110056378231532510280`
6. Add OAuth Scopes:
   ```
   https://www.googleapis.com/auth/drive,
   https://www.googleapis.com/auth/drive.file,
   https://www.googleapis.com/auth/drive.metadata,
   https://www.googleapis.com/auth/drive.readonly
   ```
7. Click "Authorize"

### 2. Wait and Test
- Wait 5-10 minutes for changes to propagate
- Test domain delegation: `npm run test:domain-delegation`
- Should show "Domain-wide delegation working: ✅ YES"

### 3. Re-test Scan
- Open http://localhost:5175
- Start a new scan
- Should now find actual files in step 2
- Step 3 should display the file tree properly

## Verification Commands

```bash
# Check domain delegation
node src/check-domain-delegation.js <EMAIL>

# Test full scan
node debug-scan-tree.js
```

## Expected Results After Fix
- Scan should find actual files (not 0)
- File tree should display with folders and files
- No more "cannot display file tree" error
