/* User Stats Component Styles */
.user-stats {
    display: flex;
    gap: 2rem;
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 1rem;
}

.stats-section {
    flex: 1;
}

.stats-section h3 {
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #4a5568;
    opacity: 0.8;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 500;
    margin-top: 0.25rem;
}

/* Selected User Stats */
.selected-stats {
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    padding-left: 2rem;
}

.selected-user-info {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.selected-user-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.selected-user-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.selected-user-initials {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4299e1, #667eea);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.selected-user-details {
    flex: 1;
}

.selected-user-name {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.selected-user-email {
    font-size: 0.85rem;
    color: #718096;
}

.selected-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.selected-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.selected-stat-icon {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.selected-stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.selected-stat-label {
    font-size: 0.7rem;
    color: #718096;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .user-stats {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .selected-stats {
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.3);
        padding-left: 0;
        padding-top: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
}

@media (max-width: 768px) {
    .user-stats {
        padding: 1rem;
        margin-top: 0;
        border-top: none;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .stat-card {
        padding: 0.75rem;
        gap: 0.5rem;
    }
    
    .stat-icon {
        font-size: 1.2rem;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .stat-label {
        font-size: 0.7rem;
    }
    
    .selected-user-header {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .selected-stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }
    
    .selected-stat {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .selected-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-section h3 {
        font-size: 0.85rem;
    }
}

/* Animation for stats updates */
.stat-value {
    transition: all 0.3s ease;
}

.stat-card {
    animation: statFadeIn 0.5s ease;
}

@keyframes statFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Highlight animation for updated stats */
.stat-card.updated {
    animation: statHighlight 0.6s ease;
}

@keyframes statHighlight {
    0% { background: rgba(255, 255, 255, 0.9); }
    50% { background: rgba(66, 153, 225, 0.1); }
    100% { background: rgba(255, 255, 255, 0.9); }
}
