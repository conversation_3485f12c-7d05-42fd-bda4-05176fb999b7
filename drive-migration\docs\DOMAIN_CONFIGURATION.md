# Domain Configuration Guide

This guide explains how to configure the application to work with different domains (osp.vn or osp.com.vn) and Google service accounts.

## Environment Variables

The application uses the following environment variables to configure the domain, Google service account, and admin account:

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_DOMAIN` | The domain to use (osp.vn or osp.com.vn) | osp.com.vn |
| `GOOGLE_SERVICE_ACCOUNT_PATH` | Path to the Google service account JSON file | ./google-service-account.json |
| `GOOGLE_ADMIN_EMAIL` | Email of the admin account | admin@{GOOGLE_DOMAIN} |

## Configuration Files

The application includes the following configuration files:

- `.env` - The main configuration file
- `.env.osp.vn` - Template for osp.vn domain
- `.env.osp.com.vn` - Template for osp.com.vn domain

## Switching Domains

To switch between domains, you can:

1. Edit the `.env` file directly and update the `GOOGLE_DOMAIN`, `GOOGLE_SERVICE_ACCOUNT_PATH`, and `GOOGLE_ADMIN_EMAIL` variables.

2. Or copy one of the template files:

```bash
# For osp.vn domain
cp .env.osp.vn .env

# For osp.com.vn domain
cp .env.osp.com.vn .env
```

## Google Service Account Files

The application expects the Google service account JSON files to be in the root directory:

- `google-service-account.json` - Default service account file
- `google-service-account-osp-vn.json` - Service account for osp.vn domain
- `google-service-account-osp-com-vn.json` - Service account for osp.com.vn domain

You can specify a different path using the `GOOGLE_SERVICE_ACCOUNT_PATH` environment variable.

## Admin Accounts

The application uses the admin account for domain-wide delegation. By default, it uses `admin@{GOOGLE_DOMAIN}`, but you can specify a different account using the `GOOGLE_ADMIN_EMAIL` environment variable.

## Example Usage

### Using osp.vn Domain

```bash
# Set environment variables
export GOOGLE_DOMAIN=osp.vn
export GOOGLE_SERVICE_ACCOUNT_PATH=./google-service-account-osp-vn.json
export GOOGLE_ADMIN_EMAIL=<EMAIL>

# Or copy the template file
cp .env.osp.vn .env
```

### Using osp.com.vn Domain

```bash
# Set environment variables
export GOOGLE_DOMAIN=osp.com.vn
export GOOGLE_SERVICE_ACCOUNT_PATH=./google-service-account-osp-com-vn.json
export GOOGLE_ADMIN_EMAIL=<EMAIL>

# Or copy the template file
cp .env.osp.com.vn .env
```

## Troubleshooting

If you encounter issues with the domain configuration:

1. Check that the Google service account JSON file exists and is valid
2. Verify that the admin account has the necessary permissions
3. Ensure that the domain is correctly configured in the Google Admin Console
4. Check the application logs for any error messages

For more information, see the [Domain-Wide Delegation Setup](./docs/DOMAIN_WIDE_DELEGATION_SETUP.md) documentation.
