<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate File Remover</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🗂️ Duplicate File Remover</h1>
            <p>Tìm và xóa các file trùng lặp trong thư mục</p>
        </header>

        <main>
            <!-- Input Section -->
            <section class="input-section">
                <div class="form-group">
                    <label for="directoryPath">Đường dẫn thư mục gốc:</label>
                    <input type="text" id="directoryPath" placeholder="Ví dụ: C:\Users\<USER>\Documents" />
                </div>
                <div class="form-group">
                    <label for="maxWorkers">Số luồng quét song song:</label>
                    <input type="number" id="maxWorkers" min="1" max="16" value="4" placeholder="4" />
                    <span class="help-text">Tăng số luồng để quét nhanh hơn (1-16)</span>
                </div>
                <div class="form-group">
                    <button id="scanBtn" class="btn btn-primary">
                        <span class="btn-text">Quét thư mục</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner"></span> Đang quét...
                        </span>
                    </button>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="progress-section" style="display: none;">
                <div class="progress-container">
                    <div class="progress-header">
                        <h3 id="progressTitle">Đang quét...</h3>
                        <span id="progressPercent">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-details">
                        <div id="progressMessage">Khởi tạo...</div>
                        <div id="progressStats">0 / 0 files</div>
                    </div>
                </div>
            </section>

            <!-- Real-time Results Section -->
            <section class="realtime-results-section" style="display: none;">
                <div class="results-header">
                    <h2>🔍 File trùng lặp được phát hiện</h2>
                    <div class="results-summary">
                        <span id="realtimeDuplicateCount">0</span> nhóm file trùng lặp
                    </div>
                </div>

                <div class="results-actions">
                    <button id="selectAllRealtimeBtn" class="btn btn-secondary">Chọn tất cả</button>
                    <button id="deselectAllRealtimeBtn" class="btn btn-secondary">Bỏ chọn tất cả</button>
                    <button id="deleteAllDuplicatesBtn" class="btn btn-danger" disabled>
                        <span class="btn-text">🗑️ Xóa tất cả file trùng (giữ file gốc)</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner"></span> Đang xóa...
                        </span>
                    </button>
                    <button id="deleteSelectedRealtimeBtn" class="btn btn-warning" disabled>
                        <span class="btn-text">Xóa file đã chọn</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner"></span> Đang xóa...
                        </span>
                    </button>
                </div>

                <div id="realtimeDuplicateGroups" class="duplicate-groups"></div>
            </section>

            <!-- Final Results Section -->
            <section class="results-section" style="display: none;">
                <div class="results-header">
                    <h2>✅ Kết quả quét hoàn tất</h2>
                    <div class="results-summary">
                        <span id="duplicateCount">0</span> nhóm file trùng lặp được tìm thấy
                    </div>
                </div>

                <div class="results-actions">
                    <button id="selectAllBtn" class="btn btn-secondary">Chọn tất cả</button>
                    <button id="deselectAllBtn" class="btn btn-secondary">Bỏ chọn tất cả</button>
                    <button id="deleteAllDuplicatesFinalBtn" class="btn btn-danger" disabled>
                        <span class="btn-text">🗑️ Xóa tất cả file trùng (giữ file gốc)</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner"></span> Đang xóa...
                        </span>
                    </button>
                    <button id="deleteSelectedBtn" class="btn btn-warning" disabled>
                        <span class="btn-text">Xóa file đã chọn</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner"></span> Đang xóa...
                        </span>
                    </button>
                </div>

                <div id="duplicateGroups" class="duplicate-groups"></div>
            </section>

            <!-- Delete Progress Section -->
            <section class="delete-progress-section" style="display: none;">
                <div class="progress-container">
                    <div class="progress-header">
                        <h3>Đang xóa file...</h3>
                        <span id="deleteProgressPercent">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="deleteProgressFill"></div>
                    </div>
                    <div class="progress-details">
                        <div id="deleteProgressMessage">Khởi tạo...</div>
                        <div id="deleteProgressStats">0 / 0 files</div>
                    </div>
                </div>
            </section>

            <!-- Delete Results Section -->
            <section class="delete-results-section" style="display: none;">
                <div class="results-header">
                    <h2>Kết quả xóa file</h2>
                </div>
                <div id="deleteResults"></div>
            </section>
        </main>
    </div>

    <script src="app.js"></script>
</body>
</html>
