import React, { useState, useEffect, useRef } from 'react';
import { apiGet } from '../utils/apiUtils';
import './UserSelector.css';

const UserSelector = ({ value, onChange, className = '', disabled = false }) => {
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [error, setError] = useState(null);
    const dropdownRef = useRef(null);
    const inputRef = useRef(null);

    // Load users when component mounts
    useEffect(() => {
        loadUsers();
    }, []);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const loadUsers = async () => {
        setLoading(true);
        setError(null);
        
        try {
            const result = await apiGet('/api/scan/users');
            const usersList = result.users || [];
            
            // Add "All Users" option at the beginning
            const usersWithAllOption = [
                {
                    id: 'ALL_USERS',
                    email: 'ALL_USERS',
                    fullName: 'Tất cả users',
                    isAllOption: true
                },
                ...usersList.map(user => ({
                    id: user.userId || user.id,
                    email: user.email,
                    fullName: user.fullName || user.email,
                    givenName: user.givenName,
                    familyName: user.familyName,
                    lastLoginTime: user.lastLoginTime,
                    suspended: user.suspended
                }))
            ];
            
            setUsers(usersWithAllOption);
        } catch (error) {
            console.error('Error loading users:', error);
            setError('Không thể tải danh sách users');
        } finally {
            setLoading(false);
        }
    };

    const filteredUsers = users.filter(user => {
        if (!searchTerm) return true;
        const term = searchTerm.toLowerCase();
        return (
            user.email?.toLowerCase().includes(term) ||
            user.fullName?.toLowerCase().includes(term) ||
            user.givenName?.toLowerCase().includes(term) ||
            user.familyName?.toLowerCase().includes(term)
        );
    });

    const selectedUser = users.find(user => user.email === value);
    const displayValue = selectedUser ? 
        (selectedUser.isAllOption ? selectedUser.fullName : selectedUser.email) : 
        value || '';

    const handleUserSelect = (user) => {
        onChange(user.email);
        setIsOpen(false);
        setSearchTerm('');
    };

    const handleInputClick = () => {
        if (!disabled) {
            setIsOpen(!isOpen);
        }
    };

    const handleInputFocus = () => {
        if (!disabled) {
            setIsOpen(true);
        }
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setIsOpen(true);
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
            setIsOpen(false);
            setSearchTerm('');
        }
    };

    return (
        <div className={`user-selector ${className}`} ref={dropdownRef}>
            <div className="user-selector-input-container">
                <input
                    ref={inputRef}
                    type="text"
                    className="user-selector-input"
                    placeholder={loading ? "Đang tải users..." : "Chọn user..."}
                    value={isOpen ? searchTerm : displayValue}
                    onChange={handleSearchChange}
                    onClick={handleInputClick}
                    onFocus={handleInputFocus}
                    onKeyDown={handleKeyDown}
                    disabled={disabled || loading}
                    autoComplete="off"
                />
                <div 
                    className={`user-selector-arrow ${isOpen ? 'open' : ''}`}
                    onClick={handleInputClick}
                >
                    ▼
                </div>
            </div>

            {isOpen && (
                <div className="user-selector-dropdown">
                    {loading && (
                        <div className="user-selector-loading">
                            <div className="spinner"></div>
                            Đang tải users...
                        </div>
                    )}

                    {error && (
                        <div className="user-selector-error">
                            {error}
                            <button 
                                className="retry-btn" 
                                onClick={loadUsers}
                                type="button"
                            >
                                Thử lại
                            </button>
                        </div>
                    )}

                    {!loading && !error && filteredUsers.length === 0 && (
                        <div className="user-selector-no-results">
                            Không tìm thấy user nào
                        </div>
                    )}

                    {!loading && !error && filteredUsers.length > 0 && (
                        <div className="user-selector-list">
                            {filteredUsers.map((user) => (
                                <div
                                    key={user.id}
                                    className={`user-selector-item ${user.isAllOption ? 'all-users-option' : ''} ${user.email === value ? 'selected' : ''} ${user.suspended ? 'suspended' : ''}`}
                                    onClick={() => handleUserSelect(user)}
                                >
                                    <div className="user-info">
                                        <div className="user-email">
                                            {user.isAllOption ? (
                                                <span className="all-users-label">
                                                    🌐 {user.fullName}
                                                </span>
                                            ) : (
                                                <>
                                                    {user.email}
                                                    {user.suspended && (
                                                        <span className="suspended-badge">Suspended</span>
                                                    )}
                                                </>
                                            )}
                                        </div>
                                        {!user.isAllOption && user.fullName && user.fullName !== user.email && (
                                            <div className="user-name">{user.fullName}</div>
                                        )}
                                        {!user.isAllOption && user.lastLoginTime && (
                                            <div className="user-last-login">
                                                Đăng nhập cuối: {new Date(user.lastLoginTime).toLocaleString('vi-VN')}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default UserSelector;
