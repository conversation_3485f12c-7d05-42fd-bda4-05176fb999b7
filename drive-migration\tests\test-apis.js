import { GoogleDriveAPI } from './api/google-drive-api.js';
import { LarkDriveAPI } from './api/lark-drive-api.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Test script để kiểm tra kết nối và API calls
 */
async function testAPIs() {
    console.log('🚀 Starting API tests...\n');

    // Test Google Drive API
    console.log('=== GOOGLE DRIVE API TESTS ===');
    try {
        const googleAPI = new GoogleDriveAPI();

        // Cần email người dùng để test (thay bằng email thật trong domain)
        const testUserEmail = process.env.TEST_USER_EMAIL || '<EMAIL>';
        console.log(`Testing with user: ${testUserEmail}\n`);

        const googleResults = await googleAPI.testAPIs(testUserEmail);

        console.log('📊 Google Drive API Test Results:');
        console.log(`  ✅ Connection: ${googleResults.connection ? '✅' : '❌'}`);
        console.log(`  📁 List Files: ${googleResults.listFiles ? '✅' : '❌'}`);
        console.log(`  📄 Get File: ${googleResults.getFile ? '✅' : '❌'}`);
        console.log(`  🔐 Get Permissions: ${googleResults.getPermissions ? '✅' : '❌'}`);

        if (googleResults.errors.length > 0) {
            console.log('  ❌ Errors:');
            googleResults.errors.forEach(error => console.log(`    - ${error}`));
        }

    } catch (error) {
        console.error('❌ Google Drive API test failed:', error.message);
    }

    console.log('\n=== LARK DRIVE API TESTS ===');
    try {
        const larkAPI = new LarkDriveAPI();

        const larkResults = await larkAPI.testAPIs();

        console.log('📊 Lark Drive API Test Results:');
        console.log(`  ✅ Connection: ${larkResults.connection ? '✅' : '❌'}`);
        console.log(`  📁 Create Folder: ${larkResults.createFolder ? '✅' : '❌'}`);
        console.log(`  📤 Upload File: ${larkResults.uploadFile ? '✅' : '❌'}`);
        console.log(`  🔐 Set Permissions: ${larkResults.setPermissions ? '✅' : '❌'}`);

        if (larkResults.errors.length > 0) {
            console.log('  ❌ Errors:');
            larkResults.errors.forEach(error => console.log(`    - ${error}`));
        }

    } catch (error) {
        console.error('❌ Lark Drive API test failed:', error.message);
    }

    console.log('\n🏁 API tests completed!');
}

// Chạy tests
testAPIs().catch(console.error);