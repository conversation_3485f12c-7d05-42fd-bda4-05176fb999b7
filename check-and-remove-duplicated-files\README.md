# Duplicate File Remover

M<PERSON><PERSON> công cụ web để tìm và xóa các file trùng lặp trong thư mục.

## Tính năng

- ✅ Quét thư mục đệ quy để tìm file trùng lặp
- ✅ Phát hiện file trùng lặp dựa trên:
  - Tên file có pattern giống nhau (ví dụ: file_1.txt, file_2.txt)
  - Kích thước file giống nhau
  - Hash MD5 giống nhau
- ✅ Giao diện web thân thiện
- ✅ Theo dõi tiến độ real-time
- ✅ Báo cáo chi tiết các file trùng lặp
- ✅ Xóa file trùng lặp với báo cáo kết quả
- ✅ Xử lý lỗi và hiển thị nguyên nhân

## Cài đặt

1. Đảm bảo đã cài đặt Node.js (phiên bản 14 trở lên)
2. Mở terminal/command prompt tại thư mục dự án
3. Cài đặt dependencies:
   ```bash
   npm install
   ```

## Sử dụng

### Cách 1: Sử dụng npm
```bash
npm start
```

### Cách 2: Sử dụng Node.js trực tiếp
```bash
node server.js
```

### Cách 3: Sử dụng file batch (Windows)
Double-click vào file `start-server.bat`

Sau khi khởi động server, mở trình duyệt và truy cập: http://localhost:3000

## Hướng dẫn sử dụng

1. **Nhập đường dẫn thư mục**: Nhập đường dẫn đầy đủ đến thư mục cần quét
   - Ví dụ: `C:\Users\<USER>\Documents`
   - Ví dụ: `D:\Photos`

2. **Quét thư mục**: Click nút "Quét thư mục" để bắt đầu
   - Hệ thống sẽ hiển thị tiến độ real-time
   - Quá trình bao gồm: đếm file → quét thư mục → tính hash

3. **Xem kết quả**: Sau khi quét xong, hệ thống hiển thị:
   - Số lượng nhóm file trùng lặp
   - Chi tiết từng nhóm file trùng lặp
   - Thông tin file: tên, đường dẫn, kích thước

4. **Chọn file để xóa**: 
   - Sử dụng checkbox để chọn file cần xóa
   - Có thể "Chọn tất cả" hoặc "Bỏ chọn tất cả"

5. **Xóa file**: Click "Xóa file đã chọn"
   - Hệ thống sẽ hỏi xác nhận
   - Hiển thị tiến độ xóa real-time
   - Báo cáo kết quả: thành công/thất bại

## Cách hoạt động

### Thuật toán phát hiện file trùng lặp

1. **Quét thư mục đệ quy**: Duyệt tất cả thư mục con
2. **Nhóm file theo pattern**: Tìm các file có tên tương tự (ví dụ: `photo_1.jpg`, `photo_2.jpg`)
3. **So sánh kích thước**: Chỉ so sánh các file có cùng kích thước
4. **Tính hash MD5**: Tính hash để xác định file thực sự giống nhau
5. **Báo cáo kết quả**: Hiển thị các nhóm file trùng lặp

### Pattern nhận dạng file trùng lặp

File được coi là có thể trùng lặp nếu:
- Tên file có format: `basename_số.extension`
- Ví dụ: `document_1.pdf`, `document_2.pdf`, `document_3.pdf`
- Regex pattern: `/^(.+)_\d+(\.[^.]+)?$/`

## Cấu trúc dự án

```
check-and-remove-duplicated-files/
├── docs/
│   └── requirement.md          # Yêu cầu dự án
├── src/
│   ├── duplicateScanner.js     # Logic quét và phát hiện file trùng lặp
│   └── fileManager.js          # Logic quản lý và xóa file
├── public/
│   ├── index.html              # Giao diện web
│   ├── styles.css              # CSS styling
│   └── app.js                  # JavaScript frontend
├── server.js                   # Server Express.js
├── package.json                # Dependencies và scripts
├── start-server.bat            # Script khởi động (Windows)
└── README.md                   # Tài liệu này
```

## API Endpoints

### POST /api/scan
Quét thư mục để tìm file trùng lặp
- Body: `{ "directoryPath": "C:\\path\\to\\directory" }`
- Response: `{ "sessionId": "unique-session-id" }`

### POST /api/delete
Xóa các file đã chọn
- Body: `{ "filePaths": ["path1", "path2", ...] }`
- Response: `{ "sessionId": "unique-session-id" }`

## WebSocket Events

- `progress`: Tiến độ quét thư mục
- `complete`: Hoàn thành quét, trả về danh sách file trùng lặp
- `error`: Lỗi trong quá trình quét
- `deleteProgress`: Tiến độ xóa file
- `deleteComplete`: Hoàn thành xóa file
- `deleteError`: Lỗi trong quá trình xóa

## Lưu ý

- **Backup dữ liệu**: Luôn backup dữ liệu quan trọng trước khi xóa file
- **Quyền truy cập**: Đảm bảo ứng dụng có quyền đọc/ghi thư mục cần quét
- **Hiệu năng**: Quá trình quét có thể mất thời gian với thư mục lớn
- **Bảo mật**: Không sử dụng với thư mục hệ thống quan trọng

## Troubleshooting

### Server không khởi động được
- Kiểm tra Node.js đã được cài đặt: `node --version`
- Kiểm tra dependencies: `npm install`
- Kiểm tra port 3000 có bị chiếm dụng không

### Không quét được thư mục
- Kiểm tra đường dẫn thư mục có tồn tại không
- Kiểm tra quyền truy cập thư mục
- Kiểm tra thư mục có file nào không

### Lỗi xóa file
- Kiểm tra quyền ghi file
- Kiểm tra file có đang được sử dụng bởi ứng dụng khác không
- Kiểm tra file có thuộc tính read-only không

## License

MIT License
