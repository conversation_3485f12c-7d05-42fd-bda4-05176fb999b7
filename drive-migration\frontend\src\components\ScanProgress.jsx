import React, { useState, useEffect } from 'react';
import { apiPost } from '../utils/apiUtils';
import ErrorDisplay from './ErrorDisplay';

const ScanProgress = ({ scanSession, onCancel }) => {
  const [progress, setProgress] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(null);
  const [cancelError, setCancelError] = useState(null);

  useEffect(() => {
    if (scanSession) {
      calculateProgress();
    }
  }, [scanSession]);

  const calculateProgress = () => {
    if (!scanSession) return;

    // Calculate progress based on scanned files vs estimated total
    const scannedFiles = scanSession.scanned_files || 0;
    const totalFiles = scanSession.total_files || 0;

    if (totalFiles > 0) {
      setProgress((scannedFiles / totalFiles) * 100);
    } else {
      // If we don't have total files yet, show indeterminate progress
      setProgress(0);
    }

    // Calculate ETA
    if (scanSession.started_at && scannedFiles > 0) {
      const startTime = new Date(scanSession.started_at);
      const currentTime = new Date();
      const elapsedTime = currentTime - startTime;
      const filesPerMs = scannedFiles / elapsedTime;

      if (totalFiles > scannedFiles && filesPerMs > 0) {
        const remainingFiles = totalFiles - scannedFiles;
        const remainingTime = remainingFiles / filesPerMs;
        setEstimatedTimeRemaining(remainingTime);
      }
    }
  };

  const formatTime = (milliseconds) => {
    if (!milliseconds) return 'Calculating...';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const handleCancel = async () => {
    setCancelError(null);

    if (scanSession?.id) {
      try {
        await apiPost(`/api/scan/cancel/${scanSession.id}`);
      } catch (error) {
        console.error('Error cancelling scan:', error);
        setCancelError(error);
        return; // Don't call onCancel if there was an error
      }
    }
    onCancel();
  };

  if (!scanSession) {
    return (
      <div className="scan-progress">
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Initializing scan...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="scan-progress">
      <h2>🔍 Scanning Your Drive</h2>

      <div className="progress-overview">
        <div className="progress-bar-container">
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${Math.min(progress, 100)}%` }}
            ></div>
          </div>
          <div className="progress-text">
            {scanSession.status === 'completed' ? '100%' : `${Math.round(progress)}%`}
          </div>
        </div>

        <div className="scan-stats">
          <div className="stat-item">
            <div className="stat-value">{scanSession.scanned_files || 0}</div>
            <div className="stat-label">Files Scanned</div>
          </div>

          <div className="stat-item">
            <div className="stat-value">{scanSession.folders_processed || 0}</div>
            <div className="stat-label">Folders Processed</div>
          </div>

          <div className="stat-item">
            <div className="stat-value">{scanSession.current_depth || 0}</div>
            <div className="stat-label">Current Depth</div>
          </div>

          <div className="stat-item">
            <div className="stat-value">{formatFileSize(scanSession.total_size || 0)}</div>
            <div className="stat-label">Total Size</div>
          </div>
        </div>
      </div>

      <div className="scan-details">
        <div className="detail-row">
          <span className="detail-label">Status:</span>
          <span className={`detail-value status-${scanSession.status}`}>
            {scanSession.status === 'running' && '🔄 Scanning...'}
            {scanSession.status === 'completed' && '✅ Completed'}
            {scanSession.status === 'failed' && '❌ Failed'}
            {scanSession.status === 'cancelled' && '🛑 Cancelled'}
          </span>
        </div>

        <div className="detail-row">
          <span className="detail-label">Started:</span>
          <span className="detail-value">
            {scanSession.started_at ? new Date(scanSession.started_at).toLocaleString() : 'N/A'}
          </span>
        </div>

        {scanSession.status === 'running' && estimatedTimeRemaining && (
          <div className="detail-row">
            <span className="detail-label">Estimated Time Remaining:</span>
            <span className="detail-value">{formatTime(estimatedTimeRemaining)}</span>
          </div>
        )}

        {scanSession.scan_duration && (
          <div className="detail-row">
            <span className="detail-label">Duration:</span>
            <span className="detail-value">{formatTime(scanSession.scan_duration)}</span>
          </div>
        )}

        {scanSession.error_message && (
          <div className="detail-row error">
            <span className="detail-label">Error:</span>
            <span className="detail-value">{scanSession.error_message}</span>
          </div>
        )}
      </div>

      {cancelError && (
        <ErrorDisplay
          error={cancelError}
          title="Lỗi hủy quét"
          onDismiss={() => setCancelError(null)}
          onRetry={handleCancel}
          className="inline compact"
        />
      )}

      {scanSession.status === 'running' && (
        <div className="scan-actions">
          <button
            onClick={handleCancel}
            className="btn btn-secondary"
          >
            🛑 Hủy quét
          </button>
        </div>
      )}

      {scanSession.status === 'completed' && (
        <div className="scan-summary">
          <div className="summary-card">
            <h3>✅ Scan Completed Successfully!</h3>
            <p>
              Found <strong>{scanSession.total_files}</strong> files
              totaling <strong>{formatFileSize(scanSession.total_size)}</strong>
            </p>
            <p>
              Scanned <strong>{scanSession.folders_processed}</strong> folders
              with maximum depth of <strong>{scanSession.current_depth}</strong> levels
            </p>
          </div>
        </div>
      )}

      {scanSession.status === 'failed' && (
        <ErrorDisplay
          error={new Error(scanSession.error_message || 'Đã xảy ra lỗi không xác định trong quá trình quét.')}
          title="Quét thất bại"
          onRetry={onCancel}
          showDetails={true}
        />
      )}
    </div>
  );
};

export default ScanProgress;
