#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// <PERSON>àu sắc cho console
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Đọc file .env
function readEnvFile() {
    const envPath = path.join(__dirname, '..', '.env');

    if (!fs.existsSync(envPath)) {
        log('❌ File .env không tồn tại!', 'red');
        process.exit(1);
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
            const [key, ...valueParts] = trimmedLine.split('=');
            if (key && valueParts.length > 0) {
                envVars[key.trim()] = valueParts.join('=').trim();
            }
        }
    });

    return { envVars, envContent, envPath };
}

// Cập nhật file .env với tokens mới
function updateEnvFile(envPath, envContent, accessToken, refreshToken) {
    let updatedContent = envContent;

    // Cập nhật hoặc thêm LARK_ACCESS_TOKEN
    const accessTokenRegex = /^LARK_ACCESS_TOKEN=.*$/m;
    if (accessTokenRegex.test(updatedContent)) {
        updatedContent = updatedContent.replace(accessTokenRegex, `LARK_ACCESS_TOKEN=${accessToken}`);
    } else {
        // Tìm vị trí sau LARK_APP_SECRET để thêm token
        const insertAfter = /^LARK_APP_SECRET=.*$/m;
        if (insertAfter.test(updatedContent)) {
            updatedContent = updatedContent.replace(insertAfter, `$&\n\nLARK_ACCESS_TOKEN=${accessToken}`);
        } else {
            updatedContent += `\nLARK_ACCESS_TOKEN=${accessToken}`;
        }
    }

    // Cập nhật hoặc thêm LARK_REFRESH_TOKEN
    const refreshTokenRegex = /^LARK_REFRESH_TOKEN=.*$/m;
    if (refreshTokenRegex.test(updatedContent)) {
        updatedContent = updatedContent.replace(refreshTokenRegex, `LARK_REFRESH_TOKEN=${refreshToken}`);
    } else {
        // Thêm refresh token sau access token
        const insertAfter = /^LARK_ACCESS_TOKEN=.*$/m;
        if (insertAfter.test(updatedContent)) {
            updatedContent = updatedContent.replace(insertAfter, `$&\nLARK_REFRESH_TOKEN=${refreshToken}`);
        } else {
            updatedContent += `\nLARK_REFRESH_TOKEN=${refreshToken}`;
        }
    }

    fs.writeFileSync(envPath, updatedContent);
}

// Kiểm tra xem lark-user-token đã được cài đặt chưa
function checkLarkUserToken() {
    return new Promise((resolve) => {
        // Thử nhiều command khác nhau
        const commands = [
            ['lark-user-token', ['--version']],
            ['lark-user-token', ['--help']],
            ['npx', ['lark-user-token', '--version']]
        ];

        let checkIndex = 0;

        function tryNextCommand() {
            if (checkIndex >= commands.length) {
                resolve(false);
                return;
            }

            const [cmd, args] = commands[checkIndex];
            const child = spawn(cmd, args, {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve(true);
                } else {
                    checkIndex++;
                    tryNextCommand();
                }
            });

            child.on('error', (err) => {
                checkIndex++;
                tryNextCommand();
            });
        }

        tryNextCommand();
    });
}

// Cài đặt lark-user-token
function installLarkUserToken() {
    return new Promise((resolve, reject) => {
        log('📦 Đang cài đặt lark-user-token...', 'blue');

        const child = spawn('npm', ['install', '-g', 'lark-user-token'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });

        let output = '';
        let errorOutput = '';

        child.stdout.on('data', (data) => {
            const text = data.toString();
            output += text;
            console.log(text);
        });

        child.stderr.on('data', (data) => {
            const text = data.toString();
            errorOutput += text;
            console.error(text);
        });

        child.on('close', (code) => {
            if (code === 0) {
                log('✅ Đã cài đặt lark-user-token thành công!', 'green');
                resolve(output);
            } else {
                reject(new Error(`Cài đặt lark-user-token thất bại với code ${code}\n${errorOutput}`));
            }
        });

        child.on('error', (err) => {
            reject(new Error(`Lỗi khi cài đặt lark-user-token: ${err.message}`));
        });
    });
}

// Chạy lark-user-token command
function runLarkUserToken(appId, appSecret, redirectUri = 'http://localhost:3000/callback') {
    return new Promise((resolve, reject) => {
        log('🚀 Đang chạy lark-user-token...', 'blue');
        log(`📱 App ID: ${appId}`, 'cyan');
        log(`🔗 Redirect URI: ${redirectUri}`, 'cyan');

        // Thử nhiều cách chạy command
        const commandOptions = [
            ['lark-user-token', ['--a', appId, '--s', appSecret, '--r', redirectUri]],
            ['npx', ['lark-user-token', '--a', appId, '--s', appSecret, '--r', redirectUri]]
        ];

        let commandIndex = 0;

        function tryNextCommand() {
            if (commandIndex >= commandOptions.length) {
                reject(new Error('Không thể chạy lark-user-token bằng bất kỳ cách nào'));
                return;
            }

            const [cmd, args] = commandOptions[commandIndex];
            log(`🔄 Thử lệnh: ${cmd} ${args.join(' ')}`, 'cyan');

            const child = spawn(cmd, args, {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            let output = '';
            let errorOutput = '';

            child.stdout.on('data', (data) => {
                const text = data.toString();
                output += text;
                console.log(text);
            });

            child.stderr.on('data', (data) => {
                const text = data.toString();
                errorOutput += text;
                console.error(text);
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve(output);
                } else {
                    log(`❌ Lệnh thất bại với code ${code}`, 'red');
                    commandIndex++;
                    if (commandIndex < commandOptions.length) {
                        log('🔄 Thử cách khác...', 'yellow');
                        setTimeout(tryNextCommand, 1000);
                    } else {
                        reject(new Error(`Tất cả lệnh đều thất bại. Error cuối: ${errorOutput}`));
                    }
                }
            });

            child.on('error', (err) => {
                log(`❌ Lỗi chạy lệnh: ${err.message}`, 'red');
                commandIndex++;
                if (commandIndex < commandOptions.length) {
                    log('🔄 Thử cách khác...', 'yellow');
                    setTimeout(tryNextCommand, 1000);
                } else {
                    reject(new Error(`Tất cả lệnh đều có lỗi. Error cuối: ${err.message}`));
                }
            });
        }

        tryNextCommand();
    });
}

// Parse token từ output
function parseTokensFromOutput(output) {
    log('🔍 Đang phân tích output...', 'blue');
    log('Raw output:', 'yellow');
    console.log(output);

    // Thử nhiều pattern khác nhau
    const patterns = [
        // Format cụ thể từ lark-user-token output - ưu tiên User Access Token
        /(?:^|\n)Access Token:\s*([a-zA-Z0-9._-]+)/,
        // App Access Token fallback (nếu không tìm thấy User Access Token)
        /App Access Token:\s*([a-zA-Z0-9._-]+)/,
        // JSON format
        /"access_token"\s*:\s*"([^"]+)"/,
        // Key-value với dấu hai chấm
        /access_token\s*:\s*([a-zA-Z0-9_.-]+)/,
        // Key-value với dấu bằng  
        /access_token\s*=\s*([a-zA-Z0-9_.-]+)/,
        // Sau từ khóa access_token
        /access_token["\s]*[:=]["\s]*([a-zA-Z0-9_.-]+)/
    ];

    const refreshPatterns = [
        // Format cụ thể từ lark-user-token output
        /Refresh Token:\s*([a-zA-Z0-9._-]+)/,
        // JSON format
        /"refresh_token"\s*:\s*"([^"]+)"/,
        // Key-value với dấu hai chấm
        /refresh_token\s*:\s*([a-zA-Z0-9_.-]+)/,
        // Key-value với dấu bằng
        /refresh_token\s*=\s*([a-zA-Z0-9_.-]+)/,
        // Sau từ khóa refresh_token
        /refresh_token["\s]*[:=]["\s]*([a-zA-Z0-9_.-]+)/
    ];

    let accessToken = null;
    let refreshToken = null;

    // Tìm access token
    for (const pattern of patterns) {
        const match = output.match(pattern);
        if (match) {
            accessToken = match[1];
            break;
        }
    }

    // Tìm refresh token
    for (const pattern of refreshPatterns) {
        const match = output.match(pattern);
        if (match) {
            refreshToken = match[1];
            break;
        }
    }

    // Thử parse JSON nếu chưa tìm được
    if (!accessToken || !refreshToken) {
        try {
            // Tìm JSON block trong output
            const jsonMatches = output.match(/\{[^{}]*"access_token"[^{}]*\}/g);
            if (jsonMatches) {
                for (const jsonStr of jsonMatches) {
                    try {
                        const tokenData = JSON.parse(jsonStr);
                        if (tokenData.access_token && tokenData.refresh_token) {
                            accessToken = tokenData.access_token;
                            refreshToken = tokenData.refresh_token;
                            break;
                        }
                    } catch (e) {
                        // Ignore individual JSON parse errors
                    }
                }
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }

    if (!accessToken || !refreshToken) {
        log('❌ Không tìm thấy token trong output:', 'red');
        log('Output nhận được:', 'yellow');
        console.log(output);
        throw new Error('Không thể parse access_token hoặc refresh_token từ output. Vui lòng kiểm tra output ở trên.');
    }

    return {
        accessToken,
        refreshToken
    };
}

// Hỏi user có muốn tiếp tục không
function askConfirmation(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(`${colors.yellow}${question} (y/N): ${colors.reset}`, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
        });
    });
}

// Main function
async function main() {
    try {
        log('🔍 Đang đọc cấu hình từ file .env...', 'blue');

        const { envVars, envContent, envPath } = readEnvFile();

        // Kiểm tra LARK_APP_ID và LARK_APP_SECRET
        if (!envVars.LARK_APP_ID || !envVars.LARK_APP_SECRET) {
            log('❌ Không tìm thấy LARK_APP_ID hoặc LARK_APP_SECRET trong file .env', 'red');
            process.exit(1);
        }

        log('✅ Đã tìm thấy Lark app credentials:', 'green');
        log(`   App ID: ${envVars.LARK_APP_ID}`, 'cyan');
        log(`   App Secret: ${envVars.LARK_APP_SECRET.substring(0, 10)}...`, 'cyan');

        // Kiểm tra xem đã có token chưa
        if (envVars.LARK_ACCESS_TOKEN && envVars.LARK_REFRESH_TOKEN) {
            log('⚠️  Đã có LARK_ACCESS_TOKEN và LARK_REFRESH_TOKEN trong file .env', 'yellow');
            log('🔄 Sẽ lấy token mới và ghi đè...', 'blue');
        }

        log('\n📋 Đang mở trình duyệt để lấy token...', 'magenta');
        log('👆 Vui lòng đăng nhập Lark và cấp quyền cho app', 'magenta');

        // Kiểm tra và cài đặt lark-user-token nếu cần
        log('\n🔍 Đang kiểm tra lark-user-token...', 'blue');
        const isLarkUserTokenInstalled = await checkLarkUserToken();

        if (!isLarkUserTokenInstalled) {
            log('⚠️  lark-user-token chưa được cài đặt', 'yellow');
            const shouldInstall = await askConfirmation('Bạn có muốn cài đặt lark-user-token không?');

            if (!shouldInstall) {
                log('❌ Không thể tiếp tục mà không có lark-user-token', 'red');
                log('💡 Vui lòng cài đặt thủ công: npm install -g lark-user-token', 'yellow');
                process.exit(1);
            }

            try {
                await installLarkUserToken();

                // Đợi một chút để package được refresh trong PATH
                log('⏳ Đợi package được refresh...', 'blue');
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Kiểm tra lại sau khi cài đặt
                const isInstalledNow = await checkLarkUserToken();
                if (!isInstalledNow) {
                    log('⚠️  lark-user-token có thể đã được cài đặt nhưng chưa có trong PATH', 'yellow');
                    log('🔄 Sẽ thử chạy bằng npx...', 'blue');
                } else {
                    log('✅ lark-user-token đã sẵn sàng!', 'green');
                }
            } catch (installError) {
                log(`❌ Không thể cài đặt lark-user-token: ${installError.message}`, 'red');
                log('💡 Vui lòng cài đặt thủ công: npm install -g lark-user-token', 'yellow');
                process.exit(1);
            }
        } else {
            log('✅ lark-user-token đã được cài đặt', 'green');

            // Hiển thị version để confirm
            try {
                const versionOutput = await new Promise((resolve, reject) => {
                    const child = spawn('lark-user-token', ['--version'], {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        shell: true
                    });

                    let output = '';
                    child.stdout.on('data', (data) => output += data.toString());
                    child.on('close', (code) => {
                        if (code === 0) resolve(output.trim());
                        else reject(new Error('Cannot get version'));
                    });
                    child.on('error', reject);
                });

                log(`📦 Version: ${versionOutput}`, 'cyan');
            } catch (e) {
                // Ignore version check errors
            }
        }

        // Chạy lark-user-token
        const output = await runLarkUserToken(envVars.LARK_APP_ID, envVars.LARK_APP_SECRET);

        // Parse tokens
        log('\n🔍 Đang parse tokens từ output...', 'blue');
        const { accessToken, refreshToken } = parseTokensFromOutput(output);

        log('✅ Đã lấy được tokens:', 'green');
        log(`   Access Token: ${accessToken.substring(0, 20)}...`, 'cyan');
        log(`   Refresh Token: ${refreshToken.substring(0, 20)}...`, 'cyan');

        // Cập nhật file .env
        log('\n💾 Đang cập nhật file .env...', 'blue');
        updateEnvFile(envPath, envContent, accessToken, refreshToken);

        log('🎉 Hoàn thành! Tokens đã được cập nhật vào file .env', 'green');
        log('📝 Bạn có thể bắt đầu sử dụng Lark APIs ngay bây giờ', 'green');

    } catch (error) {
        log(`❌ Lỗi: ${error.message}`, 'red');

        if (error.message.includes('lark-user-token không được tìm thấy')) {
            log('\n💡 Hướng dẫn cài đặt:', 'yellow');
            log('   npm install -g lark-user-token', 'cyan');
        } else if (error.message.includes('Cài đặt lark-user-token thất bại')) {
            log('\n💡 Có thể thử:', 'yellow');
            log('   1. Chạy terminal với quyền Administrator', 'cyan');
            log('   2. Cài đặt thủ công: npm install -g lark-user-token', 'cyan');
            log('   3. Kiểm tra kết nối internet', 'cyan');
        } else if (error.message.includes('Không thể parse')) {
            log('\n💡 Output từ lark-user-token có thể không đúng format:', 'yellow');
            log('   1. Kiểm tra version lark-user-token: lark-user-token --version', 'cyan');
            log('   2. Chạy thủ công để xem output: lark-user-token --a <app-id> --s <app-secret> --r <redirect-uri>', 'cyan');
        }

        process.exit(1);
    }
}

// Chạy script
main().catch(error => {
    log(`❌ Lỗi không mong đợi: ${error.message}`, 'red');
    process.exit(1);
});

export {
    readEnvFile,
    updateEnvFile,
    checkLarkUserToken,
    installLarkUserToken,
    runLarkUserToken,
    parseTokensFromOutput
};
