## GOOGLE WORKSPACE DOMAIN-WIDE DELEGATION SETUP

### Client ID: 110056378231532510280

### Required <PERSON><PERSON><PERSON>opes (copy và paste chính xác):

```
https://www.googleapis.com/auth/admin.directory.user.readonly
https://www.googleapis.com/auth/admin.directory.user
https://www.googleapis.com/auth/admin.directory.group.readonly
https://www.googleapis.com/auth/drive
https://www.googleapis.com/auth/drive.file
https://www.googleapis.com/auth/drive.metadata
https://www.googleapis.com/auth/drive.readonly
```

### Setup Steps:

1. Go to: https://admin.google.com
2. Security → API Controls → Domain-wide delegation
3. Click "Add new"
4. Enter Client ID: 110056378231532510280
5. Copy và paste toàn bộ scopes ở trên (1 dòng, phân cách bằng dấu phẩy)
6. Save

### Alternative Minimal Scopes (if above doesn't work):

```
https://www.googleapis.com/auth/admin.directory.user.readonly,https://www.googleapis.com/auth/drive
```

### APIs to Enable in Google Cloud Console:

- Admin SDK API
- Google Drive API
- Google Workspace Domain-wide Delegation API

Project: osp-drive-migration-465915
