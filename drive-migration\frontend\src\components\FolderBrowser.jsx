import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import ErrorDisplay from './ErrorDisplay';

const FolderBrowser = ({ userEmail, onFolderSelected }) => {
  const [currentPath, setCurrentPath] = useState('/');
  const [currentFolderId, setCurrentFolderId] = useState('root');
  const [folders, setFolders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pathHistory, setPathHistory] = useState([{ id: 'root', name: 'My Drive', path: '/' }]);

  useEffect(() => {
    if (userEmail && userEmail !== 'ALL_USERS') {
      loadFolders(currentFolderId);
    }
  }, [currentFolderId, userEmail]);

  const loadFolders = async (folderId) => {
    if (!userEmail || userEmail === 'ALL_USERS') return;

    setLoading(true);
    setError(null);

    try {
      const data = await apiGet(`/api/folders/list?userEmail=${encodeURIComponent(userEmail)}&parentId=${folderId}`);
      setFolders(data.folders || []);

    } catch (err) {
      console.error('Error loading folders:', err);
      setError(err);
      setFolders([]);
    } finally {
      setLoading(false);
    }
  };

  const navigateToFolder = async (folder) => {
    try {
      // Resolve folder path
      const data = await apiGet(`/api/folders/resolve-id?userEmail=${encodeURIComponent(userEmail)}&folderId=${folder.id}`);

      const folderPath = data.path || `${currentPath}/${folder.name}`.replace(/\/+/g, '/');

      setCurrentFolderId(folder.id);
      setCurrentPath(folderPath);

      // Add to path history
      setPathHistory(prev => [...prev, {
        id: folder.id,
        name: folder.name,
        path: folderPath
      }]);

    } catch (err) {
      console.error('Error navigating to folder:', err);
      setError(err);
    }
  };

  const navigateToParent = () => {
    if (pathHistory.length > 1) {
      const newHistory = pathHistory.slice(0, -1);
      const parentFolder = newHistory[newHistory.length - 1];

      setPathHistory(newHistory);
      setCurrentFolderId(parentFolder.id);
      setCurrentPath(parentFolder.path);
    }
  };

  const navigateToBreadcrumb = (index) => {
    if (index < pathHistory.length - 1) {
      const newHistory = pathHistory.slice(0, index + 1);
      const targetFolder = newHistory[newHistory.length - 1];

      setPathHistory(newHistory);
      setCurrentFolderId(targetFolder.id);
      setCurrentPath(targetFolder.path);
    }
  };

  const selectCurrentFolder = () => {
    const currentFolder = pathHistory[pathHistory.length - 1];
    onFolderSelected({
      id: currentFolder.id,
      name: currentFolder.name,
      path: currentFolder.path
    });
  };

  if (!userEmail) {
    return (
      <div className="folder-browser">
        <p className="info-message">Please select a user to browse folders</p>
      </div>
    );
  }

  if (userEmail === 'ALL_USERS') {
    return (
      <div className="folder-browser">
        <p className="info-message">📁 Folder browsing is not available when "All Users" is selected. The scan will include all accessible files from all users.</p>
      </div>
    );
  }

  return (
    <div className="folder-browser">
      <div className="browser-header">
        <div className="breadcrumb">
          {pathHistory.map((folder, index) => (
            <span key={folder.id} className="breadcrumb-item">
              {index > 0 && <span className="breadcrumb-separator">/</span>}
              <button
                onClick={() => navigateToBreadcrumb(index)}
                className={`breadcrumb-link ${index === pathHistory.length - 1 ? 'current' : ''}`}
                disabled={index === pathHistory.length - 1}
              >
                {folder.name}
              </button>
            </span>
          ))}
        </div>

        <div className="browser-actions">
          <button
            onClick={navigateToParent}
            disabled={pathHistory.length <= 1}
            className="btn btn-secondary btn-small"
          >
            ⬆️ Up
          </button>

          <button
            onClick={selectCurrentFolder}
            className="btn btn-primary btn-small"
            disabled={currentFolderId === 'root'}
          >
            ✅ Select This Folder
          </button>
        </div>
      </div>

      <div className="folder-list">
        {loading && (
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Loading folders...</p>
          </div>
        )}

        {error && (
          <ErrorDisplay
            error={error}
            title="Lỗi tải danh sách thư mục"
            onRetry={() => loadFolders(currentFolderId)}
            onDismiss={() => setError(null)}
            className="inline compact"
          />
        )}

        {!loading && !error && folders.length === 0 && (
          <div className="empty-state">
            <p>📁 No folders found in this directory</p>
          </div>
        )}

        {!loading && !error && folders.length > 0 && (
          <div className="folders-grid">
            {folders.map((folder) => (
              <div
                key={folder.id}
                className="folder-item"
                onClick={() => navigateToFolder(folder)}
              >
                <div className="folder-icon">📁</div>
                <div className="folder-info">
                  <div className="folder-name">{folder.name}</div>
                  <div className="folder-meta">
                    {folder.modifiedTime && (
                      <span className="folder-date">
                        Modified: {new Date(folder.modifiedTime).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
                <div className="folder-arrow">➡️</div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="browser-footer">
        <div className="current-selection">
          <strong>Current folder:</strong> {currentPath}
        </div>
      </div>
    </div>
  );
};

export default FolderBrowser;
