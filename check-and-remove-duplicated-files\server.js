const express = require('express');
const cors = require('cors');
const path = require('path');
const WebSocket = require('ws');
const http = require('http');
const duplicateScanner = require('./src/duplicateScanner');
const fileManager = require('./src/fileManager');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' })); // Increase payload limit
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(express.static('public'));

// Store active scan sessions
const activeSessions = new Map();
const activeScanners = new Map();
const duplicateResults = new Map(); // Store duplicate results on server

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('Client connected');
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            if (data.type === 'register') {
                activeSessions.set(data.sessionId, ws);
            }
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    });
    
    ws.on('close', () => {
        console.log('Client disconnected');
        // Remove from active sessions
        for (const [sessionId, socket] of activeSessions.entries()) {
            if (socket === ws) {
                activeSessions.delete(sessionId);
                break;
            }
        }
    });
});

// API Routes
app.post('/api/scan', async (req, res) => {
    try {
        const { directoryPath, maxWorkers = 4 } = req.body;
        const sessionId = Date.now().toString();

        if (!directoryPath) {
            return res.status(400).json({ error: 'Directory path is required' });
        }

        const workers = Math.max(1, Math.min(parseInt(maxWorkers) || 4, 16)); // Limit to 1-16 workers

        // Start scanning in background
        const progressCallback = (progress) => {
            const ws = activeSessions.get(sessionId);
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'progress',
                    data: progress
                }));
            }
        };

        // Store scanner instance for control
        activeScanners.set(sessionId, duplicateScanner);

        duplicateScanner.scanDirectory(directoryPath, progressCallback, workers)
            .then(duplicates => {
                // Store results on server
                duplicateResults.set(sessionId, duplicates);

                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'complete',
                        data: { sessionId, count: duplicates.length }
                    }));
                }
                activeScanners.delete(sessionId);
            })
            .catch(error => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'error',
                        data: { message: error.message }
                    }));
                }
                activeScanners.delete(sessionId);
            });

        res.json({ sessionId, workers });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/delete', async (req, res) => {
    try {
        const { filePaths } = req.body;
        const sessionId = Date.now().toString();
        
        if (!filePaths || !Array.isArray(filePaths)) {
            return res.status(400).json({ error: 'File paths array is required' });
        }

        const progressCallback = (progress) => {
            const ws = activeSessions.get(sessionId);
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'deleteProgress',
                    data: progress
                }));
            }
        };

        fileManager.deleteFiles(filePaths, progressCallback)
            .then(result => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'deleteComplete',
                        data: result
                    }));
                }
            })
            .catch(error => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'deleteError',
                        data: { message: error.message }
                    }));
                }
            });

        res.json({ sessionId });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get duplicate results
app.get('/api/duplicates/:sessionId', (req, res) => {
    try {
        const { sessionId } = req.params;
        const duplicates = duplicateResults.get(sessionId);

        if (duplicates) {
            res.json({ duplicates });
        } else {
            res.status(404).json({ error: 'Results not found' });
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Delete all duplicates (keep originals)
app.post('/api/delete-all-duplicates/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        const duplicates = duplicateResults.get(sessionId);

        if (!duplicates) {
            return res.status(404).json({ error: 'Results not found' });
        }

        // Collect all duplicate files (not original files)
        const duplicateFiles = [];
        duplicates.forEach(group => {
            group.files.forEach(file => {
                if (!file.isOriginal) {
                    duplicateFiles.push(file.path);
                }
            });
        });

        if (duplicateFiles.length === 0) {
            return res.json({ message: 'No duplicate files to delete' });
        }

        const progressCallback = (progress) => {
            const ws = activeSessions.get(sessionId);
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'deleteProgress',
                    data: progress
                }));
            }
        };

        fileManager.deleteFiles(duplicateFiles, progressCallback)
            .then(result => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'deleteComplete',
                        data: result
                    }));
                }
            })
            .catch(error => {
                const ws = activeSessions.get(sessionId);
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'deleteError',
                        data: { message: error.message }
                    }));
                }
            });

        res.json({
            message: `Starting deletion of ${duplicateFiles.length} duplicate files`
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Scan control endpoints
app.post('/api/scan/pause', (req, res) => {
    try {
        const { sessionId } = req.body;
        const scanner = activeScanners.get(sessionId);

        if (scanner) {
            scanner.pauseScan();
            res.json({ success: true, message: 'Scan paused' });
        } else {
            res.status(404).json({ error: 'Scanner not found' });
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/scan/resume', (req, res) => {
    try {
        const { sessionId } = req.body;
        const scanner = activeScanners.get(sessionId);

        if (scanner) {
            scanner.resumeScan();
            res.json({ success: true, message: 'Scan resumed' });
        } else {
            res.status(404).json({ error: 'Scanner not found' });
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/scan/stop', (req, res) => {
    try {
        const { sessionId } = req.body;
        const scanner = activeScanners.get(sessionId);

        if (scanner) {
            scanner.stopScan();
            activeScanners.delete(sessionId);
            res.json({ success: true, message: 'Scan stopped' });
        } else {
            res.status(404).json({ error: 'Scanner not found' });
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

const PORT = process.env.PORT || 3003;

// Error handling
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Open http://localhost:${PORT} to use the application`);
}).on('error', (error) => {
    console.error('Server error:', error);
});
