/**
 * Database Mock
 * Mock implementation cho Supabase database operations để testing
 */

import { TestUtils, mockData } from '../test-config.js';

export class DatabaseMock {
    constructor() {
        this.tables = {
            scan_sessions: new Map(),
            scanned_files: new Map(),
            migration_tasks: new Map(),
            migration_items: new Map(),
            user_mappings: new Map()
        };
        this.callHistory = [];
        
        // Initialize với mock data
        this.initializeMockData();
    }
    
    initializeMockData() {
        // Tạo một số mock scan sessions
        for (let i = 0; i < 3; i++) {
            const session = TestUtils.createMockScanSession();
            this.tables.scan_sessions.set(session.id, session);
        }
        
        // Tạo một số mock migration tasks
        for (let i = 0; i < 2; i++) {
            const task = TestUtils.createMockMigrationTask();
            this.tables.migration_tasks.set(task.id, task);
        }
    }
    
    // Mock Supabase client methods
    from(tableName) {
        return new MockQueryBuilder(this, tableName);
    }
    
    // Internal methods
    _insert(tableName, data) {
        this.callHistory.push({ method: 'insert', tableName, data });
        
        if (!this.tables[tableName]) {
            throw new Error(`Table ${tableName} not found`);
        }
        
        // Generate appropriate ID based on table
        let id;
        if (tableName === 'scanned_files') {
            // Use auto-incrementing integer for scanned_files
            const existingIds = Array.from(this.tables[tableName].keys())
                .filter(k => typeof k === 'number')
                .sort((a, b) => b - a);
            id = data.id || (existingIds.length > 0 ? existingIds[0] + 1 : 1);
        } else {
            id = data.id || TestUtils.generateUUID();
        }

        const record = {
            id: id,
            ...data,
            created_at: data.created_at || new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        this.tables[tableName].set(record.id, record);
        return { data: [record], error: null };
    }
    
    _select(tableName, columns = '*', filters = {}, options = {}) {
        this.callHistory.push({ method: 'select', tableName, columns, filters, options });
        
        if (!this.tables[tableName]) {
            throw new Error(`Table ${tableName} not found`);
        }
        
        let records = Array.from(this.tables[tableName].values());
        
        // Apply filters
        for (const [key, value] of Object.entries(filters)) {
            if (key === 'eq') {
                const [field, filterValue] = value;
                records = records.filter(record => record[field] === filterValue);
            } else if (key === 'neq') {
                const [field, filterValue] = value;
                records = records.filter(record => record[field] !== filterValue);
            } else if (key === 'in') {
                const [field, values] = value;
                records = records.filter(record => values.includes(record[field]));
            } else if (key === 'ilike') {
                const [field, pattern] = value;
                const regex = new RegExp(pattern.replace(/%/g, '.*'), 'i');
                records = records.filter(record => regex.test(record[field]));
            } else if (key === 'gte') {
                const [field, filterValue] = value;
                records = records.filter(record => record[field] >= filterValue);
            } else if (key === 'lte') {
                const [field, filterValue] = value;
                records = records.filter(record => record[field] <= filterValue);
            }
        }
        
        // Apply sorting
        if (options.order) {
            const [field, direction] = options.order;
            records.sort((a, b) => {
                if (direction === 'desc') {
                    return b[field] > a[field] ? 1 : -1;
                }
                return a[field] > b[field] ? 1 : -1;
            });
        }
        
        // Apply pagination
        if (options.range) {
            const [start, end] = options.range;
            records = records.slice(start, end + 1);
        }
        
        // Apply limit
        if (options.limit) {
            records = records.slice(0, options.limit);
        }
        
        return {
            data: records,
            error: null,
            count: this.tables[tableName].size
        };
    }
    
    _update(tableName, data, filters = {}) {
        this.callHistory.push({ method: 'update', tableName, data, filters });
        
        if (!this.tables[tableName]) {
            throw new Error(`Table ${tableName} not found`);
        }
        
        let records = Array.from(this.tables[tableName].values());
        
        // Apply filters để tìm records cần update
        for (const [key, value] of Object.entries(filters)) {
            if (key === 'eq') {
                const [field, filterValue] = value;
                records = records.filter(record => record[field] === filterValue);
            }
        }
        
        // Update records
        const updatedRecords = [];
        for (const record of records) {
            const updatedRecord = {
                ...record,
                ...data,
                updated_at: new Date().toISOString()
            };
            this.tables[tableName].set(record.id, updatedRecord);
            updatedRecords.push(updatedRecord);
        }
        
        return { data: updatedRecords, error: null };
    }
    
    _delete(tableName, filters = {}) {
        this.callHistory.push({ method: 'delete', tableName, filters });
        
        if (!this.tables[tableName]) {
            throw new Error(`Table ${tableName} not found`);
        }
        
        let records = Array.from(this.tables[tableName].values());
        
        // Apply filters để tìm records cần delete
        for (const [key, value] of Object.entries(filters)) {
            if (key === 'eq') {
                const [field, filterValue] = value;
                records = records.filter(record => record[field] === filterValue);
            }
        }
        
        // Delete records
        const deletedRecords = [];
        for (const record of records) {
            this.tables[tableName].delete(record.id);
            deletedRecords.push(record);
        }
        
        return { data: deletedRecords, error: null };
    }
    
    // Utility methods cho testing
    addMockRecord(tableName, record) {
        if (!this.tables[tableName]) {
            this.tables[tableName] = new Map();
        }
        this.tables[tableName].set(record.id, record);
    }
    
    getCallHistory() {
        return [...this.callHistory];
    }
    
    clearCallHistory() {
        this.callHistory = [];
    }
    
    reset() {
        for (const table of Object.values(this.tables)) {
            table.clear();
        }
        this.callHistory = [];
        this.initializeMockData();
    }
    
    // Simulate database errors
    simulateError(method, error) {
        const originalMethod = this[`_${method}`];
        this[`_${method}`] = (...args) => {
            throw error;
        };
        
        return () => {
            this[`_${method}`] = originalMethod;
        };
    }
}

class MockQueryBuilder {
    constructor(db, tableName) {
        this.db = db;
        this.tableName = tableName;
        this.filters = {};
        this.options = {};
    }
    
    select(columns = '*', options = {}) {
        this.columns = columns;
        if (options.count) {
            this.options.count = options.count;
        }
        return this;
    }
    
    insert(data) {
        const result = this.db._insert(this.tableName, data);
        return {
            ...result,
            select: () => result
        };
    }
    
    update(data) {
        const result = this.db._update(this.tableName, data, this.filters);
        return {
            ...result,
            select: () => result
        };
    }
    
    delete() {
        return this.db._delete(this.tableName, this.filters);
    }
    
    eq(field, value) {
        this.filters.eq = [field, value];
        return this;
    }
    
    neq(field, value) {
        this.filters.neq = [field, value];
        return this;
    }
    
    in(field, values) {
        this.filters.in = [field, values];
        return this;
    }
    
    ilike(field, pattern) {
        this.filters.ilike = [field, pattern];
        return this;
    }
    
    gte(field, value) {
        this.filters.gte = [field, value];
        return this;
    }
    
    lte(field, value) {
        this.filters.lte = [field, value];
        return this;
    }
    
    order(field, options = {}) {
        this.options.order = [field, options.ascending === false ? 'desc' : 'asc'];
        return this;
    }
    
    range(start, end) {
        this.options.range = [start, end];
        return this;
    }
    
    limit(count) {
        this.options.limit = count;
        return this;
    }
    
    single() {
        const result = this.db._select(this.tableName, this.columns, this.filters, this.options);
        return {
            data: result.data[0] || null,
            error: result.data.length === 0 ? new Error('No rows found') : null
        };
    }
    
    // Execute query
    then(callback) {
        const result = this.db._select(this.tableName, this.columns, this.filters, this.options);
        return callback(result);
    }
}

// Export singleton instance
export const databaseMock = new DatabaseMock();

export default DatabaseMock;
