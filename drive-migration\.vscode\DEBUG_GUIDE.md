# VSCode Debug Guide for Drive Migration Project

## C<PERSON><PERSON> hình Debug đã được thiết lập

### 🚀 Debug Configurations (F5)

1. **Debug Server** - Debug server chính
2. **Debug Domain Delegation Check** - Debug kiểm tra domain delegation
3. **Debug Troubleshoot Delegation** - Debug troubleshoot delegation
4. **Debug Google Auth Test** - Debug test Google authentication
5. **Debug Database Setup** - Debug thiết lập database
6. **Debug Infrastructure Check** - Debug kiểm tra infrastructure
7. **Debug All Tests** - Debug tất cả tests
8. **Debug Current File** - Debug file hiện tại
9. **Attach to Process** - Attach vào process đang chạy

### 🛠️ Tasks (Ctrl+Shift+P → "Tasks: Run Task")

1. **Start Server** - Khởi động server
2. **Check Domain Delegation** - Ki<PERSON>m tra domain delegation
3. **Troubleshoot Delegation** - Troubleshoot delegation issues
4. **Run All Tests** - Ch<PERSON>y tất cả tests
5. **Check Database Setup** - Kiểm tra database setup
6. **Infrastructure Check** - Kiểm tra infrastructure
7. **Install Dependencies** - Cài đặt dependencies
8. **Test Google Auth** - Test Google authentication

## 🔧 Cách sử dụng Debug

### 1. Debug cơ bản
- Mở file cần debug
- Đặt breakpoint (F9)
- Nhấn F5 và chọn configuration phù hợp
- Hoặc chọn "Debug Current File" để debug file hiện tại

### 2. Debug Domain Delegation Issues
```bash
# Chọn "Debug Domain Delegation Check" hoặc
# Chọn "Debug Troubleshoot Delegation"
```

### 3. Debug Server
```bash
# Chọn "Debug Server"
# Server sẽ khởi động ở mode debug
# Có thể đặt breakpoint trong các route handlers
```

### 4. Debug Tests
```bash
# Chọn "Debug All Tests" để debug tất cả tests
# Hoặc "Debug Google Auth Test" cho test cụ thể
```

### 5. Attach to Running Process
```bash
# Khởi động process với debug flag:
node --inspect=9229 src/server.js

# Sau đó chọn "Attach to Process" trong VSCode
```

## 🎯 Debug Tips

### Breakpoints
- **F9**: Toggle breakpoint
- **Ctrl+Shift+F9**: Toggle conditional breakpoint
- **Shift+F9**: Toggle inline breakpoint

### Debug Controls
- **F5**: Continue/Start debugging
- **F10**: Step over
- **F11**: Step into
- **Shift+F11**: Step out
- **Ctrl+Shift+F5**: Restart debugging
- **Shift+F5**: Stop debugging

### Debug Console
- Có thể chạy JavaScript expressions
- Truy cập variables trong scope hiện tại
- Sử dụng `console.log()` để output

### Watch Variables
- Thêm variables vào Watch panel
- Monitor giá trị thay đổi theo thời gian

## 🐛 Common Debug Scenarios

### 1. Debug Domain Delegation
```javascript
// Đặt breakpoint trong google-auth.js
// Tại method testConnection() line 218
// Kiểm tra giá trị result.connectedAs vs userEmail
```

### 2. Debug API Calls
```javascript
// Đặt breakpoint trong API handlers
// Kiểm tra request/response data
// Monitor error handling
```

### 3. Debug Database Operations
```javascript
// Đặt breakpoint trong database service
// Kiểm tra SQL queries
// Monitor connection status
```

### 4. Debug Authentication Flow
```javascript
// Đặt breakpoint trong auth methods
// Kiểm tra token generation/validation
// Monitor scope permissions
```

## 📝 Environment Variables

Debug configurations sẽ tự động load:
- `.env` files
- Environment variables từ system
- NODE_ENV được set thành "development"

## 🔍 Troubleshooting Debug Issues

### 1. Nếu debug không hoạt động:
- Kiểm tra Node.js version (>= 14)
- Đảm bảo có quyền đọc file
- Restart VSCode

### 2. Nếu breakpoint không hit:
- Kiểm tra file path
- Đảm bảo code được execute
- Kiểm tra source maps

### 3. Nếu variables không hiển thị:
- Kiểm tra scope
- Đảm bảo variable đã được declare
- Refresh debug session

## 📚 Useful Extensions

Các extension được recommend trong `.vscode/extensions.json`:
- **Node.js Extension Pack** - Debug tools cho Node.js
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Path Intellisense** - Auto-complete paths
- **GitHub Copilot** - AI assistance

## 🎉 Happy Debugging!

Với cấu hình này, bạn có thể debug hiệu quả mọi phần của Drive Migration project.
