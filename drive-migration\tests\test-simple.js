import dotenv from 'dotenv';
import fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config();

console.log('🔍 Testing Environment Configuration...\n');

// Test Google credentials from JSON file
console.log('=== GOOGLE CONFIGURATION ===');
const keyPath = join(__dirname, '..', 'google-service-account.json');
try {
    if (fs.existsSync(keyPath)) {
        const credentials = JSON.parse(fs.readFileSync(keyPath, 'utf8'));
        console.log('google-service-account.json:', '✅ Found');
        console.log('client_email:', credentials.client_email ? '✅ Set' : '❌ Missing');
        console.log('private_key:', credentials.private_key ? '✅ Set' : '❌ Missing');
        console.log('project_id:', credentials.project_id ? '✅ Set' : '❌ Missing');
        console.log('client_id:', credentials.client_id ? '✅ Set' : '❌ Missing');
    } else {
        console.log('google-service-account.json:', '❌ Not found');
    }
} catch (error) {
    console.log('google-service-account.json:', '❌ Invalid JSON');
}

// Test Lark credentials
console.log('\n=== LARK CONFIGURATION ===');
console.log('LARK_APP_ID:', process.env.LARK_APP_ID ? '✅ Set' : '❌ Missing');
console.log('LARK_APP_SECRET:', process.env.LARK_APP_SECRET ? '✅ Set' : '❌ Missing');

// Test Supabase credentials
console.log('\n=== SUPABASE CONFIGURATION ===');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing');

// Test other config
console.log('\n=== APPLICATION CONFIGURATION ===');
console.log('NODE_ENV:', process.env.NODE_ENV || 'development');
console.log('PORT:', process.env.PORT || '3000');
console.log('LOG_LEVEL:', process.env.LOG_LEVEL || 'info');

console.log('\n✅ Environment configuration check completed!');

// Test basic imports
console.log('\n🔍 Testing Basic Imports...\n');

try {
    console.log('Testing googleapis import...');
    const { google } = await import('googleapis');
    console.log('✅ googleapis imported successfully');
} catch (error) {
    console.log('❌ googleapis import failed:', error.message);
}

try {
    console.log('Testing @supabase/supabase-js import...');
    const { createClient } = await import('@supabase/supabase-js');
    console.log('✅ @supabase/supabase-js imported successfully');
} catch (error) {
    console.log('❌ @supabase/supabase-js import failed:', error.message);
}

try {
    console.log('Testing jsonwebtoken import...');
    const jwt = await import('jsonwebtoken');
    console.log('✅ jsonwebtoken imported successfully');
} catch (error) {
    console.log('❌ jsonwebtoken import failed:', error.message);
}

console.log('\n✅ Basic imports test completed!');
