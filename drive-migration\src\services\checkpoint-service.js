/**
 * Checkpoint Service - <PERSON><PERSON> thống checkpoint và recovery
 * <PERSON><PERSON><PERSON> trạng thái migration để có thể khôi phục sau khi restart
 */

import fs from 'fs/promises';
import path from 'path';
import { supabaseClient } from '../database/supabase.js';

class CheckpointService {
    constructor() {
        this.checkpointsDir = path.join(process.cwd(), 'checkpoints');
        this.ensureCheckpointsDirectory();
    }

    async ensureCheckpointsDirectory() {
        try {
            await fs.mkdir(this.checkpointsDir, { recursive: true });
        } catch (error) {
            console.error('Error creating checkpoints directory:', error);
        }
    }

    /**
     * Tạo checkpoint cho migration task
     */
    async createCheckpoint(migrationTaskId, checkpointData) {
        try {
            console.log(`💾 Creating checkpoint for migration task ${migrationTaskId}`);

            const checkpoint = {
                migration_task_id: migrationTaskId,
                checkpoint_id: `checkpoint_${migrationTaskId}_${Date.now()}`,
                created_at: new Date().toISOString(),
                data: {
                    ...checkpointData,
                    timestamp: Date.now(),
                    version: '1.0'
                }
            };

            // Save to database
            const dbResult = await this.saveCheckpointToDatabase(checkpoint);
            
            // Save to local file as backup
            const fileResult = await this.saveCheckpointToFile(checkpoint);

            if (dbResult.success && fileResult.success) {
                console.log(`✅ Checkpoint created successfully: ${checkpoint.checkpoint_id}`);
                return {
                    success: true,
                    checkpointId: checkpoint.checkpoint_id,
                    data: checkpoint
                };
            } else {
                throw new Error('Failed to save checkpoint');
            }

        } catch (error) {
            console.error('❌ Error creating checkpoint:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Lưu checkpoint vào database
     */
    async saveCheckpointToDatabase(checkpoint) {
        try {
            // Update migration task với checkpoint data
            const { data, error } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .update({
                    checkpoint_data: checkpoint.data,
                    updated_at: new Date().toISOString()
                })
                .eq('id', checkpoint.migration_task_id)
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Lưu checkpoint vào file
     */
    async saveCheckpointToFile(checkpoint) {
        try {
            const filename = `${checkpoint.checkpoint_id}.json`;
            const filepath = path.join(this.checkpointsDir, filename);
            
            await fs.writeFile(filepath, JSON.stringify(checkpoint, null, 2));
            
            return { success: true, filepath };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Khôi phục migration từ checkpoint
     */
    async recoverFromCheckpoint(migrationTaskId) {
        try {
            console.log(`🔄 Recovering migration task ${migrationTaskId} from checkpoint`);

            // Try to load from database first
            let checkpoint = await this.loadCheckpointFromDatabase(migrationTaskId);
            
            // If not found in database, try file backup
            if (!checkpoint.success) {
                checkpoint = await this.loadCheckpointFromFile(migrationTaskId);
            }

            if (!checkpoint.success) {
                return {
                    success: false,
                    error: 'No checkpoint found for this migration task'
                };
            }

            const checkpointData = checkpoint.data;
            
            // Validate checkpoint data
            const validation = this.validateCheckpoint(checkpointData);
            if (!validation.valid) {
                return {
                    success: false,
                    error: `Invalid checkpoint: ${validation.error}`
                };
            }

            // Analyze what needs to be recovered
            const recoveryPlan = await this.createRecoveryPlan(migrationTaskId, checkpointData);
            
            console.log(`✅ Recovery plan created for ${migrationTaskId}:`);
            console.log(`   📊 Total items: ${recoveryPlan.totalItems}`);
            console.log(`   ✅ Completed: ${recoveryPlan.completedItems}`);
            console.log(`   🔄 To resume: ${recoveryPlan.itemsToResume}`);
            console.log(`   ❌ Failed (retry): ${recoveryPlan.failedItems}`);

            return {
                success: true,
                checkpointData,
                recoveryPlan,
                message: 'Checkpoint loaded successfully'
            };

        } catch (error) {
            console.error('❌ Error recovering from checkpoint:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Load checkpoint từ database
     */
    async loadCheckpointFromDatabase(migrationTaskId) {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .select('checkpoint_data')
                .eq('id', migrationTaskId)
                .single();

            if (error) throw error;

            if (!data.checkpoint_data) {
                return {
                    success: false,
                    error: 'No checkpoint data found in database'
                };
            }

            return {
                success: true,
                data: data.checkpoint_data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Load checkpoint từ file backup
     */
    async loadCheckpointFromFile(migrationTaskId) {
        try {
            const files = await fs.readdir(this.checkpointsDir);
            const checkpointFiles = files.filter(file => 
                file.includes(migrationTaskId) && file.endsWith('.json')
            );

            if (checkpointFiles.length === 0) {
                return {
                    success: false,
                    error: 'No checkpoint files found'
                };
            }

            // Get the most recent checkpoint file
            const latestFile = checkpointFiles.sort().pop();
            const filepath = path.join(this.checkpointsDir, latestFile);
            
            const content = await fs.readFile(filepath, 'utf8');
            const checkpoint = JSON.parse(content);

            return {
                success: true,
                data: checkpoint.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Validate checkpoint data
     */
    validateCheckpoint(checkpointData) {
        try {
            // Check required fields
            const requiredFields = ['timestamp', 'version'];
            for (const field of requiredFields) {
                if (!checkpointData[field]) {
                    return {
                        valid: false,
                        error: `Missing required field: ${field}`
                    };
                }
            }

            // Check timestamp is not too old (24 hours)
            const maxAge = 24 * 60 * 60 * 1000; // 24 hours
            const age = Date.now() - checkpointData.timestamp;
            if (age > maxAge) {
                return {
                    valid: false,
                    error: 'Checkpoint is too old (>24 hours)'
                };
            }

            return { valid: true };
        } catch (error) {
            return {
                valid: false,
                error: error.message
            };
        }
    }

    /**
     * Tạo recovery plan từ checkpoint
     */
    async createRecoveryPlan(migrationTaskId, checkpointData) {
        try {
            // Get current state of migration items
            const { data: items, error } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .select('*')
                .eq('migration_task_id', migrationTaskId);

            if (error) throw error;

            const totalItems = items.length;
            const completedItems = items.filter(item => item.status === 'completed').length;
            const failedItems = items.filter(item => item.status === 'failed').length;
            const pendingItems = items.filter(item => item.status === 'pending').length;
            const inProgressItems = items.filter(item => 
                ['downloading', 'uploading'].includes(item.status)
            ).length;

            // Items to resume = failed + in-progress + pending
            const itemsToResume = failedItems + inProgressItems + pendingItems;

            const recoveryPlan = {
                totalItems,
                completedItems,
                failedItems,
                pendingItems,
                inProgressItems,
                itemsToResume,
                checkpointAge: Date.now() - checkpointData.timestamp,
                resumeFromItem: checkpointData.currentItemIndex || 0,
                batchSize: checkpointData.batchSize || 10,
                retryFailedItems: true
            };

            return recoveryPlan;
        } catch (error) {
            console.error('Error creating recovery plan:', error);
            return {
                totalItems: 0,
                completedItems: 0,
                failedItems: 0,
                itemsToResume: 0,
                error: error.message
            };
        }
    }

    /**
     * Resume migration từ checkpoint
     */
    async resumeMigration(migrationTaskId, recoveryPlan) {
        try {
            console.log(`🚀 Resuming migration ${migrationTaskId}`);

            // Reset in-progress items to pending
            await this.resetInProgressItems(migrationTaskId);

            // Update migration task status
            await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .update({
                    status: 'running',
                    updated_at: new Date().toISOString()
                })
                .eq('id', migrationTaskId);

            console.log(`✅ Migration ${migrationTaskId} resumed successfully`);
            
            return {
                success: true,
                message: 'Migration resumed from checkpoint',
                recoveryPlan
            };
        } catch (error) {
            console.error('❌ Error resuming migration:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Reset in-progress items về pending để retry
     */
    async resetInProgressItems(migrationTaskId) {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .update({
                    status: 'pending',
                    updated_at: new Date().toISOString()
                })
                .eq('migration_task_id', migrationTaskId)
                .in('status', ['downloading', 'uploading']);

            if (error) throw error;

            console.log(`🔄 Reset ${data?.length || 0} in-progress items to pending`);
            return { success: true, count: data?.length || 0 };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cleanup old checkpoints
     */
    async cleanupOldCheckpoints(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
        try {
            const files = await fs.readdir(this.checkpointsDir);
            let deletedCount = 0;

            for (const file of files) {
                if (!file.endsWith('.json')) continue;

                const filepath = path.join(this.checkpointsDir, file);
                const stats = await fs.stat(filepath);
                const age = Date.now() - stats.mtime.getTime();

                if (age > maxAge) {
                    await fs.unlink(filepath);
                    deletedCount++;
                    console.log(`🗑️ Deleted old checkpoint: ${file}`);
                }
            }

            console.log(`✅ Cleanup completed. Deleted ${deletedCount} old checkpoints`);
            return {
                success: true,
                deletedCount
            };
        } catch (error) {
            console.error('❌ Error cleaning up checkpoints:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * List available checkpoints
     */
    async listCheckpoints() {
        try {
            const files = await fs.readdir(this.checkpointsDir);
            const checkpoints = [];

            for (const file of files) {
                if (!file.endsWith('.json')) continue;

                const filepath = path.join(this.checkpointsDir, file);
                const stats = await fs.stat(filepath);
                const content = await fs.readFile(filepath, 'utf8');
                const checkpoint = JSON.parse(content);

                checkpoints.push({
                    filename: file,
                    migrationTaskId: checkpoint.migration_task_id,
                    checkpointId: checkpoint.checkpoint_id,
                    created: checkpoint.created_at,
                    size: stats.size,
                    age: Date.now() - new Date(checkpoint.created_at).getTime()
                });
            }

            return {
                success: true,
                checkpoints: checkpoints.sort((a, b) => new Date(b.created) - new Date(a.created))
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

export default CheckpointService;
