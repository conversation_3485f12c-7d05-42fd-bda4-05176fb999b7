<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drive-to-Lark Migrator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        input,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        textarea {
            height: 120px;
            resize: vertical;
        }

        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
        }

        .success {
            color: #28a745;
        }

        .error {
            color: #dc3545;
        }

        .info {
            color: #17a2b8;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
        }

        .section h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🚀 Drive-to-Lark Migrator</h1>
        <p>Cấu hình và kiểm tra kết nối API</p>

        <!-- Google Service Account Section -->
        <div class="section">
            <h3>📁 Google Service Account</h3>
            <div class="form-group">
                <label for="googleServiceAccount">Service Account JSON:</label>
                <textarea id="googleServiceAccount"
                    placeholder="Paste your Google Service Account JSON here..."></textarea>
            </div>
            <div class="form-group">
                <label for="testUserEmail">Test User Email:</label>
                <input type="email" id="testUserEmail" placeholder="<EMAIL>">
            </div>
            <button onclick="testGoogleAPI()">Test Google Drive API</button>
        </div>

        <!-- Lark App Section -->
        <div class="section">
            <h3>🦄 Lark App Credentials</h3>
            <div class="form-group">
                <label for="larkAppId">App ID:</label>
                <input type="text" id="larkAppId" placeholder="cli_xxxxxxxxxx">
            </div>
            <div class="form-group">
                <label for="larkAppSecret">App Secret:</label>
                <input type="password" id="larkAppSecret" placeholder="Your app secret">
            </div>
            <button onclick="testLarkAPI()">Test Lark Drive API</button>
        </div>

        <!-- Test Results -->
        <div id="testResults" class="test-results" style="display: none;">
            <h3>📊 Test Results</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        async function testGoogleAPI() {
            const serviceAccountJson = document.getElementById('googleServiceAccount').value;
            const testUserEmail = document.getElementById('testUserEmail').value;

            if (!serviceAccountJson || !testUserEmail) {
                alert('Vui lòng nhập đầy đủ thông tin Google Service Account');
                return;
            }

            try {
                const serviceAccount = JSON.parse(serviceAccountJson);
                showResults('🔍 Testing Google Drive API...', 'info');

                // Gọi API test (cần implement backend endpoint)
                const response = await fetch('/api/test-google', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ serviceAccount, testUserEmail })
                });

                const result = await response.json();
                displayTestResults('Google Drive API', result);

            } catch (error) {
                showResults(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testLarkAPI() {
            const appId = document.getElementById('larkAppId').value;
            const appSecret = document.getElementById('larkAppSecret').value;

            if (!appId || !appSecret) {
                alert('Vui lòng nhập đầy đủ thông tin Lark App');
                return;
            }

            try {
                showResults('🔍 Testing Lark Drive API...', 'info');

                // Gọi API test (cần implement backend endpoint)
                const response = await fetch('/api/test-lark', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ appId, appSecret })
                });

                const result = await response.json();
                displayTestResults('Lark Drive API', result);

            } catch (error) {
                showResults(`❌ Error: ${error.message}`, 'error');
            }
        }

        function showResults(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultsContent');

            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function displayTestResults(apiName, results) {
            const content = `
                <h4>${apiName} Test Results:</h4>
                <ul>
                    ${Object.entries(results).map(([key, value]) => {
                if (key === 'errors') return '';
                const icon = value ? '✅' : '❌';
                return `<li>${icon} ${key}: ${value ? 'Success' : 'Failed'}</li>`;
            }).join('')}
                </ul>
                ${results.errors && results.errors.length > 0 ?
                    `<div class="error"><strong>Errors:</strong><ul>${results.errors.map(err => `<li>${err}</li>`).join('')}</ul></div>`
                    : ''}
            `;

            showResults(content, 'info');
        }
    </script>
</body>

</html>