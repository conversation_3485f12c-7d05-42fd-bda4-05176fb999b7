/* User Selector Component Styles */
.user-selector {
    position: relative;
    width: 100%;
    font-family: inherit;
}

.user-selector-input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.user-selector-input-container:focus-within {
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.user-selector-input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    color: #333;
}

.user-selector-input:disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

.user-selector-input::placeholder {
    color: #999;
}

.user-selector-arrow {
    padding: 8px 12px;
    color: #666;
    cursor: pointer;
    user-select: none;
    transition: transform 0.2s ease;
}

.user-selector-arrow.open {
    transform: rotate(180deg);
}

.user-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow: hidden;
}

.user-selector-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    color: #666;
    font-size: 14px;
}

.user-selector-error {
    padding: 16px;
    color: #d32f2f;
    font-size: 14px;
    text-align: center;
}

.user-selector-error .retry-btn {
    display: block;
    margin: 8px auto 0;
    padding: 4px 12px;
    background: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.user-selector-error .retry-btn:hover {
    background: #3367d6;
}

.user-selector-no-results {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 14px;
}

.user-selector-list {
    max-height: 250px;
    overflow-y: auto;
}

.user-selector-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.15s ease;
}

.user-selector-item:hover {
    background-color: #f5f5f5;
}

.user-selector-item:last-child {
    border-bottom: none;
}

.user-selector-item.selected {
    background-color: #e3f2fd;
    border-left: 3px solid #4285f4;
}

.user-selector-item.all-users-option {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e1e5e9;
    font-weight: 500;
}

.user-selector-item.all-users-option:hover {
    background-color: #e9ecef;
}

.user-selector-item.all-users-option.selected {
    background-color: #e7f3ff;
    border-left: 3px solid #4285f4;
}

.user-selector-item.suspended {
    opacity: 0.6;
}

.user-selector-item.suspended .user-email {
    color: #999;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-email {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.all-users-label {
    font-size: 14px;
    color: #4285f4;
    font-weight: 600;
}

.suspended-badge {
    font-size: 10px;
    background: #ff9800;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: normal;
}

.user-name {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.user-last-login {
    font-size: 11px;
    color: #999;
    font-weight: normal;
}

/* Spinner for loading state */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .user-selector-dropdown {
        max-height: 200px;
    }
    
    .user-selector-item {
        padding: 10px 12px;
    }
    
    .user-email {
        font-size: 13px;
    }
    
    .user-name {
        font-size: 11px;
    }
    
    .user-last-login {
        font-size: 10px;
    }
}

/* Scrollbar styling for dropdown list */
.user-selector-list::-webkit-scrollbar {
    width: 6px;
}

.user-selector-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.user-selector-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.user-selector-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
