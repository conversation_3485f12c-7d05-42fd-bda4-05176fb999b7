-- Migration: Add scanned storage columns to user_storage_stats table
-- Date: 2025-01-16

-- Add scanned storage columns
ALTER TABLE user_storage_stats 
ADD COLUMN IF NOT EXISTS scanned_storage_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS scanned_storage_updated_at TIMESTAMP WITH TIME ZONE;

-- Add comment for the new columns
COMMENT ON COLUMN user_storage_stats.scanned_storage_bytes IS 'Tổng dung lượng files đã scan (từ bảng scanned_files)';
COMMENT ON COLUMN user_storage_stats.scanned_storage_updated_at IS 'Thời gian cập nhật cuối scanned storage';

-- Create index for the new columns
CREATE INDEX IF NOT EXISTS idx_user_storage_stats_scanned_storage ON user_storage_stats(scanned_storage_bytes);
CREATE INDEX IF NOT EXISTS idx_user_storage_stats_scanned_updated ON user_storage_stats(scanned_storage_updated_at);

-- Update existing records with calculated scanned storage
-- This will calculate the scanned storage for all existing users
WITH scanned_totals AS (
    SELECT 
        user_email,
        COALESCE(SUM(size), 0) as total_scanned_size
    FROM scanned_files 
    GROUP BY user_email
)
UPDATE user_storage_stats 
SET 
    scanned_storage_bytes = COALESCE(scanned_totals.total_scanned_size, 0),
    scanned_storage_updated_at = NOW()
FROM scanned_totals 
WHERE user_storage_stats.user_email = scanned_totals.user_email;

-- Set scanned_storage_updated_at for users without scanned files
UPDATE user_storage_stats 
SET scanned_storage_updated_at = NOW()
WHERE scanned_storage_updated_at IS NULL;
