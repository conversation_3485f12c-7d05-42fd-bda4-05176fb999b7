class DuplicateFileRemover {
    constructor() {
        this.ws = null;
        this.currentSessionId = null;
        this.duplicateGroups = [];
        this.selectedFiles = new Set();
        
        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
    }

    initializeElements() {
        // Input elements
        this.directoryInput = document.getElementById('directoryPath');
        this.scanBtn = document.getElementById('scanBtn');
        
        // Progress elements
        this.progressSection = document.querySelector('.progress-section');
        this.progressTitle = document.getElementById('progressTitle');
        this.progressPercent = document.getElementById('progressPercent');
        this.progressFill = document.getElementById('progressFill');
        this.progressMessage = document.getElementById('progressMessage');
        this.progressStats = document.getElementById('progressStats');
        
        // Results elements
        this.resultsSection = document.querySelector('.results-section');
        this.duplicateCount = document.getElementById('duplicateCount');
        this.duplicateGroupsContainer = document.getElementById('duplicateGroups');
        this.selectAllBtn = document.getElementById('selectAllBtn');
        this.deselectAllBtn = document.getElementById('deselectAllBtn');
        this.deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
        
        // Delete progress elements
        this.deleteProgressSection = document.querySelector('.delete-progress-section');
        this.deleteProgressPercent = document.getElementById('deleteProgressPercent');
        this.deleteProgressFill = document.getElementById('deleteProgressFill');
        this.deleteProgressMessage = document.getElementById('deleteProgressMessage');
        this.deleteProgressStats = document.getElementById('deleteProgressStats');
        
        // Delete results elements
        this.deleteResultsSection = document.querySelector('.delete-results-section');
        this.deleteResults = document.getElementById('deleteResults');
    }

    bindEvents() {
        this.scanBtn.addEventListener('click', () => this.startScan());
        this.selectAllBtn.addEventListener('click', () => this.selectAllFiles());
        this.deselectAllBtn.addEventListener('click', () => this.deselectAllFiles());
        this.deleteSelectedBtn.addEventListener('click', () => this.deleteSelectedFiles());
        
        // Enter key support for directory input
        this.directoryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.startScan();
            }
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.connectWebSocket(), 3000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'progress':
                this.updateProgress(data.data);
                break;
            case 'complete':
                this.handleScanComplete(data.data);
                break;
            case 'error':
                this.handleScanError(data.data);
                break;
            case 'deleteProgress':
                this.updateDeleteProgress(data.data);
                break;
            case 'deleteComplete':
                this.handleDeleteComplete(data.data);
                break;
            case 'deleteError':
                this.handleDeleteError(data.data);
                break;
        }
    }

    async startScan() {
        const directoryPath = this.directoryInput.value.trim();
        
        if (!directoryPath) {
            alert('Vui lòng nhập đường dẫn thư mục');
            return;
        }

        this.showScanLoading(true);
        this.hideResults();
        this.hideDeleteResults();
        
        try {
            const response = await fetch('/api/scan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ directoryPath }),
            });

            const result = await response.json();
            
            if (response.ok) {
                this.currentSessionId = result.sessionId;
                this.registerWebSocketSession();
                this.showProgress();
            } else {
                throw new Error(result.error || 'Lỗi không xác định');
            }
        } catch (error) {
            this.showScanLoading(false);
            alert(`Lỗi: ${error.message}`);
        }
    }

    registerWebSocketSession() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN && this.currentSessionId) {
            this.ws.send(JSON.stringify({
                type: 'register',
                sessionId: this.currentSessionId
            }));
        }
    }

    showScanLoading(loading) {
        const btnText = this.scanBtn.querySelector('.btn-text');
        const btnLoading = this.scanBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-flex';
            this.scanBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            this.scanBtn.disabled = false;
        }
    }

    showProgress() {
        this.progressSection.style.display = 'block';
        this.resultsSection.style.display = 'none';
    }

    hideResults() {
        this.resultsSection.style.display = 'none';
    }

    hideDeleteResults() {
        this.deleteResultsSection.style.display = 'none';
    }

    updateProgress(progress) {
        const percent = progress.total > 0 ? Math.round((progress.processed / progress.total) * 100) : 0;
        
        this.progressPercent.textContent = `${percent}%`;
        this.progressFill.style.width = `${percent}%`;
        this.progressMessage.textContent = progress.message || 'Đang xử lý...';
        this.progressStats.textContent = `${progress.processed} / ${progress.total} files`;
        
        if (progress.phase === 'counting') {
            this.progressTitle.textContent = 'Đang đếm file...';
        } else if (progress.phase === 'scanning') {
            this.progressTitle.textContent = 'Đang quét thư mục...';
        } else if (progress.phase === 'hashing') {
            this.progressTitle.textContent = 'Đang tính hash...';
        }
    }

    handleScanComplete(duplicates) {
        this.showScanLoading(false);
        this.progressSection.style.display = 'none';
        
        this.duplicateGroups = duplicates;
        this.displayResults();
    }

    handleScanError(error) {
        this.showScanLoading(false);
        this.progressSection.style.display = 'none';
        alert(`Lỗi quét thư mục: ${error.message}`);
    }

    displayResults() {
        this.duplicateCount.textContent = this.duplicateGroups.length;
        this.resultsSection.style.display = 'block';
        
        this.duplicateGroupsContainer.innerHTML = '';
        this.selectedFiles.clear();
        
        this.duplicateGroups.forEach((group, groupIndex) => {
            const groupElement = this.createDuplicateGroupElement(group, groupIndex);
            this.duplicateGroupsContainer.appendChild(groupElement);
        });
        
        this.updateDeleteButton();
    }

    createDuplicateGroupElement(group, groupIndex) {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'duplicate-group';
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'duplicate-group-header';
        
        const infoDiv = document.createElement('div');
        infoDiv.className = 'duplicate-group-info';
        infoDiv.textContent = `${group.files.length} file trùng lặp trong ${group.directory}`;
        
        const sizeSpan = document.createElement('span');
        sizeSpan.className = 'duplicate-group-size';
        sizeSpan.textContent = this.formatFileSize(group.size);
        
        headerDiv.appendChild(infoDiv);
        headerDiv.appendChild(sizeSpan);
        
        const filesDiv = document.createElement('div');
        filesDiv.className = 'duplicate-files';
        
        group.files.forEach((file, fileIndex) => {
            const fileDiv = this.createDuplicateFileElement(file, groupIndex, fileIndex);
            filesDiv.appendChild(fileDiv);
        });
        
        groupDiv.appendChild(headerDiv);
        groupDiv.appendChild(filesDiv);
        
        return groupDiv;
    }

    createDuplicateFileElement(file, groupIndex, fileIndex) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'duplicate-file';
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `file-${groupIndex}-${fileIndex}`;
        checkbox.addEventListener('change', () => {
            if (checkbox.checked) {
                this.selectedFiles.add(file.path);
            } else {
                this.selectedFiles.delete(file.path);
            }
            this.updateDeleteButton();
        });
        
        const fileInfoDiv = document.createElement('div');
        fileInfoDiv.className = 'file-info';
        
        const fileNameDiv = document.createElement('div');
        fileNameDiv.className = 'file-name';
        fileNameDiv.textContent = file.name;
        
        const filePathDiv = document.createElement('div');
        filePathDiv.className = 'file-path';
        filePathDiv.textContent = file.path;
        
        fileInfoDiv.appendChild(fileNameDiv);
        fileInfoDiv.appendChild(filePathDiv);
        
        fileDiv.appendChild(checkbox);
        fileDiv.appendChild(fileInfoDiv);
        
        return fileDiv;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    selectAllFiles() {
        const checkboxes = this.duplicateGroupsContainer.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            const fileId = checkbox.id;
            const [, groupIndex, fileIndex] = fileId.split('-');
            const file = this.duplicateGroups[groupIndex].files[fileIndex];
            this.selectedFiles.add(file.path);
        });
        this.updateDeleteButton();
    }

    deselectAllFiles() {
        const checkboxes = this.duplicateGroupsContainer.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        this.selectedFiles.clear();
        this.updateDeleteButton();
    }

    updateDeleteButton() {
        this.deleteSelectedBtn.disabled = this.selectedFiles.size === 0;
    }

    async deleteSelectedFiles() {
        if (this.selectedFiles.size === 0) {
            alert('Vui lòng chọn ít nhất một file để xóa');
            return;
        }

        const confirmed = confirm(`Bạn có chắc chắn muốn xóa ${this.selectedFiles.size} file đã chọn?`);
        if (!confirmed) {
            return;
        }

        this.showDeleteLoading(true);
        this.showDeleteProgress();
        
        try {
            const response = await fetch('/api/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filePaths: Array.from(this.selectedFiles) }),
            });

            const result = await response.json();
            
            if (response.ok) {
                this.currentSessionId = result.sessionId;
                this.registerWebSocketSession();
            } else {
                throw new Error(result.error || 'Lỗi không xác định');
            }
        } catch (error) {
            this.showDeleteLoading(false);
            this.deleteProgressSection.style.display = 'none';
            alert(`Lỗi: ${error.message}`);
        }
    }

    showDeleteLoading(loading) {
        const btnText = this.deleteSelectedBtn.querySelector('.btn-text');
        const btnLoading = this.deleteSelectedBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-flex';
            this.deleteSelectedBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            this.updateDeleteButton();
        }
    }

    showDeleteProgress() {
        this.deleteProgressSection.style.display = 'block';
        this.deleteResultsSection.style.display = 'none';
    }

    updateDeleteProgress(progress) {
        const percent = progress.total > 0 ? Math.round((progress.processed / progress.total) * 100) : 0;
        
        this.deleteProgressPercent.textContent = `${percent}%`;
        this.deleteProgressFill.style.width = `${percent}%`;
        this.deleteProgressMessage.textContent = progress.message || 'Đang xóa...';
        this.deleteProgressStats.textContent = `${progress.processed} / ${progress.total} files`;
    }

    handleDeleteComplete(result) {
        this.showDeleteLoading(false);
        this.deleteProgressSection.style.display = 'none';
        
        this.displayDeleteResults(result);
        
        // Remove successfully deleted files from the UI
        result.successful.forEach(filePath => {
            this.selectedFiles.delete(filePath);
            this.removeFileFromUI(filePath);
        });
        
        this.updateDeleteButton();
    }

    handleDeleteError(error) {
        this.showDeleteLoading(false);
        this.deleteProgressSection.style.display = 'none';
        alert(`Lỗi xóa file: ${error.message}`);
    }

    displayDeleteResults(result) {
        this.deleteResultsSection.style.display = 'block';
        
        let html = '<div class="delete-results">';
        
        if (result.successful.length > 0) {
            html += `<div class="delete-success">✅ Đã xóa thành công ${result.successful.length} file</div>`;
        }
        
        if (result.failed.length > 0) {
            html += `<div class="delete-error">❌ Không thể xóa ${result.failed.length} file:</div>`;
            result.failed.forEach(failure => {
                html += `<div class="delete-error">• ${failure.path}: ${failure.error}</div>`;
            });
        }
        
        html += '</div>';
        this.deleteResults.innerHTML = html;
    }

    removeFileFromUI(filePath) {
        const checkboxes = this.duplicateGroupsContainer.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            const fileId = checkbox.id;
            const [, groupIndex, fileIndex] = fileId.split('-');
            const file = this.duplicateGroups[groupIndex].files[fileIndex];
            
            if (file.path === filePath) {
                const fileElement = checkbox.closest('.duplicate-file');
                fileElement.remove();
                
                // Remove from duplicateGroups data
                this.duplicateGroups[groupIndex].files.splice(fileIndex, 1);
                
                // If group is empty, remove the entire group
                if (this.duplicateGroups[groupIndex].files.length <= 1) {
                    const groupElement = checkbox.closest('.duplicate-group');
                    groupElement.remove();
                    this.duplicateGroups.splice(groupIndex, 1);
                    this.duplicateCount.textContent = this.duplicateGroups.length;
                }
            }
        });
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DuplicateFileRemover();
});
