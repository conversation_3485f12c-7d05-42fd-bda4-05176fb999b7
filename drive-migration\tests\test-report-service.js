/**
 * Test Report Service
 * Test báo cáo migration với mock data
 */

import ReportService from './services/report-service.js';
import { supabaseClient } from './database/supabase.js';

async function testReportService() {
    console.log('🧪 Testing Report Service...\n');

    const reportService = new ReportService();

    try {
        // Test 1: Tạo mock migration task
        console.log('📝 Creating mock migration task...');
        
        const mockTaskData = {
            id: 'test-migration-' + Date.now(),
            user_email: '<EMAIL>',
            status: 'completed',
            total_files: 10,
            processed_files: 8,
            successful_files: 7,
            failed_files: 1,
            total_size: 1024 * 1024 * 100, // 100MB
            processed_size: 1024 * 1024 * 80, // 80MB
            started_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            completed_at: new Date().toISOString(),
            created_at: new Date(Date.now() - 3600000).toISOString(),
            updated_at: new Date().toISOString()
        };

        const { data: task, error: taskError } = await supabaseClient.getServiceClient()
            .from('migration_tasks')
            .insert([mockTaskData])
            .select()
            .single();

        if (taskError) {
            console.log('⚠️ Could not create mock task (table may not exist):', taskError.message);
            console.log('📋 Testing with mock data instead...\n');
            
            // Test với mock data
            await testWithMockData(reportService);
            return;
        }

        console.log('✅ Mock migration task created:', task.id);

        // Test 2: Tạo mock migration items
        console.log('📝 Creating mock migration items...');
        
        const mockItems = [
            {
                migration_task_id: task.id,
                google_file_id: 'file1',
                google_file_name: 'document1.pdf',
                google_file_path: '/folder1/document1.pdf',
                google_file_size: 1024 * 1024 * 10, // 10MB
                lark_file_token: 'lark_token_1',
                status: 'completed',
                download_time: 2000,
                upload_time: 3000,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                migration_task_id: task.id,
                google_file_id: 'file2',
                google_file_name: 'spreadsheet1.xlsx',
                google_file_path: '/folder1/spreadsheet1.xlsx',
                google_file_size: 1024 * 1024 * 5, // 5MB
                lark_file_token: 'lark_token_2',
                status: 'completed',
                download_time: 1500,
                upload_time: 2000,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                migration_task_id: task.id,
                google_file_id: 'file3',
                google_file_name: 'presentation1.pptx',
                google_file_path: '/folder2/presentation1.pptx',
                google_file_size: 1024 * 1024 * 15, // 15MB
                status: 'failed',
                error_message: 'File too large',
                retry_count: 3,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];

        const { data: items, error: itemsError } = await supabaseClient.getServiceClient()
            .from('migration_items')
            .insert(mockItems)
            .select();

        if (itemsError) {
            console.log('⚠️ Could not create mock items:', itemsError.message);
            await testWithMockData(reportService);
            return;
        }

        console.log(`✅ Created ${items.length} mock migration items`);

        // Test 3: Generate CSV report
        console.log('\n📊 Testing CSV report generation...');
        try {
            const csvReport = await reportService.generateMigrationSummaryReport(task.id, 'csv');
            console.log('✅ CSV report generated successfully:');
            console.log(`   📄 File: ${csvReport.filename}`);
            console.log(`   📏 Size: ${csvReport.size} bytes`);
        } catch (error) {
            console.log('❌ CSV report generation failed:', error.message);
        }

        // Test 4: Generate PDF report
        console.log('\n📊 Testing PDF report generation...');
        try {
            const pdfReport = await reportService.generateMigrationSummaryReport(task.id, 'pdf');
            console.log('✅ PDF report generated successfully:');
            console.log(`   📄 File: ${pdfReport.filename}`);
            console.log(`   📏 Size: ${pdfReport.size} bytes`);
        } catch (error) {
            console.log('❌ PDF report generation failed:', error.message);
        }

        // Test 5: List available reports
        console.log('\n📋 Testing report listing...');
        try {
            const reports = await reportService.getAvailableReports();
            console.log(`✅ Found ${reports.length} available reports:`);
            reports.forEach(report => {
                console.log(`   📄 ${report.filename} (${reportService.formatFileSize(report.size)})`);
            });
        } catch (error) {
            console.log('❌ Report listing failed:', error.message);
        }

        // Test 6: Cleanup old reports
        console.log('\n🧹 Testing report cleanup...');
        try {
            await reportService.cleanupOldReports(5);
            console.log('✅ Report cleanup completed');
        } catch (error) {
            console.log('❌ Report cleanup failed:', error.message);
        }

        // Cleanup: Remove mock data
        console.log('\n🧹 Cleaning up mock data...');
        await supabaseClient.getServiceClient()
            .from('migration_items')
            .delete()
            .eq('migration_task_id', task.id);
        
        await supabaseClient.getServiceClient()
            .from('migration_tasks')
            .delete()
            .eq('id', task.id);
        
        console.log('✅ Mock data cleaned up');

    } catch (error) {
        console.error('❌ Test failed:', error);
        await testWithMockData(reportService);
    }
}

async function testWithMockData(reportService) {
    console.log('📋 Testing report service with mock data...\n');

    // Mock data structure
    const mockReportData = {
        migrationTask: {
            id: 'mock-task-123',
            task_name: 'Mock Migration Task',
            status: 'completed',
            created_at: new Date(Date.now() - 3600000).toISOString(),
            updated_at: new Date().toISOString()
        },
        stats: {
            total_files: 10,
            completed_files: 8,
            failed_files: 2,
            in_progress_files: 0,
            success_rate: 80,
            total_size: 1024 * 1024 * 100,
            processed_size: 1024 * 1024 * 80
        },
        items: [
            {
                google_file_id: 'mock_file_1',
                file_name: 'test-document.pdf',
                file_type: 'application/pdf',
                file_size: 1024 * 1024 * 5,
                source_path: '/test/folder/test-document.pdf',
                target_path: '/migrated/test-document.pdf',
                status: 'completed',
                lark_file_token: 'mock_lark_token_1',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                processing_time_ms: 5000,
                retry_count: 0
            },
            {
                google_file_id: 'mock_file_2',
                file_name: 'test-spreadsheet.xlsx',
                file_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                file_size: 1024 * 1024 * 3,
                source_path: '/test/folder/test-spreadsheet.xlsx',
                target_path: '/migrated/test-spreadsheet.xlsx',
                status: 'failed',
                error_message: 'Network timeout',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                processing_time_ms: 0,
                retry_count: 3
            }
        ]
    };

    try {
        // Test CSV generation with mock data
        console.log('📊 Testing CSV generation with mock data...');
        const csvReport = await reportService.generateCSVReport(mockReportData, 'mock-task-123');
        console.log('✅ Mock CSV report generated:');
        console.log(`   📄 File: ${csvReport.filename}`);
        console.log(`   📏 Size: ${csvReport.size} bytes`);

        // Test PDF generation with mock data
        console.log('\n📊 Testing PDF generation with mock data...');
        const pdfReport = await reportService.generatePDFReport(mockReportData, 'mock-task-123');
        console.log('✅ Mock PDF report generated:');
        console.log(`   📄 File: ${pdfReport.filename}`);
        console.log(`   📏 Size: ${pdfReport.size} bytes`);

        // Test utility functions
        console.log('\n🔧 Testing utility functions...');
        console.log(`✅ Format file size: ${reportService.formatFileSize(1024 * 1024 * 5)} = 5 MB`);
        console.log(`✅ Summary text generated: ${mockReportData.migrationTask.task_name}`);

    } catch (error) {
        console.error('❌ Mock data test failed:', error);
    }
}

// Run test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testReportService()
        .then(() => {
            console.log('\n🏁 Report Service test completed!');
        })
        .catch(error => {
            console.error('\n❌ Report Service test failed:', error);
        });
}

export { testReportService };
