# Google Service Account Configuration for osp.com.vn domain
# Google credentials are loaded from google-service-account.json file
# You can override the path using GOOGLE_SERVICE_ACCOUNT_PATH

# Google Domain Configuration
GOOGLE_DOMAIN=osp.com.vn

# Google Service Account JSON File Path (optional - defaults to google-service-account.json)
GOOGLE_SERVICE_ACCOUNT_PATH=./google-service-account-osp-com-vn.json

# Google Admin Email (optional - defaults to admin@{GOOGLE_DOMAIN})
GOOGLE_ADMIN_EMAIL=<EMAIL>

# Lark Configuration
LARK_APP_ID=********************
LARK_APP_SECRET=gVUuBIpJn2ggqjIuz6vPyb3ousCSVutn

# Supabase Configuration
SUPABASE_URL=https://ypldwmrtngriajjxyvma.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MaXAQJuryqxIoJd8QpuoKE6zmYTUEmJMEYzR5woZBxU
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.j7YyBntY6J2lQwdlj7Ec9_oSEoMGRvXylysPuIiqTmA

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
