.storage-errors-modal {
    max-width: 900px;
    max-height: 80vh;
    width: 90vw;
}

.storage-errors-modal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
}

.error-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    border-left: 4px solid #007bff;
}

.error-summary h3 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-weight: 500;
    color: #666;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.errors-list h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.no-errors {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

.error-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.error-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.error-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.error-header:hover {
    background: #e9ecef;
}

.error-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.severity-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.user-email {
    font-weight: 600;
    color: #333;
}

.error-time {
    color: #666;
    font-size: 12px;
}

.error-stats {
    display: flex;
    align-items: center;
    gap: 16px;
}

.scan-percentage,
.download-percentage {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background: #e9ecef;
    color: #666;
}

.expand-icon {
    color: #007bff;
    font-size: 12px;
    margin-left: 8px;
}

.error-details {
    padding: 16px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.error-details h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.error-message {
    margin-bottom: 16px;
}

.error-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #d73a49;
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
}

.storage-breakdown {
    margin-bottom: 16px;
}

.storage-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.storage-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 13px;
}

.storage-stat span:first-child {
    color: #666;
}

.storage-stat span:last-child {
    font-weight: 600;
    color: #333;
}

.storage-stat .missing {
    color: #dc3545;
}

.error-classification {
    margin-bottom: 0;
}

.severity-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}
