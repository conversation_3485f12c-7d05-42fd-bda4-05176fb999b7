#!/usr/bin/env node

/**
 * Test Runner cho tấ<PERSON> cả Unit Tests
 * Ch<PERSON>y tất cả tests và tạo báo cáo tổng hợp
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TestRunner {
    constructor() {
        this.testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0,
            suites: []
        };
        
        this.testSuites = [
            {
                name: 'Drive Scanner Service',
                file: 'tests/services/drive-scanner.test.js',
                description: 'Tests cho Google Drive scanning functionality'
            },
            {
                name: 'Path Resolver Service',
                file: 'tests/services/path-resolver.test.js',
                description: 'Tests cho path resolution và caching'
            },
            {
                name: 'File Download Engine',
                file: 'tests/services/file-download-engine.test.js',
                description: 'Tests cho file download từ Google Drive'
            },
            {
                name: 'Lark Upload Engine',
                file: 'tests/services/lark-upload-engine.test.js',
                description: 'Tests cho file upload lên Lark Drive'
            },
            {
                name: 'User Mapping Service',
                file: 'tests/services/user-mapping-service.test.js',
                description: 'Tests cho user mapping và validation'
            }
        ];
    }
    
    async runAllTests() {
        console.log('🧪 Starting Unit Test Suite for Drive-to-Lark Migration');
        console.log('=' .repeat(60));
        
        const startTime = Date.now();
        
        for (const suite of this.testSuites) {
            await this.runTestSuite(suite);
        }
        
        const endTime = Date.now();
        this.testResults.duration = endTime - startTime;
        
        this.printSummary();
        this.generateReport();
        
        // Exit với appropriate code
        process.exit(this.testResults.failed > 0 ? 1 : 0);
    }
    
    async runTestSuite(suite) {
        console.log(`\n📋 Running: ${suite.name}`);
        console.log(`📄 Description: ${suite.description}`);
        console.log(`📁 File: ${suite.file}`);
        console.log('-'.repeat(50));
        
        const suiteResult = {
            name: suite.name,
            file: suite.file,
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0,
            tests: []
        };
        
        try {
            const result = await this.executeTest(suite.file);
            
            suiteResult.passed = result.passed;
            suiteResult.failed = result.failed;
            suiteResult.skipped = result.skipped;
            suiteResult.duration = result.duration;
            suiteResult.tests = result.tests;
            
            // Update totals
            this.testResults.total += result.passed + result.failed + result.skipped;
            this.testResults.passed += result.passed;
            this.testResults.failed += result.failed;
            this.testResults.skipped += result.skipped;
            
            if (result.failed === 0) {
                console.log(`✅ ${suite.name}: ALL TESTS PASSED`);
            } else {
                console.log(`❌ ${suite.name}: ${result.failed} TESTS FAILED`);
            }
            
        } catch (error) {
            console.error(`💥 ${suite.name}: SUITE FAILED - ${error.message}`);
            suiteResult.failed = 1;
            this.testResults.failed += 1;
        }
        
        this.testResults.suites.push(suiteResult);
    }
    
    async executeTest(testFile) {
        return new Promise((resolve, reject) => {
            const testProcess = spawn('node', ['--test', testFile], {
                cwd: path.join(__dirname, '..'),
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            let stdout = '';
            let stderr = '';
            
            testProcess.stdout.on('data', (data) => {
                stdout += data.toString();
                process.stdout.write(data);
            });
            
            testProcess.stderr.on('data', (data) => {
                stderr += data.toString();
                process.stderr.write(data);
            });
            
            testProcess.on('close', (code) => {
                const result = this.parseTestOutput(stdout, stderr);
                
                if (code === 0) {
                    resolve(result);
                } else {
                    // Even if exit code is non-zero, try to parse results
                    resolve(result);
                }
            });
            
            testProcess.on('error', (error) => {
                reject(error);
            });
        });
    }
    
    parseTestOutput(stdout, stderr) {
        // Parse Node.js test runner output
        const result = {
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0,
            tests: []
        };
        
        // Simple parsing - count ✓ and ✗ symbols
        const lines = stdout.split('\n');
        
        for (const line of lines) {
            if (line.includes('✓')) {
                result.passed++;
            } else if (line.includes('✗') || line.includes('×')) {
                result.failed++;
            } else if (line.includes('⚠') || line.includes('skip')) {
                result.skipped++;
            }
        }
        
        // Extract duration if available
        const durationMatch = stdout.match(/(\d+(?:\.\d+)?)\s*ms/);
        if (durationMatch) {
            result.duration = parseFloat(durationMatch[1]);
        }
        
        return result;
    }
    
    printSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 TEST SUMMARY');
        console.log('='.repeat(60));
        
        console.log(`📈 Total Tests: ${this.testResults.total}`);
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`⚠️  Skipped: ${this.testResults.skipped}`);
        console.log(`⏱️  Duration: ${this.testResults.duration}ms`);
        
        const successRate = this.testResults.total > 0 
            ? ((this.testResults.passed / this.testResults.total) * 100).toFixed(1)
            : 0;
        console.log(`📊 Success Rate: ${successRate}%`);
        
        console.log('\n📋 Suite Results:');
        this.testResults.suites.forEach(suite => {
            const status = suite.failed === 0 ? '✅' : '❌';
            console.log(`  ${status} ${suite.name}: ${suite.passed} passed, ${suite.failed} failed`);
        });
        
        if (this.testResults.failed === 0) {
            console.log('\n🎉 ALL TESTS PASSED! 🎉');
        } else {
            console.log(`\n💥 ${this.testResults.failed} TESTS FAILED`);
        }
    }
    
    generateReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: this.testResults,
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch
            },
            suites: this.testResults.suites
        };
        
        const reportPath = path.join(__dirname, 'test-report.json');
        
        try {
            fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
            console.log(`\n📄 Test report saved: ${reportPath}`);
        } catch (error) {
            console.error(`❌ Failed to save test report: ${error.message}`);
        }
        
        // Generate HTML report
        this.generateHtmlReport(reportData);
    }
    
    generateHtmlReport(reportData) {
        const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unit Test Report - Drive to Lark Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .suite { margin: 10px 0; padding: 15px; border-left: 4px solid #ddd; }
        .passed { border-left-color: #4CAF50; }
        .failed { border-left-color: #f44336; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Unit Test Report</h1>
        <p><strong>Project:</strong> Drive to Lark Migration</p>
        <p><strong>Generated:</strong> ${reportData.timestamp}</p>
        <p><strong>Environment:</strong> Node.js ${reportData.environment.nodeVersion} on ${reportData.environment.platform}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 2em; font-weight: bold;">${reportData.summary.total}</div>
        </div>
        <div class="metric">
            <h3>Passed</h3>
            <div style="font-size: 2em; font-weight: bold; color: #4CAF50;">${reportData.summary.passed}</div>
        </div>
        <div class="metric">
            <h3>Failed</h3>
            <div style="font-size: 2em; font-weight: bold; color: #f44336;">${reportData.summary.failed}</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div style="font-size: 2em; font-weight: bold;">${((reportData.summary.passed / reportData.summary.total) * 100).toFixed(1)}%</div>
        </div>
    </div>
    
    <h2>📋 Test Suites</h2>
    ${reportData.suites.map(suite => `
        <div class="suite ${suite.failed === 0 ? 'passed' : 'failed'}">
            <h3>${suite.failed === 0 ? '✅' : '❌'} ${suite.name}</h3>
            <p><strong>File:</strong> ${suite.file}</p>
            <p><strong>Results:</strong> ${suite.passed} passed, ${suite.failed} failed, ${suite.skipped} skipped</p>
            <p><strong>Duration:</strong> ${suite.duration}ms</p>
        </div>
    `).join('')}
    
    <div style="margin-top: 40px; padding: 20px; background: #f9f9f9; border-radius: 8px;">
        <h3>📝 Notes</h3>
        <ul>
            <li>This report covers unit tests for Sprint 2-4 features</li>
            <li>Tests include Drive Scanner, Path Resolver, File Download Engine, Lark Upload Engine, and User Mapping Service</li>
            <li>All tests use mocked dependencies for isolation</li>
            <li>For integration tests, run <code>npm run test:uat</code></li>
        </ul>
    </div>
</body>
</html>`;
        
        const htmlPath = path.join(__dirname, 'test-report.html');
        
        try {
            fs.writeFileSync(htmlPath, htmlContent);
            console.log(`📄 HTML report saved: ${htmlPath}`);
        } catch (error) {
            console.error(`❌ Failed to save HTML report: ${error.message}`);
        }
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const runner = new TestRunner();
    runner.runAllTests().catch(error => {
        console.error('💥 Test runner failed:', error);
        process.exit(1);
    });
}

export default TestRunner;
