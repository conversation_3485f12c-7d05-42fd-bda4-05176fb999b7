.storage-discrepancy-modal {
    max-width: 1000px;
    max-height: 85vh;
    width: 95vw;
}

.storage-discrepancy-modal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
}

.folder-path-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.folder-path-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.folder-path-input {
    display: flex;
    gap: 12px;
    align-items: center;
}

.folder-path-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.analysis-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #28a745;
}

.analysis-summary h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.summary-item .label {
    font-weight: 500;
    color: #666;
}

.summary-item .value {
    font-weight: 600;
    color: #333;
}

.summary-item .value.missing {
    color: #dc3545;
}

.summary-item .value.extra {
    color: #28a745;
}

.filter-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.filter-section label {
    font-weight: 600;
    color: #333;
}

.filter-select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.users-discrepancy-list h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

.user-discrepancy-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.user-discrepancy-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.user-discrepancy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.user-discrepancy-header:hover {
    background: #e9ecef;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.discrepancy-icon {
    font-size: 16px;
}

.user-email {
    font-weight: 600;
    color: #333;
}

.error-badge {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.discrepancy-summary {
    display: flex;
    align-items: center;
    gap: 12px;
}

.discrepancy-amount {
    font-weight: 600;
    font-size: 14px;
}

.discrepancy-percentage {
    font-size: 12px;
    opacity: 0.8;
    margin-left: 4px;
}

.expand-icon {
    color: #007bff;
    font-size: 12px;
}

.user-discrepancy-details {
    padding: 16px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 13px;
}

.detail-item span:first-child {
    color: #666;
    font-weight: 500;
}

.detail-item span:last-child {
    font-weight: 600;
    color: #333;
}

.scan-error,
.analysis-error {
    margin-top: 12px;
    padding: 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.scan-error h4,
.analysis-error h4 {
    margin: 0 0 8px 0;
    color: #856404;
    font-size: 14px;
}

.scan-error pre,
.analysis-error pre {
    margin: 0;
    font-size: 12px;
    color: #856404;
    white-space: pre-wrap;
    word-break: break-word;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.no-analysis {
    text-align: center;
    padding: 40px;
    color: #666;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

/* File Size Analysis Modal specific styles */
.file-size-analysis-modal {
    max-width: 1200px;
    max-height: 90vh;
    width: 98vw;
}

.analysis-controls {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.control-row {
    display: flex;
    gap: 16px;
    align-items: end;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.control-group label {
    font-weight: 600;
    color: #333;
    font-size: 12px;
}

.control-group input,
.control-group select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.conversion-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #28a745;
}

.conversion-stats h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.conversion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.conversion-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
}

.conversion-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.file-icon {
    font-size: 16px;
}

.conversion-type {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.conversion-stats-details {
    font-size: 12px;
    color: #666;
}

.conversion-stats-details div {
    margin-bottom: 2px;
}

.files-list h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.file-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.file-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.file-header:hover {
    background: #e9ecef;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-user {
    color: #666;
    font-size: 12px;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
}

.size-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
}

.google-size,
.actual-size {
    color: #666;
    white-space: nowrap;
}

.size-discrepancy {
    font-weight: 600;
    white-space: nowrap;
}

.file-details {
    padding: 14px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 12px;
}

.detail-item span:first-child {
    color: #666;
    font-weight: 500;
}

.detail-item span:last-child {
    font-weight: 600;
    color: #333;
}

.status.downloaded {
    color: #28a745;
}

.status.not_downloaded {
    color: #dc3545;
}

.status.failed {
    color: #dc3545;
}

.more-files-note {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 12px;
}

.summary-item .value.increase {
    color: #28a745;
}

.summary-item .value.decrease {
    color: #dc3545;
}