/* Users Overview Page Styles */
.users-overview {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.users-overview-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 2rem;
}

.header-content h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d3748;
    white-space: nowrap;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.view-controls {
    display: flex;
    gap: 0.25rem;
    background: #f7fafc;
    border-radius: 8px;
    padding: 0.25rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #4a5568;
}

.btn-icon:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.btn-icon.active {
    background: #4299e1;
    color: white;
}

.users-overview-main {
    display: grid;
    grid-template-columns: 350px 1fr;
    height: calc(100vh - 140px);
    max-width: 1400px;
    margin: 0 auto;
    gap: 0;
    transition: grid-template-columns 0.3s ease;
}

.users-overview-main.sidebar-collapsed {
    grid-template-columns: 60px 1fr;
}

.users-sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.sidebar-collapsed .users-sidebar {
    overflow: hidden;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.sidebar-collapsed .sidebar-header {
    padding: 1rem 0.5rem;
    text-align: center;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
}

.sidebar-collapsed .sidebar-header h2 {
    font-size: 1.5rem;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-ghost {
    background: transparent;
    color: #718096;
}

.btn-ghost:hover {
    background: #f7fafc;
    color: #2d3748;
}

.file-tree-content {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.file-tree-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.file-tree-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.file-tree-header h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
}

.file-tree-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-count {
    color: #718096;
    font-size: 0.9rem;
}

.empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 3rem;
}

.empty-state-content {
    max-width: 400px;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #2d3748;
}

.empty-state p {
    margin: 0 0 1.5rem 0;
    color: #718096;
    line-height: 1.5;
}

.empty-users {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
}

.empty-users p {
    color: #c53030;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .users-overview-main {
        grid-template-columns: 300px 1fr;
    }
    
    .header-content {
        padding: 0 1rem;
        gap: 1rem;
    }
    
    .header-content h1 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .users-overview-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .users-sidebar {
        max-height: 300px;
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 640px) {
    .users-overview-header {
        padding: 1rem;
    }
    
    .file-tree-header {
        padding: 1rem;
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .file-tree-actions {
        justify-content: space-between;
    }
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
    color: #2d3748;
}

/* Animation for loading states */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}
