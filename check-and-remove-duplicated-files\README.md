# Duplicate File Remover

M<PERSON><PERSON> công cụ web để tìm và xóa các file trùng lặp trong thư mục.

## Tính năng

- ✅ Quét thư mục đệ quy để tìm file trùng lặp
- ✅ Phát hiện file trùng lặp dựa trên:
  - Tên file có pattern giống nhau (ví dụ: file_1.txt, file_2.txt)
  - Kích thước file giống nhau
  - Hash MD5 giống nhau
- ✅ Giao diện web thân thiện
- ✅ Theo dõi tiến độ real-time
- ✅ Báo cáo chi tiết các file trùng lặp
- ✅ Xóa file trùng lặp với báo cáo kết quả
- ✅ Xử lý lỗi và hiển thị nguyên nhân

## Cài đặt

1. Đảm bảo đã cài đặt Node.js (phiên bản 14 trở lên)
2. Mở terminal/command prompt tại thư mục dự án
3. Cài đặt dependencies:
   ```bash
   npm install
   ```

## Sử dụng

### Cách 1: Sử dụng npm
```bash
npm start
```

### Cách 2: Sử dụng Node.js trực tiếp
```bash
node server.js
```

### Cách 3: Sử dụng file batch (Windows)
Double-click vào file `start-server.bat`

Sau khi khởi động server, mở trình duyệt và truy cập: http://localhost:3003

## Hướng dẫn sử dụng

1. **Nhập thông tin quét**:
   - **Đường dẫn thư mục**: Nhập đường dẫn đầy đủ đến thư mục cần quét
     - Ví dụ: `C:\Users\<USER>\Documents`
     - Ví dụ: `E:\<EMAIL>`
   - **Số luồng quét**: Chọn số luồng song song (1-16)
     - 4 luồng: Phù hợp cho máy tính thông thường
     - 8-16 luồng: Cho máy tính mạnh, thư mục lớn

2. **Quét thư mục**: Click nút "Quét thư mục" để bắt đầu
   - Hệ thống sẽ hiển thị tiến độ real-time với nhiều worker
   - Quá trình: đếm file → chuẩn bị → quét song song → tính hash

3. **Điều khiển quét linh hoạt**:
   - **⏸️ Tạm dừng**: Dừng quét tạm thời, có thể tiếp tục sau
   - **▶️ Tiếp tục**: Tiếp tục quét từ vị trí đã dừng
   - **⏹️ Dừng hẳn**: Hủy bỏ hoàn toàn quá trình quét

4. **Xem kết quả real-time**:
   - File duplicate hiển thị ngay khi được phát hiện
   - File gốc có màu xanh với icon 📁
   - File bản sao có màu vàng với icon 📄
   - File bản sao được tự động chọn sẵn để xóa

5. **Thao tác với file duplicate**:
   - **Xóa tất cả file trùng**: Click nút "🗑️ Xóa tất cả file trùng (giữ file gốc)"
     - Tự động xóa tất cả file bản sao
     - Giữ lại file gốc an toàn
     - Xử lý được hàng nghìn file mà không bị lỗi
   - **Xóa file đã chọn**: Tùy chọn file cụ thể để xóa
   - **Chọn/Bỏ chọn tất cả**: Điều khiển nhanh việc chọn file

6. **Theo dõi quá trình xóa chi tiết**:
   - Hiển thị từng file đang được xóa
   - Trạng thái real-time: 🔄 đang xóa, ✅ đã xóa, ❌ lỗi
   - Cuộn tự động để theo dõi tiến trình
   - Báo cáo tổng kết: thành công/thất bại

## Cách hoạt động

### Thuật toán phát hiện file trùng lặp (Tối ưu)

1. **Quét thư mục đệ quy**: Duyệt tất cả thư mục con với đa luồng
2. **Nhóm file theo pattern**: Tìm các file có tên tương tự (ví dụ: `photo_1.jpg`, `photo_2.jpg`)
3. **So sánh kích thước**: Chỉ so sánh các file có cùng kích thước
4. **Xác định duplicate**: Dựa vào tên file pattern + kích thước (bỏ qua hash để tăng tốc)
5. **Báo cáo real-time**: Hiển thị ngay khi phát hiện duplicate

### Pattern nhận dạng file trùng lặp

File được coi là có thể trùng lặp nếu:
- Tên file có format: `basename_số.extension`
- Ví dụ: `document_1.pdf`, `document_2.pdf`, `document_3.pdf`
- Regex pattern: `/^(.+)_\d+(\.[^.]+)?$/`

## Cấu trúc dự án

```
check-and-remove-duplicated-files/
├── docs/
│   └── requirement.md          # Yêu cầu dự án
├── src/
│   ├── duplicateScanner.js     # Logic quét và phát hiện file trùng lặp
│   └── fileManager.js          # Logic quản lý và xóa file
├── public/
│   ├── index.html              # Giao diện web
│   ├── styles.css              # CSS styling
│   └── app.js                  # JavaScript frontend
├── server.js                   # Server Express.js
├── package.json                # Dependencies và scripts
├── start-server.bat            # Script khởi động (Windows)
└── README.md                   # Tài liệu này
```

## API Endpoints

### POST /api/scan
Quét thư mục để tìm file trùng lặp
- Body: `{ "directoryPath": "C:\\path\\to\\directory" }`
- Response: `{ "sessionId": "unique-session-id" }`

### POST /api/delete
Xóa các file đã chọn
- Body: `{ "filePaths": ["path1", "path2", ...] }`
- Response: `{ "sessionId": "unique-session-id" }`

## WebSocket Events

- `progress`: Tiến độ quét thư mục
- `complete`: Hoàn thành quét, trả về danh sách file trùng lặp
- `error`: Lỗi trong quá trình quét
- `deleteProgress`: Tiến độ xóa file
- `deleteComplete`: Hoàn thành xóa file
- `deleteError`: Lỗi trong quá trình xóa

## Cập nhật mới nhất

### Phiên bản 2.1 - Tối ưu hiệu năng và điều khiển
- 🚀 **Tối ưu thuật toán**:
  - Bỏ qua check hash/content để tăng tốc độ quét
  - Chỉ dựa vào tên file (pattern) và kích thước
  - Giảm thời gian quét từ phút xuống giây
- ⏯️ **Điều khiển quét linh hoạt**:
  - Nút "⏸️ Tạm dừng" quét bất cứ lúc nào
  - Nút "▶️ Tiếp tục" quét từ vị trí đã dừng
  - Nút "⏹️ Dừng hẳn" để hủy bỏ quá trình
- 🗂️ **Lưu trữ thông minh**:
  - Kết quả duplicate lưu trên server
  - Tránh lỗi "PayloadTooLargeError"
  - Xử lý được thư mục có hàng nghìn file duplicate
- 📊 **Hiển thị tiến trình xóa chi tiết**:
  - Theo dõi từng file đang được xóa
  - Hiển thị trạng thái: đang xóa/đã xóa/lỗi
  - Cuộn tự động để theo dõi tiến trình

### Phiên bản 2.0 - Quét song song và Real-time
- 🚀 **Quét song song đa luồng**:
  - Thêm textbox để nhập số luồng quét (1-16 luồng)
  - Mỗi luồng xử lý một thư mục riêng biệt
  - Tăng tốc độ quét đáng kể với thư mục lớn
- ⚡ **Hiển thị kết quả real-time**:
  - File duplicate được hiển thị ngay khi phát hiện
  - Không cần chờ quét xong hết mới thấy kết quả
  - Có thể thao tác với file duplicate ngay trong quá trình quét
- 🗑️ **Xóa tất cả file trùng thông minh**:
  - Button "Xóa tất cả file trùng (giữ file gốc)"
  - Tự động nhận diện file gốc và file bản sao
  - Chỉ xóa file bản sao, giữ lại file gốc an toàn
- 🎨 **Giao diện cải thiện**:
  - Phân biệt rõ file gốc (màu xanh) và file bản sao (màu vàng)
  - Hiển thị icon và nhãn cho từng loại file
  - Tự động chọn sẵn file bản sao để xóa

### Phiên bản 1.1 - Sửa lỗi và cải thiện
- ✅ **Sửa lỗi quyền truy cập**: Bỏ qua các thư mục hệ thống như "System Volume Information", "$RECYCLE.BIN"
- ✅ **Cải thiện logic phát hiện duplicate**:
  - Nhận diện đúng file gốc và file có số (ví dụ: `file.docx`, `file_1.docx`, `file_2.docx`)
  - Xử lý tên file tiếng Việt có dấu
  - Hỗ trợ tên file dài và phức tạp
- ✅ **Tăng cường xử lý lỗi**: Bỏ qua file/thư mục không thể truy cập thay vì dừng quá trình
- ✅ **Cải thiện hiệu năng**: Tối ưu thuật toán nhóm file

### Các file được bỏ qua tự động:
- `System Volume Information`
- `$RECYCLE.BIN`
- `pagefile.sys`, `hiberfil.sys`, `swapfile.sys`
- Các file/thư mục bắt đầu bằng `$`

## Lưu ý

- **Backup dữ liệu**: Luôn backup dữ liệu quan trọng trước khi xóa file
- **Quyền truy cập**: Ứng dụng sẽ tự động bỏ qua các thư mục hệ thống không có quyền truy cập
- **Hiệu năng**: Quá trình quét có thể mất thời gian với thư mục lớn
- **Bảo mật**: Ứng dụng đã được cải thiện để an toàn hơn với thư mục hệ thống

## Troubleshooting

### Server không khởi động được
- Kiểm tra Node.js đã được cài đặt: `node --version`
- Kiểm tra dependencies: `npm install`
- Kiểm tra port 3000 có bị chiếm dụng không

### Không quét được thư mục
- Kiểm tra đường dẫn thư mục có tồn tại không
- Kiểm tra quyền truy cập thư mục
- Kiểm tra thư mục có file nào không

### Lỗi xóa file
- Kiểm tra quyền ghi file
- Kiểm tra file có đang được sử dụng bởi ứng dụng khác không
- Kiểm tra file có thuộc tính read-only không

## License

MIT License
