.storage-analytics-dashboard {
    max-width: 1400px;
    max-height: 90vh;
    width: 98vw;
}

.storage-analytics-dashboard .modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 20px;
}

/* Health Score Section */
.health-score-section {
    margin-bottom: 24px;
}

.health-score-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.health-score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.health-score-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.health-score-value {
    font-size: 32px;
    font-weight: 700;
}

.health-score-bar {
    width: 100%;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 12px;
}

.health-score-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 6px;
}

.issues-summary {
    display: flex;
    gap: 16px;
    font-size: 14px;
}

.critical-count,
.warning-count,
.info-count {
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* Key Metrics Section */
.key-metrics-section {
    margin-bottom: 24px;
}

.key-metrics-section h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.metric-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.2s ease;
}

.metric-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 8px;
}

.metric-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Issues Section */
.issues-section {
    margin-bottom: 24px;
}

.issues-section h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.issue-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.issue-card.critical {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.issue-card.warning {
    border-left: 4px solid #ffc107;
    background: #fffbf0;
}

.issue-card.info {
    border-left: 4px solid #17a2b8;
    background: #f0f9ff;
}

.issue-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.issue-icon {
    font-size: 18px;
}

.issue-title {
    flex: 1;
    font-weight: 600;
    color: #333;
}

.issue-action-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.issue-action-btn:hover {
    background: #0056b3;
}

.issue-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* Quick Actions Section */
.quick-actions-section {
    margin-bottom: 24px;
}

.quick-actions-section h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.quick-action-btn {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    text-align: left;
}

.quick-action-btn:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.quick-action-btn.errors:hover {
    border-color: #dc3545;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

.quick-action-btn.discrepancy:hover {
    border-color: #ffc107;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1);
}

.quick-action-btn.file-analysis:hover {
    border-color: #17a2b8;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.1);
}

.action-icon {
    font-size: 24px;
}

.action-label {
    flex: 1;
    font-weight: 600;
    color: #333;
}

.action-count {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.quick-action-btn.errors .action-count {
    background: #dc3545;
}

.quick-action-btn.discrepancy .action-count {
    background: #ffc107;
    color: #212529;
}

.quick-action-btn.file-analysis .action-count {
    background: #17a2b8;
}

/* Dashboard Footer */
.dashboard-footer {
    text-align: center;
    padding: 16px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    margin: 0 -20px -20px -20px;
}

.dashboard-footer p {
    margin: 0;
    color: #666;
    font-size: 12px;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.no-data {
    text-align: center;
    padding: 60px;
    color: #666;
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}
