* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Input Section */
.input-section {
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.form-group label {
    font-weight: 600;
    color: #555;
    min-width: 180px;
}

.form-group input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    min-width: 300px;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

.btn-loading .btn-text {
    display: none;
}

.btn-loading .btn-loading {
    display: inline-flex !important;
    align-items: center;
    gap: 8px;
}

/* Spinner */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Section */
.progress-section, .delete-progress-section {
    margin-bottom: 30px;
}

.progress-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h3 {
    color: #495057;
    font-size: 1.2rem;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #6c757d;
}

/* Results Section */
.results-section {
    margin-bottom: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-summary {
    background: #e7f3ff;
    color: #0066cc;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
}

.results-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

/* Duplicate Groups */
.duplicate-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
}

.duplicate-group-header {
    background: #e9ecef;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.duplicate-group-info {
    font-weight: 600;
    color: #495057;
}

.duplicate-group-size {
    background: #6f42c1;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
}

.duplicate-files {
    padding: 0;
}

.duplicate-file {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.duplicate-file:last-child {
    border-bottom: none;
}

.duplicate-file:hover {
    background: #f1f3f4;
}

.duplicate-file input[type="checkbox"] {
    margin-right: 15px;
    transform: scale(1.2);
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.file-path {
    font-size: 12px;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

/* Delete Results */
.delete-results {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.delete-success {
    background: #d4edda;
    color: #155724;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #28a745;
}

.delete-error {
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    main {
        padding: 20px;
    }

    .form-group {
        flex-direction: column;
        align-items: stretch;
    }

    .form-group label {
        min-width: auto;
    }

    .form-group input {
        min-width: auto;
    }

    .results-header {
        flex-direction: column;
        align-items: stretch;
    }

    .results-actions {
        justify-content: center;
    }

    .duplicate-file {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
