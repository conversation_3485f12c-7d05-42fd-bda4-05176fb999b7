{"name": "drive-to-lark-migrator", "version": "1.0.0", "description": "Migrate files from Google Drive to Lark Drive with permission mapping", "main": "src/server.js", "type": "module", "scripts": {"dev": "node --watch src/server.js", "dev:all": "concurrently \"npm run dev\" \"npm run dev:frontend\"", "dev:install": "npm install && cd frontend && npm install", "dev:frontend": "cd frontend && npm run dev", "start": "node src/server.js", "test": "node src/test-all-apis.js", "test-health": "node src/test-all-apis.js health", "test-db": "node src/test-database.js", "test-google": "node src/test-google-auth.js", "test-lark": "node src/test-lark-auth.js", "test-drive-api": "node src/test-google-drive-api.js", "test-lark-api": "node src/test-lark-drive-api.js", "check-delegation": "node src/check-domain-delegation.js", "check-delegation-watch": "node src/check-domain-delegation.js --watch", "diagnose-drive": "node src/simple-drive-test-fixed.js", "test-unit": "node --test tests/**/*.test.js", "test-unit-services": "node --test tests/services/*.test.js", "test-unit-routes": "node --test tests/routes/*.test.js", "test-unit-all": "node tests/run-all-tests.js", "test-coverage": "node --test --experimental-test-coverage tests/**/*.test.js", "check-infra": "node src/infrastructure-check.js", "check-db": "node src/test-supabase-simple.js", "check-db-setup": "node src/check-database-setup.js", "apply-schema": "node src/apply-database-schema.js", "setup-uat": "node scripts/setup-uat.js", "start:uat": "NODE_ENV=uat node --env-file=config/.env.uat src/server.js", "test:uat": "node scripts/uat-test-runner.js", "test:performance": "NODE_ENV=uat PERFORMANCE_TEST_ENABLED=true node scripts/uat-test-runner.js", "get-lark-token": "node scripts/get-lark-token.js", "check-deps": "node scripts/check-dependencies.js"}, "keywords": ["google-drive", "lark", "migration", "file-transfer"], "author": "OSP Group", "license": "MIT", "dependencies": {"@playwright/test": "^1.54.1", "@supabase/supabase-js": "^2.39.0", "axios": "^1.6.0", "chalk": "^5.4.1", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^5.1.0", "form-data": "^4.0.0", "googleapis": "^128.0.0", "jsonwebtoken": "^9.0.2", "pdfkit": "^0.17.1", "playwright": "^1.54.1"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^9.2.0"}, "packageManager": "pnpm"}