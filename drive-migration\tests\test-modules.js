import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Testing Module Imports...\n');

// Test Google Auth
console.log('=== TESTING GOOGLE AUTH ===');
try {
    const { googleAuth } = await import('./auth/google-auth.js');
    console.log('✅ Google Auth module imported successfully');
    
    // Test basic validation
    const isValid = googleAuth.validateCredentials();
    console.log('✅ Credentials validation:', isValid ? 'Valid' : 'Invalid');
    
    if (isValid) {
        console.log('📧 Service Account:', googleAuth.serviceAccountEmail);
        console.log('🆔 Project ID:', googleAuth.projectId);
    }
} catch (error) {
    console.log('❌ Google Auth import failed:', error.message);
    console.log('Stack:', error.stack);
}

// Test Lark Auth
console.log('\n=== TESTING LARK AUTH ===');
try {
    const { larkAuth } = await import('./auth/lark-auth.js');
    console.log('✅ Lark Auth module imported successfully');
    
    // Test basic validation
    const isValid = larkAuth.validateCredentials();
    console.log('✅ Credentials validation:', isValid ? 'Valid' : 'Invalid');
    
    if (isValid) {
        console.log('📱 App ID:', larkAuth.appId);
        console.log('🔗 Base URL:', larkAuth.baseUrl);
    }
} catch (error) {
    console.log('❌ Lark Auth import failed:', error.message);
    console.log('Stack:', error.stack);
}

// Test Supabase Client
console.log('\n=== TESTING SUPABASE CLIENT ===');
try {
    const { supabaseClient } = await import('./database/supabase.js');
    console.log('✅ Supabase Client module imported successfully');
    
    // Test basic connection
    const connectionTest = await supabaseClient.testConnection();
    console.log('✅ Connection test:', connectionTest.success ? 'Success' : 'Failed');
    
    if (!connectionTest.success) {
        console.log('❌ Connection error:', connectionTest.error);
    }
} catch (error) {
    console.log('❌ Supabase Client import failed:', error.message);
    console.log('Stack:', error.stack);
}

console.log('\n✅ Module testing completed!');
