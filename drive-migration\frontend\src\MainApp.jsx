import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import Navigation from "./components/Navigation";
import Download from "./pages/Download";
import Upload from "./pages/Upload";
import GoogleWorkspaceScanner from "./pages/GoogleWorkspaceScanner";
import StorageComparison from "./pages/StorageComparison";
import { ToastProvider } from "./contexts/ToastContext";
import "./MainApp.css";

function MainApp() {
  return (
    <ToastProvider>
      <Router>
        <div className="main-app">
          <Navigation />
          <div className="main-content">
            <Routes>
              <Route
                path="/"
                element={<Navigate to="/google-workspace-scanner" replace />}
              />
              <Route
                path="/google-workspace-scanner"
                element={<GoogleWorkspaceScanner />}
              />
              <Route path="/download" element={<Download />} />
              <Route path="/upload" element={<Upload />} />
              <Route path="/storage" element={<StorageComparison />} />
              {/* <Route path="/migration" element={<Migration />} /> */}
              {/* <Route path="/users" element={<UsersOverview />} /> */}
            </Routes>
          </div>
        </div>
      </Router>
    </ToastProvider>
  );
}

export default MainApp;
