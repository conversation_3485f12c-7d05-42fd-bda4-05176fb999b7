class DuplicateFileRemover {
    constructor() {
        this.ws = null;
        this.currentSessionId = null;
        this.duplicateGroups = [];
        this.selectedFiles = new Set();
        
        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
    }

    initializeElements() {
        // Input elements
        this.directoryInput = document.getElementById('directoryPath');
        this.maxWorkersInput = document.getElementById('maxWorkers');
        this.scanBtn = document.getElementById('scanBtn');
        
        // Progress elements
        this.progressSection = document.querySelector('.progress-section');
        this.progressTitle = document.getElementById('progressTitle');
        this.progressPercent = document.getElementById('progressPercent');
        this.progressFill = document.getElementById('progressFill');
        this.progressMessage = document.getElementById('progressMessage');
        this.progressStats = document.getElementById('progressStats');

        // Progress control elements
        this.pauseBtn = document.getElementById('pauseBtn');
        this.resumeBtn = document.getElementById('resumeBtn');
        this.stopBtn = document.getElementById('stopBtn');
        
        // Real-time results elements
        this.realtimeResultsSection = document.querySelector('.realtime-results-section');
        this.realtimeDuplicateCount = document.getElementById('realtimeDuplicateCount');
        this.realtimeDuplicateGroupsContainer = document.getElementById('realtimeDuplicateGroups');
        this.selectAllRealtimeBtn = document.getElementById('selectAllRealtimeBtn');
        this.deselectAllRealtimeBtn = document.getElementById('deselectAllRealtimeBtn');
        this.deleteAllDuplicatesBtn = document.getElementById('deleteAllDuplicatesBtn');
        this.deleteSelectedRealtimeBtn = document.getElementById('deleteSelectedRealtimeBtn');

        // Final results elements
        this.resultsSection = document.querySelector('.results-section');
        this.duplicateCount = document.getElementById('duplicateCount');
        this.duplicateGroupsContainer = document.getElementById('duplicateGroups');
        this.selectAllBtn = document.getElementById('selectAllBtn');
        this.deselectAllBtn = document.getElementById('deselectAllBtn');
        this.deleteAllDuplicatesFinalBtn = document.getElementById('deleteAllDuplicatesFinalBtn');
        this.deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
        
        // Delete progress elements
        this.deleteProgressSection = document.querySelector('.delete-progress-section');
        this.deleteProgressPercent = document.getElementById('deleteProgressPercent');
        this.deleteProgressFill = document.getElementById('deleteProgressFill');
        this.deleteProgressMessage = document.getElementById('deleteProgressMessage');
        this.deleteProgressStats = document.getElementById('deleteProgressStats');
        this.deleteFileDetails = document.getElementById('deleteFileDetails');
        
        // Delete results elements
        this.deleteResultsSection = document.querySelector('.delete-results-section');
        this.deleteResults = document.getElementById('deleteResults');
    }

    bindEvents() {
        this.scanBtn.addEventListener('click', () => this.startScan());

        // Progress control events
        this.pauseBtn.addEventListener('click', () => this.pauseScan());
        this.resumeBtn.addEventListener('click', () => this.resumeScan());
        this.stopBtn.addEventListener('click', () => this.stopScan());

        // Real-time results events
        this.selectAllRealtimeBtn.addEventListener('click', () => this.selectAllFiles(true));
        this.deselectAllRealtimeBtn.addEventListener('click', () => this.deselectAllFiles(true));
        this.deleteAllDuplicatesBtn.addEventListener('click', () => this.deleteAllDuplicates(true));
        this.deleteSelectedRealtimeBtn.addEventListener('click', () => this.deleteSelectedFiles(true));

        // Final results events
        this.selectAllBtn.addEventListener('click', () => this.selectAllFiles(false));
        this.deselectAllBtn.addEventListener('click', () => this.deselectAllFiles(false));
        this.deleteAllDuplicatesFinalBtn.addEventListener('click', () => this.deleteAllDuplicates(false));
        this.deleteSelectedBtn.addEventListener('click', () => this.deleteSelectedFiles(false));

        // Enter key support for directory input
        this.directoryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.startScan();
            }
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.connectWebSocket(), 3000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'progress':
                this.updateProgress(data.data);
                if (data.data.phase === 'duplicate_found') {
                    this.handleDuplicateFound(data.data);
                }
                break;
            case 'complete':
                this.handleScanComplete(data.data);
                break;
            case 'error':
                this.handleScanError(data.data);
                break;
            case 'deleteProgress':
                this.updateDeleteProgress(data.data);
                break;
            case 'deleteComplete':
                this.handleDeleteComplete(data.data);
                break;
            case 'deleteError':
                this.handleDeleteError(data.data);
                break;
        }
    }

    async pauseScan() {
        if (!this.currentSessionId) return;

        try {
            await fetch('/api/scan/pause', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sessionId: this.currentSessionId })
            });

            this.pauseBtn.style.display = 'none';
            this.resumeBtn.style.display = 'inline-block';
        } catch (error) {
            console.error('Error pausing scan:', error);
        }
    }

    async resumeScan() {
        if (!this.currentSessionId) return;

        try {
            await fetch('/api/scan/resume', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sessionId: this.currentSessionId })
            });

            this.resumeBtn.style.display = 'none';
            this.pauseBtn.style.display = 'inline-block';
        } catch (error) {
            console.error('Error resuming scan:', error);
        }
    }

    async stopScan() {
        if (!this.currentSessionId) return;

        const confirmed = confirm('Bạn có chắc chắn muốn dừng quét? Tiến trình sẽ bị mất.');
        if (!confirmed) return;

        try {
            await fetch('/api/scan/stop', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sessionId: this.currentSessionId })
            });

            this.hideProgressControls();
            this.showScanLoading(false);
            this.progressSection.style.display = 'none';
        } catch (error) {
            console.error('Error stopping scan:', error);
        }
    }

    async startScan() {
        const directoryPath = this.directoryInput.value.trim();
        const maxWorkers = parseInt(this.maxWorkersInput.value) || 4;

        if (!directoryPath) {
            alert('Vui lòng nhập đường dẫn thư mục');
            return;
        }

        this.showScanLoading(true);
        this.hideResults();
        this.hideDeleteResults();
        this.hideRealtimeResults();
        this.duplicateGroups = [];
        this.selectedFiles.clear();

        try {
            const response = await fetch('/api/scan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ directoryPath, maxWorkers }),
            });

            const result = await response.json();

            if (response.ok) {
                this.currentSessionId = result.sessionId;
                this.registerWebSocketSession();
                this.showProgress();
            } else {
                throw new Error(result.error || 'Lỗi không xác định');
            }
        } catch (error) {
            this.showScanLoading(false);
            alert(`Lỗi: ${error.message}`);
        }
    }

    registerWebSocketSession() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN && this.currentSessionId) {
            this.ws.send(JSON.stringify({
                type: 'register',
                sessionId: this.currentSessionId
            }));
        }
    }

    showScanLoading(loading) {
        const btnText = this.scanBtn.querySelector('.btn-text');
        const btnLoading = this.scanBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-flex';
            this.scanBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            this.scanBtn.disabled = false;
        }
    }

    showProgress() {
        this.progressSection.style.display = 'block';
        this.resultsSection.style.display = 'none';
        this.showProgressControls();
    }

    showProgressControls() {
        this.pauseBtn.style.display = 'inline-block';
        this.resumeBtn.style.display = 'none';
        this.stopBtn.style.display = 'inline-block';
    }

    hideProgressControls() {
        this.pauseBtn.style.display = 'none';
        this.resumeBtn.style.display = 'none';
        this.stopBtn.style.display = 'none';
    }

    hideResults() {
        this.resultsSection.style.display = 'none';
    }

    hideDeleteResults() {
        this.deleteResultsSection.style.display = 'none';
    }

    hideRealtimeResults() {
        this.realtimeResultsSection.style.display = 'none';
    }

    showRealtimeResults() {
        this.realtimeResultsSection.style.display = 'block';
    }

    handleDuplicateFound(data) {
        if (!data.duplicateGroup) return;

        // Add to duplicateGroups array
        this.duplicateGroups.push(data.duplicateGroup);

        // Show real-time results section
        this.showRealtimeResults();

        // Update count
        this.realtimeDuplicateCount.textContent = this.duplicateGroups.length;

        // Add the new duplicate group to the display
        const groupElement = this.createDuplicateGroupElement(data.duplicateGroup, this.duplicateGroups.length - 1, true);
        this.realtimeDuplicateGroupsContainer.appendChild(groupElement);

        // Update buttons
        this.updateDeleteButtons();
    }

    updateProgress(progress) {
        const percent = progress.total > 0 ? Math.round((progress.processed / progress.total) * 100) : 0;

        this.progressPercent.textContent = `${percent}%`;
        this.progressFill.style.width = `${percent}%`;
        this.progressMessage.textContent = progress.message || 'Đang xử lý...';
        this.progressStats.textContent = `${progress.processed} / ${progress.total} files`;

        if (progress.phase === 'counting') {
            this.progressTitle.textContent = 'Đang đếm file...';
        } else if (progress.phase === 'preparing') {
            this.progressTitle.textContent = 'Đang chuẩn bị quét song song...';
        } else if (progress.phase === 'scanning') {
            this.progressTitle.textContent = `Đang quét thư mục... (${progress.workerId ? 'Worker ' + progress.workerId : ''})`;
        } else if (progress.phase === 'hashing') {
            this.progressTitle.textContent = `Đang tính hash... (${progress.workerId ? 'Worker ' + progress.workerId : ''})`;
        } else if (progress.phase === 'duplicate_found') {
            this.progressTitle.textContent = '🎯 Đang phát hiện file trùng lặp...';
        } else if (progress.phase === 'checking') {
            this.progressTitle.textContent = `📏 Đang kiểm tra file trùng lặp... (${progress.workerId ? 'Worker ' + progress.workerId : ''})`;
        } else if (progress.phase === 'paused') {
            this.progressTitle.textContent = '⏸️ Quét đã tạm dừng';
            this.pauseBtn.style.display = 'none';
            this.resumeBtn.style.display = 'inline-block';
        } else if (progress.phase === 'resumed') {
            this.progressTitle.textContent = '▶️ Tiếp tục quét...';
            this.resumeBtn.style.display = 'none';
            this.pauseBtn.style.display = 'inline-block';
        } else if (progress.phase === 'stopped') {
            this.progressTitle.textContent = '⏹️ Quét đã dừng';
            this.hideProgressControls();
        }
    }

    async handleScanComplete(data) {
        this.showScanLoading(false);
        this.progressSection.style.display = 'none';
        this.hideProgressControls();

        // Fetch full duplicate results from server
        try {
            const response = await fetch(`/api/duplicates/${data.sessionId}`);
            const result = await response.json();

            if (response.ok) {
                this.duplicateGroups = result.duplicates;
                this.displayResults();
            } else {
                throw new Error(result.error || 'Failed to fetch results');
            }
        } catch (error) {
            console.error('Error fetching results:', error);
            alert(`Lỗi tải kết quả: ${error.message}`);
        }
    }

    handleScanError(error) {
        this.showScanLoading(false);
        this.progressSection.style.display = 'none';
        alert(`Lỗi quét thư mục: ${error.message}`);
    }

    displayResults() {
        this.duplicateCount.textContent = this.duplicateGroups.length;
        this.resultsSection.style.display = 'block';
        
        this.duplicateGroupsContainer.innerHTML = '';
        this.selectedFiles.clear();
        
        this.duplicateGroups.forEach((group, groupIndex) => {
            const groupElement = this.createDuplicateGroupElement(group, groupIndex);
            this.duplicateGroupsContainer.appendChild(groupElement);
        });
        
        this.updateDeleteButton();
    }

    createDuplicateGroupElement(group, groupIndex, isRealtime = false) {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'duplicate-group';
        groupDiv.dataset.groupIndex = groupIndex;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'duplicate-group-header';

        const infoDiv = document.createElement('div');
        infoDiv.className = 'duplicate-group-info';
        infoDiv.textContent = `${group.files.length} file trùng lặp trong ${group.directory}`;

        const sizeSpan = document.createElement('span');
        sizeSpan.className = 'duplicate-group-size';
        sizeSpan.textContent = this.formatFileSize(group.size);

        headerDiv.appendChild(infoDiv);
        headerDiv.appendChild(sizeSpan);

        const filesDiv = document.createElement('div');
        filesDiv.className = 'duplicate-files';

        group.files.forEach((file, fileIndex) => {
            const fileDiv = this.createDuplicateFileElement(file, groupIndex, fileIndex, isRealtime);
            filesDiv.appendChild(fileDiv);
        });

        groupDiv.appendChild(headerDiv);
        groupDiv.appendChild(filesDiv);

        return groupDiv;
    }

    createDuplicateFileElement(file, groupIndex, fileIndex, isRealtime = false) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'duplicate-file';

        // Add special styling for original vs duplicate files
        if (file.isOriginal) {
            fileDiv.classList.add('file-original');
        } else {
            fileDiv.classList.add('file-duplicate');
        }

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `file-${isRealtime ? 'rt-' : ''}${groupIndex}-${fileIndex}`;

        // Don't auto-select original files
        if (!file.isOriginal) {
            checkbox.checked = true;
            this.selectedFiles.add(file.path);
        }

        checkbox.addEventListener('change', () => {
            if (checkbox.checked) {
                this.selectedFiles.add(file.path);
            } else {
                this.selectedFiles.delete(file.path);
            }
            this.updateDeleteButtons();
        });

        const fileInfoDiv = document.createElement('div');
        fileInfoDiv.className = 'file-info';

        const fileNameDiv = document.createElement('div');
        fileNameDiv.className = 'file-name';
        fileNameDiv.textContent = file.name;

        const filePathDiv = document.createElement('div');
        filePathDiv.className = 'file-path';
        filePathDiv.textContent = file.path;

        fileInfoDiv.appendChild(fileNameDiv);
        fileInfoDiv.appendChild(filePathDiv);

        fileDiv.appendChild(checkbox);
        fileDiv.appendChild(fileInfoDiv);

        return fileDiv;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    selectAllFiles(isRealtime = false) {
        const container = isRealtime ? this.realtimeDuplicateGroupsContainer : this.duplicateGroupsContainer;
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            const fileId = checkbox.id;
            const parts = fileId.split('-');
            let groupIndex, fileIndex;

            if (parts[1] === 'rt') {
                groupIndex = parseInt(parts[2]);
                fileIndex = parseInt(parts[3]);
            } else {
                groupIndex = parseInt(parts[1]);
                fileIndex = parseInt(parts[2]);
            }

            const file = this.duplicateGroups[groupIndex].files[fileIndex];
            this.selectedFiles.add(file.path);
        });
        this.updateDeleteButtons();
    }

    deselectAllFiles(isRealtime = false) {
        const container = isRealtime ? this.realtimeDuplicateGroupsContainer : this.duplicateGroupsContainer;
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        this.selectedFiles.clear();
        this.updateDeleteButtons();
    }

    updateDeleteButtons() {
        const hasSelected = this.selectedFiles.size > 0;
        const hasDuplicates = this.duplicateGroups.length > 0;

        // Real-time buttons
        if (this.deleteSelectedRealtimeBtn) {
            this.deleteSelectedRealtimeBtn.disabled = !hasSelected;
        }
        if (this.deleteAllDuplicatesBtn) {
            this.deleteAllDuplicatesBtn.disabled = !hasDuplicates;
        }

        // Final buttons
        if (this.deleteSelectedBtn) {
            this.deleteSelectedBtn.disabled = !hasSelected;
        }
        if (this.deleteAllDuplicatesFinalBtn) {
            this.deleteAllDuplicatesFinalBtn.disabled = !hasDuplicates;
        }
    }

    async deleteAllDuplicates(isRealtime = false) {
        if (this.duplicateGroups.length === 0) {
            alert('Không có file trùng lặp nào để xóa');
            return;
        }

        // Count duplicate files (not original files)
        let duplicateCount = 0;
        this.duplicateGroups.forEach(group => {
            group.files.forEach(file => {
                if (!file.isOriginal) {
                    duplicateCount++;
                }
            });
        });

        if (duplicateCount === 0) {
            alert('Không có file bản sao nào để xóa');
            return;
        }

        const confirmed = confirm(`Bạn có chắc chắn muốn xóa tất cả ${duplicateCount} file bản sao?\nCác file gốc sẽ được giữ lại.`);
        if (!confirmed) {
            return;
        }

        this.showDeleteLoading(true, isRealtime);
        this.showDeleteProgress();

        try {
            const response = await fetch(`/api/delete-all-duplicates/${this.currentSessionId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Lỗi không xác định');
            }

            // The progress will be handled via WebSocket

        } catch (error) {
            this.showDeleteLoading(false, isRealtime);
            this.deleteProgressSection.style.display = 'none';
            alert(`Lỗi: ${error.message}`);
        }
    }

    async deleteSelectedFiles(isRealtime = false) {
        if (this.selectedFiles.size === 0) {
            alert('Vui lòng chọn ít nhất một file để xóa');
            return;
        }

        const confirmed = confirm(`Bạn có chắc chắn muốn xóa ${this.selectedFiles.size} file đã chọn?`);
        if (!confirmed) {
            return;
        }

        this.showDeleteLoading(true, isRealtime);
        this.showDeleteProgress();

        try {
            const response = await fetch('/api/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filePaths: Array.from(this.selectedFiles) }),
            });

            const result = await response.json();

            if (response.ok) {
                this.currentSessionId = result.sessionId;
                this.registerWebSocketSession();
            } else {
                throw new Error(result.error || 'Lỗi không xác định');
            }
        } catch (error) {
            this.showDeleteLoading(false, isRealtime);
            this.deleteProgressSection.style.display = 'none';
            alert(`Lỗi: ${error.message}`);
        }
    }

    showDeleteLoading(loading, isRealtime = false) {
        const buttons = isRealtime ?
            [this.deleteSelectedRealtimeBtn, this.deleteAllDuplicatesBtn] :
            [this.deleteSelectedBtn, this.deleteAllDuplicatesFinalBtn];

        buttons.forEach(btn => {
            if (!btn) return;

            const btnText = btn.querySelector('.btn-text');
            const btnLoading = btn.querySelector('.btn-loading');

            if (loading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-flex';
                btn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
            }
        });

        if (!loading) {
            this.updateDeleteButtons();
        }
    }

    showDeleteProgress() {
        this.deleteProgressSection.style.display = 'block';
        this.deleteResultsSection.style.display = 'none';
        this.deleteFileDetails.innerHTML = ''; // Clear previous details
    }

    updateDeleteProgress(progress) {
        const percent = progress.total > 0 ? Math.round((progress.processed / progress.total) * 100) : 0;

        this.deleteProgressPercent.textContent = `${percent}%`;
        this.deleteProgressFill.style.width = `${percent}%`;
        this.deleteProgressMessage.textContent = progress.message || 'Đang xóa...';
        this.deleteProgressStats.textContent = `${progress.processed} / ${progress.total} files`;

        // Add detailed file deletion info
        if (progress.fileName && progress.status) {
            this.addDeleteDetail(progress);
        }
    }

    addDeleteDetail(progress) {
        const detailDiv = document.createElement('div');
        detailDiv.className = `delete-item ${progress.status}`;

        let statusIcon = '';
        let statusText = '';

        switch (progress.status) {
            case 'deleting':
                statusIcon = '🔄';
                statusText = 'Đang xóa';
                detailDiv.classList.add('current');
                break;
            case 'success':
                statusIcon = '✅';
                statusText = 'Đã xóa';
                break;
            case 'error':
                statusIcon = '❌';
                statusText = 'Lỗi';
                break;
        }

        detailDiv.innerHTML = `
            <span class="status">${statusIcon} ${statusText}:</span>
            <span class="file-name">${progress.fileName}</span>
            ${progress.error ? `<br><small style="color: #dc3545;">Lỗi: ${progress.error}</small>` : ''}
        `;

        // Remove previous "current" status
        if (progress.status === 'deleting') {
            const currentItems = this.deleteFileDetails.querySelectorAll('.current');
            currentItems.forEach(item => item.classList.remove('current'));
        }

        this.deleteFileDetails.appendChild(detailDiv);

        // Auto scroll to bottom
        this.deleteFileDetails.scrollTop = this.deleteFileDetails.scrollHeight;
    }

    handleDeleteComplete(result) {
        this.showDeleteLoading(false);
        this.deleteProgressSection.style.display = 'none';

        this.displayDeleteResults(result);

        // Remove successfully deleted files from the UI
        result.successful.forEach(filePath => {
            this.selectedFiles.delete(filePath);
            this.removeFileFromUI(filePath);
        });

        this.updateDeleteButtons();

        // Update counts
        this.realtimeDuplicateCount.textContent = this.duplicateGroups.length;
        this.duplicateCount.textContent = this.duplicateGroups.length;
    }

    handleDeleteError(error) {
        this.showDeleteLoading(false);
        this.deleteProgressSection.style.display = 'none';
        alert(`Lỗi xóa file: ${error.message}`);
    }

    displayDeleteResults(result) {
        this.deleteResultsSection.style.display = 'block';
        
        let html = '<div class="delete-results">';
        
        if (result.successful.length > 0) {
            html += `<div class="delete-success">✅ Đã xóa thành công ${result.successful.length} file</div>`;
        }
        
        if (result.failed.length > 0) {
            html += `<div class="delete-error">❌ Không thể xóa ${result.failed.length} file:</div>`;
            result.failed.forEach(failure => {
                html += `<div class="delete-error">• ${failure.path}: ${failure.error}</div>`;
            });
        }
        
        html += '</div>';
        this.deleteResults.innerHTML = html;
    }

    removeFileFromUI(filePath) {
        // Remove from both real-time and final results
        [this.realtimeDuplicateGroupsContainer, this.duplicateGroupsContainer].forEach(container => {
            if (!container) return;

            const checkboxes = container.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                const fileId = checkbox.id;
                const parts = fileId.split('-');
                let groupIndex, fileIndex;

                if (parts[1] === 'rt') {
                    groupIndex = parseInt(parts[2]);
                    fileIndex = parseInt(parts[3]);
                } else {
                    groupIndex = parseInt(parts[1]);
                    fileIndex = parseInt(parts[2]);
                }

                if (groupIndex >= this.duplicateGroups.length) return;

                const file = this.duplicateGroups[groupIndex].files[fileIndex];

                if (file && file.path === filePath) {
                    const fileElement = checkbox.closest('.duplicate-file');
                    fileElement.remove();

                    // Remove from duplicateGroups data
                    this.duplicateGroups[groupIndex].files.splice(fileIndex, 1);

                    // If group has only 1 file left, remove the entire group
                    if (this.duplicateGroups[groupIndex].files.length <= 1) {
                        const groupElement = checkbox.closest('.duplicate-group');
                        groupElement.remove();
                        this.duplicateGroups.splice(groupIndex, 1);

                        // Update group indices for remaining groups
                        this.updateGroupIndices();
                    }
                }
            });
        });
    }

    updateGroupIndices() {
        // Update group indices in both containers after group removal
        [this.realtimeDuplicateGroupsContainer, this.duplicateGroupsContainer].forEach(container => {
            if (!container) return;

            const groups = container.querySelectorAll('.duplicate-group');
            groups.forEach((group, newIndex) => {
                group.dataset.groupIndex = newIndex;

                // Update checkbox IDs
                const checkboxes = group.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach((checkbox, fileIndex) => {
                    const isRealtime = container === this.realtimeDuplicateGroupsContainer;
                    checkbox.id = `file-${isRealtime ? 'rt-' : ''}${newIndex}-${fileIndex}`;
                });
            });
        });
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DuplicateFileRemover();
});
