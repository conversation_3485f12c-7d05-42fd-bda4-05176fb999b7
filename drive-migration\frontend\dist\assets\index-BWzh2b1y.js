(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(s){if(s.ep)return;s.ep=!0;const a=n(s);fetch(s.href,a)}})();function gd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Bo={exports:{}},Rs={},Ao={exports:{}},B={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jr=Symbol.for("react.element"),xd=Symbol.for("react.portal"),yd=Symbol.for("react.fragment"),jd=Symbol.for("react.strict_mode"),wd=Symbol.for("react.profiler"),Nd=Symbol.for("react.provider"),Sd=Symbol.for("react.context"),kd=Symbol.for("react.forward_ref"),Cd=Symbol.for("react.suspense"),Ed=Symbol.for("react.memo"),_d=Symbol.for("react.lazy"),Si=Symbol.iterator;function Pd(e){return e===null||typeof e!="object"?null:(e=Si&&e[Si]||e["@@iterator"],typeof e=="function"?e:null)}var Ho={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Wo=Object.assign,Vo={};function _n(e,t,n){this.props=e,this.context=t,this.refs=Vo,this.updater=n||Ho}_n.prototype.isReactComponent={};_n.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};_n.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function bo(){}bo.prototype=_n.prototype;function ja(e,t,n){this.props=e,this.context=t,this.refs=Vo,this.updater=n||Ho}var wa=ja.prototype=new bo;wa.constructor=ja;Wo(wa,_n.prototype);wa.isPureReactComponent=!0;var ki=Array.isArray,Qo=Object.prototype.hasOwnProperty,Na={current:null},Ko={key:!0,ref:!0,__self:!0,__source:!0};function Go(e,t,n){var r,s={},a=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(a=""+t.key),t)Qo.call(t,r)&&!Ko.hasOwnProperty(r)&&(s[r]=t[r]);var o=arguments.length-2;if(o===1)s.children=n;else if(1<o){for(var u=Array(o),c=0;c<o;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)s[r]===void 0&&(s[r]=o[r]);return{$$typeof:jr,type:e,key:a,ref:i,props:s,_owner:Na.current}}function Ld(e,t){return{$$typeof:jr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Sa(e){return typeof e=="object"&&e!==null&&e.$$typeof===jr}function Fd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ci=/\/+/g;function Xs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Fd(""+e.key):t.toString(36)}function Qr(e,t,n,r,s){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(a){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case jr:case xd:i=!0}}if(i)return i=e,s=s(i),e=r===""?"."+Xs(i,0):r,ki(s)?(n="",e!=null&&(n=e.replace(Ci,"$&/")+"/"),Qr(s,t,n,"",function(c){return c})):s!=null&&(Sa(s)&&(s=Ld(s,n+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(Ci,"$&/")+"/")+e)),t.push(s)),1;if(i=0,r=r===""?".":r+":",ki(e))for(var o=0;o<e.length;o++){a=e[o];var u=r+Xs(a,o);i+=Qr(a,t,n,u,s)}else if(u=Pd(e),typeof u=="function")for(e=u.call(e),o=0;!(a=e.next()).done;)a=a.value,u=r+Xs(a,o++),i+=Qr(a,t,n,u,s);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Pr(e,t,n){if(e==null)return e;var r=[],s=0;return Qr(e,r,"","",function(a){return t.call(n,a,s++)}),r}function Rd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Kr={transition:null},Td={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Kr,ReactCurrentOwner:Na};function Yo(){throw Error("act(...) is not supported in production builds of React.")}B.Children={map:Pr,forEach:function(e,t,n){Pr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Pr(e,function(){t++}),t},toArray:function(e){return Pr(e,function(t){return t})||[]},only:function(e){if(!Sa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};B.Component=_n;B.Fragment=yd;B.Profiler=wd;B.PureComponent=ja;B.StrictMode=jd;B.Suspense=Cd;B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Td;B.act=Yo;B.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Wo({},e.props),s=e.key,a=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,i=Na.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(u in t)Qo.call(t,u)&&!Ko.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&o!==void 0?o[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){o=Array(u);for(var c=0;c<u;c++)o[c]=arguments[c+2];r.children=o}return{$$typeof:jr,type:e.type,key:s,ref:a,props:r,_owner:i}};B.createContext=function(e){return e={$$typeof:Sd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Nd,_context:e},e.Consumer=e};B.createElement=Go;B.createFactory=function(e){var t=Go.bind(null,e);return t.type=e,t};B.createRef=function(){return{current:null}};B.forwardRef=function(e){return{$$typeof:kd,render:e}};B.isValidElement=Sa;B.lazy=function(e){return{$$typeof:_d,_payload:{_status:-1,_result:e},_init:Rd}};B.memo=function(e,t){return{$$typeof:Ed,type:e,compare:t===void 0?null:t}};B.startTransition=function(e){var t=Kr.transition;Kr.transition={};try{e()}finally{Kr.transition=t}};B.unstable_act=Yo;B.useCallback=function(e,t){return xe.current.useCallback(e,t)};B.useContext=function(e){return xe.current.useContext(e)};B.useDebugValue=function(){};B.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};B.useEffect=function(e,t){return xe.current.useEffect(e,t)};B.useId=function(){return xe.current.useId()};B.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};B.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};B.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};B.useMemo=function(e,t){return xe.current.useMemo(e,t)};B.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};B.useRef=function(e){return xe.current.useRef(e)};B.useState=function(e){return xe.current.useState(e)};B.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};B.useTransition=function(){return xe.current.useTransition()};B.version="18.3.1";Ao.exports=B;var g=Ao.exports;const Dd=gd(g);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zd=g,Md=Symbol.for("react.element"),$d=Symbol.for("react.fragment"),Id=Object.prototype.hasOwnProperty,Od=zd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ud={key:!0,ref:!0,__self:!0,__source:!0};function Xo(e,t,n){var r,s={},a=null,i=null;n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Id.call(t,r)&&!Ud.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Md,type:e,key:a,ref:i,props:s,_owner:Od.current}}Rs.Fragment=$d;Rs.jsx=Xo;Rs.jsxs=Xo;Bo.exports=Rs;var l=Bo.exports,kl={},Jo={exports:{}},Fe={},Zo={exports:{}},qo={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,$){var O=R.length;R.push($);e:for(;0<O;){var J=O-1>>>1,H=R[J];if(0<s(H,$))R[J]=$,R[O]=H,O=J;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var $=R[0],O=R.pop();if(O!==$){R[0]=O;e:for(var J=0,H=R.length,qt=H>>>1;J<qt;){var rt=2*(J+1)-1,Dn=R[rt],T=rt+1,I=R[T];if(0>s(Dn,O))T<H&&0>s(I,Dn)?(R[J]=I,R[T]=O,J=T):(R[J]=Dn,R[rt]=O,J=rt);else if(T<H&&0>s(I,O))R[J]=I,R[T]=O,J=T;else break e}}return $}function s(R,$){var O=R.sortIndex-$.sortIndex;return O!==0?O:R.id-$.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var i=Date,o=i.now();e.unstable_now=function(){return i.now()-o}}var u=[],c=[],f=1,v=null,h=3,S=!1,N=!1,_=!1,L=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(R){for(var $=n(c);$!==null;){if($.callback===null)r(c);else if($.startTime<=R)r(c),$.sortIndex=$.expirationTime,t(u,$);else break;$=n(c)}}function w(R){if(_=!1,m(R),!N)if(n(u)!==null)N=!0,Xe(x);else{var $=n(c);$!==null&&V(w,$.startTime-R)}}function x(R,$){N=!1,_&&(_=!1,p(j),j=-1),S=!0;var O=h;try{for(m($),v=n(u);v!==null&&(!(v.expirationTime>$)||R&&!k());){var J=v.callback;if(typeof J=="function"){v.callback=null,h=v.priorityLevel;var H=J(v.expirationTime<=$);$=e.unstable_now(),typeof H=="function"?v.callback=H:v===n(u)&&r(u),m($)}else r(u);v=n(u)}if(v!==null)var qt=!0;else{var rt=n(c);rt!==null&&V(w,rt.startTime-$),qt=!1}return qt}finally{v=null,h=O,S=!1}}var E=!1,P=null,j=-1,C=5,y=-1;function k(){return!(e.unstable_now()-y<C)}function D(){if(P!==null){var R=e.unstable_now();y=R;var $=!0;try{$=P(!0,R)}finally{$?M():(E=!1,P=null)}}else E=!1}var M;if(typeof d=="function")M=function(){d(D)};else if(typeof MessageChannel<"u"){var U=new MessageChannel,Ye=U.port2;U.port1.onmessage=D,M=function(){Ye.postMessage(null)}}else M=function(){L(D,0)};function Xe(R){P=R,E||(E=!0,M())}function V(R,$){j=L(function(){R(e.unstable_now())},$)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){N||S||(N=!0,Xe(x))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(R){switch(h){case 1:case 2:case 3:var $=3;break;default:$=h}var O=h;h=$;try{return R()}finally{h=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,$){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var O=h;h=R;try{return $()}finally{h=O}},e.unstable_scheduleCallback=function(R,$,O){var J=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?J+O:J):O=J,R){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=O+H,R={id:f++,callback:$,priorityLevel:R,startTime:O,expirationTime:H,sortIndex:-1},O>J?(R.sortIndex=O,t(c,R),n(u)===null&&R===n(c)&&(_?(p(j),j=-1):_=!0,V(w,O-J))):(R.sortIndex=H,t(u,R),N||S||(N=!0,Xe(x))),R},e.unstable_shouldYield=k,e.unstable_wrapCallback=function(R){var $=h;return function(){var O=h;h=$;try{return R.apply(this,arguments)}finally{h=O}}}})(qo);Zo.exports=qo;var Bd=Zo.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ad=g,Le=Bd;function F(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var eu=new Set,nr={};function Jt(e,t){jn(e,t),jn(e+"Capture",t)}function jn(e,t){for(nr[e]=t,e=0;e<t.length;e++)eu.add(t[e])}var ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Cl=Object.prototype.hasOwnProperty,Hd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ei={},_i={};function Wd(e){return Cl.call(_i,e)?!0:Cl.call(Ei,e)?!1:Hd.test(e)?_i[e]=!0:(Ei[e]=!0,!1)}function Vd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function bd(e,t,n,r){if(t===null||typeof t>"u"||Vd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ye(e,t,n,r,s,a,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var de={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){de[e]=new ye(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];de[t]=new ye(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){de[e]=new ye(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){de[e]=new ye(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){de[e]=new ye(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){de[e]=new ye(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){de[e]=new ye(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){de[e]=new ye(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){de[e]=new ye(e,5,!1,e.toLowerCase(),null,!1,!1)});var ka=/[\-:]([a-z])/g;function Ca(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ka,Ca);de[t]=new ye(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ka,Ca);de[t]=new ye(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ka,Ca);de[t]=new ye(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){de[e]=new ye(e,1,!1,e.toLowerCase(),null,!1,!1)});de.xlinkHref=new ye("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){de[e]=new ye(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ea(e,t,n,r){var s=de.hasOwnProperty(t)?de[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(bd(t,n,s,r)&&(n=null),r||s===null?Wd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var mt=Ad.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Lr=Symbol.for("react.element"),tn=Symbol.for("react.portal"),nn=Symbol.for("react.fragment"),_a=Symbol.for("react.strict_mode"),El=Symbol.for("react.profiler"),tu=Symbol.for("react.provider"),nu=Symbol.for("react.context"),Pa=Symbol.for("react.forward_ref"),_l=Symbol.for("react.suspense"),Pl=Symbol.for("react.suspense_list"),La=Symbol.for("react.memo"),gt=Symbol.for("react.lazy"),ru=Symbol.for("react.offscreen"),Pi=Symbol.iterator;function zn(e){return e===null||typeof e!="object"?null:(e=Pi&&e[Pi]||e["@@iterator"],typeof e=="function"?e:null)}var ee=Object.assign,Js;function Wn(e){if(Js===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Js=t&&t[1]||""}return`
`+Js+e}var Zs=!1;function qs(e,t){if(!e||Zs)return"";Zs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),a=r.stack.split(`
`),i=s.length-1,o=a.length-1;1<=i&&0<=o&&s[i]!==a[o];)o--;for(;1<=i&&0<=o;i--,o--)if(s[i]!==a[o]){if(i!==1||o!==1)do if(i--,o--,0>o||s[i]!==a[o]){var u=`
`+s[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=i&&0<=o);break}}}finally{Zs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Wn(e):""}function Qd(e){switch(e.tag){case 5:return Wn(e.type);case 16:return Wn("Lazy");case 13:return Wn("Suspense");case 19:return Wn("SuspenseList");case 0:case 2:case 15:return e=qs(e.type,!1),e;case 11:return e=qs(e.type.render,!1),e;case 1:return e=qs(e.type,!0),e;default:return""}}function Ll(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case nn:return"Fragment";case tn:return"Portal";case El:return"Profiler";case _a:return"StrictMode";case _l:return"Suspense";case Pl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case nu:return(e.displayName||"Context")+".Consumer";case tu:return(e._context.displayName||"Context")+".Provider";case Pa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case La:return t=e.displayName||null,t!==null?t:Ll(e.type)||"Memo";case gt:t=e._payload,e=e._init;try{return Ll(e(t))}catch{}}return null}function Kd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ll(t);case 8:return t===_a?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Rt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function su(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Gd(e){var t=su(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(i){r=""+i,a.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Fr(e){e._valueTracker||(e._valueTracker=Gd(e))}function lu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=su(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function as(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Fl(e,t){var n=t.checked;return ee({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Li(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Rt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function au(e,t){t=t.checked,t!=null&&Ea(e,"checked",t,!1)}function Rl(e,t){au(e,t);var n=Rt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Tl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Tl(e,t.type,Rt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Fi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Tl(e,t,n){(t!=="number"||as(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Vn=Array.isArray;function pn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Rt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Dl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(F(91));return ee({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ri(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(F(92));if(Vn(n)){if(1<n.length)throw Error(F(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Rt(n)}}function iu(e,t){var n=Rt(t.value),r=Rt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ti(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ou(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ou(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Rr,uu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Rr=Rr||document.createElement("div"),Rr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Rr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function rr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Kn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Yd=["Webkit","ms","Moz","O"];Object.keys(Kn).forEach(function(e){Yd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Kn[t]=Kn[e]})});function cu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Kn.hasOwnProperty(e)&&Kn[e]?(""+t).trim():t+"px"}function du(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=cu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Xd=ee({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ml(e,t){if(t){if(Xd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(F(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(F(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(F(61))}if(t.style!=null&&typeof t.style!="object")throw Error(F(62))}}function $l(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Il=null;function Fa(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ol=null,mn=null,vn=null;function Di(e){if(e=Sr(e)){if(typeof Ol!="function")throw Error(F(280));var t=e.stateNode;t&&(t=$s(t),Ol(e.stateNode,e.type,t))}}function fu(e){mn?vn?vn.push(e):vn=[e]:mn=e}function hu(){if(mn){var e=mn,t=vn;if(vn=mn=null,Di(e),t)for(e=0;e<t.length;e++)Di(t[e])}}function pu(e,t){return e(t)}function mu(){}var el=!1;function vu(e,t,n){if(el)return e(t,n);el=!0;try{return pu(e,t,n)}finally{el=!1,(mn!==null||vn!==null)&&(mu(),hu())}}function sr(e,t){var n=e.stateNode;if(n===null)return null;var r=$s(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(F(231,t,typeof n));return n}var Ul=!1;if(ct)try{var Mn={};Object.defineProperty(Mn,"passive",{get:function(){Ul=!0}}),window.addEventListener("test",Mn,Mn),window.removeEventListener("test",Mn,Mn)}catch{Ul=!1}function Jd(e,t,n,r,s,a,i,o,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var Gn=!1,is=null,os=!1,Bl=null,Zd={onError:function(e){Gn=!0,is=e}};function qd(e,t,n,r,s,a,i,o,u){Gn=!1,is=null,Jd.apply(Zd,arguments)}function ef(e,t,n,r,s,a,i,o,u){if(qd.apply(this,arguments),Gn){if(Gn){var c=is;Gn=!1,is=null}else throw Error(F(198));os||(os=!0,Bl=c)}}function Zt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function gu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function zi(e){if(Zt(e)!==e)throw Error(F(188))}function tf(e){var t=e.alternate;if(!t){if(t=Zt(e),t===null)throw Error(F(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var a=s.alternate;if(a===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===a.child){for(a=s.child;a;){if(a===n)return zi(s),e;if(a===r)return zi(s),t;a=a.sibling}throw Error(F(188))}if(n.return!==r.return)n=s,r=a;else{for(var i=!1,o=s.child;o;){if(o===n){i=!0,n=s,r=a;break}if(o===r){i=!0,r=s,n=a;break}o=o.sibling}if(!i){for(o=a.child;o;){if(o===n){i=!0,n=a,r=s;break}if(o===r){i=!0,r=a,n=s;break}o=o.sibling}if(!i)throw Error(F(189))}}if(n.alternate!==r)throw Error(F(190))}if(n.tag!==3)throw Error(F(188));return n.stateNode.current===n?e:t}function xu(e){return e=tf(e),e!==null?yu(e):null}function yu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=yu(e);if(t!==null)return t;e=e.sibling}return null}var ju=Le.unstable_scheduleCallback,Mi=Le.unstable_cancelCallback,nf=Le.unstable_shouldYield,rf=Le.unstable_requestPaint,ne=Le.unstable_now,sf=Le.unstable_getCurrentPriorityLevel,Ra=Le.unstable_ImmediatePriority,wu=Le.unstable_UserBlockingPriority,us=Le.unstable_NormalPriority,lf=Le.unstable_LowPriority,Nu=Le.unstable_IdlePriority,Ts=null,et=null;function af(e){if(et&&typeof et.onCommitFiberRoot=="function")try{et.onCommitFiberRoot(Ts,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:cf,of=Math.log,uf=Math.LN2;function cf(e){return e>>>=0,e===0?32:31-(of(e)/uf|0)|0}var Tr=64,Dr=4194304;function bn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function cs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,a=e.pingedLanes,i=n&268435455;if(i!==0){var o=i&~s;o!==0?r=bn(o):(a&=i,a!==0&&(r=bn(a)))}else i=n&~s,i!==0?r=bn(i):a!==0&&(r=bn(a));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,a=t&-t,s>=a||s===16&&(a&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),s=1<<n,r|=e[n],t&=~s;return r}function df(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ff(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-Ve(a),o=1<<i,u=s[i];u===-1?(!(o&n)||o&r)&&(s[i]=df(o,t)):u<=t&&(e.expiredLanes|=o),a&=~o}}function Al(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Su(){var e=Tr;return Tr<<=1,!(Tr&4194240)&&(Tr=64),e}function tl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function wr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function hf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Ve(n),a=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~a}}function Ta(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var W=0;function ku(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Cu,Da,Eu,_u,Pu,Hl=!1,zr=[],St=null,kt=null,Ct=null,lr=new Map,ar=new Map,yt=[],pf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function $i(e,t){switch(e){case"focusin":case"focusout":St=null;break;case"dragenter":case"dragleave":kt=null;break;case"mouseover":case"mouseout":Ct=null;break;case"pointerover":case"pointerout":lr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ar.delete(t.pointerId)}}function $n(e,t,n,r,s,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[s]},t!==null&&(t=Sr(t),t!==null&&Da(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function mf(e,t,n,r,s){switch(t){case"focusin":return St=$n(St,e,t,n,r,s),!0;case"dragenter":return kt=$n(kt,e,t,n,r,s),!0;case"mouseover":return Ct=$n(Ct,e,t,n,r,s),!0;case"pointerover":var a=s.pointerId;return lr.set(a,$n(lr.get(a)||null,e,t,n,r,s)),!0;case"gotpointercapture":return a=s.pointerId,ar.set(a,$n(ar.get(a)||null,e,t,n,r,s)),!0}return!1}function Lu(e){var t=Bt(e.target);if(t!==null){var n=Zt(t);if(n!==null){if(t=n.tag,t===13){if(t=gu(n),t!==null){e.blockedOn=t,Pu(e.priority,function(){Eu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Gr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Il=r,n.target.dispatchEvent(r),Il=null}else return t=Sr(n),t!==null&&Da(t),e.blockedOn=n,!1;t.shift()}return!0}function Ii(e,t,n){Gr(e)&&n.delete(t)}function vf(){Hl=!1,St!==null&&Gr(St)&&(St=null),kt!==null&&Gr(kt)&&(kt=null),Ct!==null&&Gr(Ct)&&(Ct=null),lr.forEach(Ii),ar.forEach(Ii)}function In(e,t){e.blockedOn===t&&(e.blockedOn=null,Hl||(Hl=!0,Le.unstable_scheduleCallback(Le.unstable_NormalPriority,vf)))}function ir(e){function t(s){return In(s,e)}if(0<zr.length){In(zr[0],e);for(var n=1;n<zr.length;n++){var r=zr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(St!==null&&In(St,e),kt!==null&&In(kt,e),Ct!==null&&In(Ct,e),lr.forEach(t),ar.forEach(t),n=0;n<yt.length;n++)r=yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<yt.length&&(n=yt[0],n.blockedOn===null);)Lu(n),n.blockedOn===null&&yt.shift()}var gn=mt.ReactCurrentBatchConfig,ds=!0;function gf(e,t,n,r){var s=W,a=gn.transition;gn.transition=null;try{W=1,za(e,t,n,r)}finally{W=s,gn.transition=a}}function xf(e,t,n,r){var s=W,a=gn.transition;gn.transition=null;try{W=4,za(e,t,n,r)}finally{W=s,gn.transition=a}}function za(e,t,n,r){if(ds){var s=Wl(e,t,n,r);if(s===null)dl(e,t,r,fs,n),$i(e,r);else if(mf(s,e,t,n,r))r.stopPropagation();else if($i(e,r),t&4&&-1<pf.indexOf(e)){for(;s!==null;){var a=Sr(s);if(a!==null&&Cu(a),a=Wl(e,t,n,r),a===null&&dl(e,t,r,fs,n),a===s)break;s=a}s!==null&&r.stopPropagation()}else dl(e,t,r,null,n)}}var fs=null;function Wl(e,t,n,r){if(fs=null,e=Fa(r),e=Bt(e),e!==null)if(t=Zt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=gu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return fs=e,null}function Fu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(sf()){case Ra:return 1;case wu:return 4;case us:case lf:return 16;case Nu:return 536870912;default:return 16}default:return 16}}var wt=null,Ma=null,Yr=null;function Ru(){if(Yr)return Yr;var e,t=Ma,n=t.length,r,s="value"in wt?wt.value:wt.textContent,a=s.length;for(e=0;e<n&&t[e]===s[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===s[a-r];r++);return Yr=s.slice(e,1<r?1-r:void 0)}function Xr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Mr(){return!0}function Oi(){return!1}function Re(e){function t(n,r,s,a,i){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=a,this.target=i,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(a):a[o]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?Mr:Oi,this.isPropagationStopped=Oi,this}return ee(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Mr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Mr)},persist:function(){},isPersistent:Mr}),t}var Pn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$a=Re(Pn),Nr=ee({},Pn,{view:0,detail:0}),yf=Re(Nr),nl,rl,On,Ds=ee({},Nr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ia,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==On&&(On&&e.type==="mousemove"?(nl=e.screenX-On.screenX,rl=e.screenY-On.screenY):rl=nl=0,On=e),nl)},movementY:function(e){return"movementY"in e?e.movementY:rl}}),Ui=Re(Ds),jf=ee({},Ds,{dataTransfer:0}),wf=Re(jf),Nf=ee({},Nr,{relatedTarget:0}),sl=Re(Nf),Sf=ee({},Pn,{animationName:0,elapsedTime:0,pseudoElement:0}),kf=Re(Sf),Cf=ee({},Pn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ef=Re(Cf),_f=ee({},Pn,{data:0}),Bi=Re(_f),Pf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Lf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ff={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Rf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ff[e])?!!t[e]:!1}function Ia(){return Rf}var Tf=ee({},Nr,{key:function(e){if(e.key){var t=Pf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Xr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Lf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ia,charCode:function(e){return e.type==="keypress"?Xr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Xr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Df=Re(Tf),zf=ee({},Ds,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ai=Re(zf),Mf=ee({},Nr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ia}),$f=Re(Mf),If=ee({},Pn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Of=Re(If),Uf=ee({},Ds,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Bf=Re(Uf),Af=[9,13,27,32],Oa=ct&&"CompositionEvent"in window,Yn=null;ct&&"documentMode"in document&&(Yn=document.documentMode);var Hf=ct&&"TextEvent"in window&&!Yn,Tu=ct&&(!Oa||Yn&&8<Yn&&11>=Yn),Hi=" ",Wi=!1;function Du(e,t){switch(e){case"keyup":return Af.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var rn=!1;function Wf(e,t){switch(e){case"compositionend":return zu(t);case"keypress":return t.which!==32?null:(Wi=!0,Hi);case"textInput":return e=t.data,e===Hi&&Wi?null:e;default:return null}}function Vf(e,t){if(rn)return e==="compositionend"||!Oa&&Du(e,t)?(e=Ru(),Yr=Ma=wt=null,rn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Tu&&t.locale!=="ko"?null:t.data;default:return null}}var bf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!bf[e.type]:t==="textarea"}function Mu(e,t,n,r){fu(r),t=hs(t,"onChange"),0<t.length&&(n=new $a("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Xn=null,or=null;function Qf(e){Qu(e,0)}function zs(e){var t=an(e);if(lu(t))return e}function Kf(e,t){if(e==="change")return t}var $u=!1;if(ct){var ll;if(ct){var al="oninput"in document;if(!al){var bi=document.createElement("div");bi.setAttribute("oninput","return;"),al=typeof bi.oninput=="function"}ll=al}else ll=!1;$u=ll&&(!document.documentMode||9<document.documentMode)}function Qi(){Xn&&(Xn.detachEvent("onpropertychange",Iu),or=Xn=null)}function Iu(e){if(e.propertyName==="value"&&zs(or)){var t=[];Mu(t,or,e,Fa(e)),vu(Qf,t)}}function Gf(e,t,n){e==="focusin"?(Qi(),Xn=t,or=n,Xn.attachEvent("onpropertychange",Iu)):e==="focusout"&&Qi()}function Yf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return zs(or)}function Xf(e,t){if(e==="click")return zs(t)}function Jf(e,t){if(e==="input"||e==="change")return zs(t)}function Zf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qe=typeof Object.is=="function"?Object.is:Zf;function ur(e,t){if(Qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Cl.call(t,s)||!Qe(e[s],t[s]))return!1}return!0}function Ki(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Gi(e,t){var n=Ki(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ki(n)}}function Ou(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ou(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Uu(){for(var e=window,t=as();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=as(e.document)}return t}function Ua(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function qf(e){var t=Uu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ou(n.ownerDocument.documentElement,n)){if(r!==null&&Ua(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,a=Math.min(r.start,s);r=r.end===void 0?a:Math.min(r.end,s),!e.extend&&a>r&&(s=r,r=a,a=s),s=Gi(n,a);var i=Gi(n,r);s&&i&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var eh=ct&&"documentMode"in document&&11>=document.documentMode,sn=null,Vl=null,Jn=null,bl=!1;function Yi(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;bl||sn==null||sn!==as(r)||(r=sn,"selectionStart"in r&&Ua(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Jn&&ur(Jn,r)||(Jn=r,r=hs(Vl,"onSelect"),0<r.length&&(t=new $a("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=sn)))}function $r(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ln={animationend:$r("Animation","AnimationEnd"),animationiteration:$r("Animation","AnimationIteration"),animationstart:$r("Animation","AnimationStart"),transitionend:$r("Transition","TransitionEnd")},il={},Bu={};ct&&(Bu=document.createElement("div").style,"AnimationEvent"in window||(delete ln.animationend.animation,delete ln.animationiteration.animation,delete ln.animationstart.animation),"TransitionEvent"in window||delete ln.transitionend.transition);function Ms(e){if(il[e])return il[e];if(!ln[e])return e;var t=ln[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Bu)return il[e]=t[n];return e}var Au=Ms("animationend"),Hu=Ms("animationiteration"),Wu=Ms("animationstart"),Vu=Ms("transitionend"),bu=new Map,Xi="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Dt(e,t){bu.set(e,t),Jt(t,[e])}for(var ol=0;ol<Xi.length;ol++){var ul=Xi[ol],th=ul.toLowerCase(),nh=ul[0].toUpperCase()+ul.slice(1);Dt(th,"on"+nh)}Dt(Au,"onAnimationEnd");Dt(Hu,"onAnimationIteration");Dt(Wu,"onAnimationStart");Dt("dblclick","onDoubleClick");Dt("focusin","onFocus");Dt("focusout","onBlur");Dt(Vu,"onTransitionEnd");jn("onMouseEnter",["mouseout","mouseover"]);jn("onMouseLeave",["mouseout","mouseover"]);jn("onPointerEnter",["pointerout","pointerover"]);jn("onPointerLeave",["pointerout","pointerover"]);Jt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Jt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Jt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Jt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Jt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Jt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Qn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rh=new Set("cancel close invalid load scroll toggle".split(" ").concat(Qn));function Ji(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ef(r,t,void 0,e),e.currentTarget=null}function Qu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],u=o.instance,c=o.currentTarget;if(o=o.listener,u!==a&&s.isPropagationStopped())break e;Ji(s,o,c),a=u}else for(i=0;i<r.length;i++){if(o=r[i],u=o.instance,c=o.currentTarget,o=o.listener,u!==a&&s.isPropagationStopped())break e;Ji(s,o,c),a=u}}}if(os)throw e=Bl,os=!1,Bl=null,e}function Q(e,t){var n=t[Xl];n===void 0&&(n=t[Xl]=new Set);var r=e+"__bubble";n.has(r)||(Ku(t,e,2,!1),n.add(r))}function cl(e,t,n){var r=0;t&&(r|=4),Ku(n,e,r,t)}var Ir="_reactListening"+Math.random().toString(36).slice(2);function cr(e){if(!e[Ir]){e[Ir]=!0,eu.forEach(function(n){n!=="selectionchange"&&(rh.has(n)||cl(n,!1,e),cl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ir]||(t[Ir]=!0,cl("selectionchange",!1,t))}}function Ku(e,t,n,r){switch(Fu(t)){case 1:var s=gf;break;case 4:s=xf;break;default:s=za}n=s.bind(null,t,n,e),s=void 0,!Ul||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function dl(e,t,n,r,s){var a=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var o=r.stateNode.containerInfo;if(o===s||o.nodeType===8&&o.parentNode===s)break;if(i===4)for(i=r.return;i!==null;){var u=i.tag;if((u===3||u===4)&&(u=i.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;i=i.return}for(;o!==null;){if(i=Bt(o),i===null)return;if(u=i.tag,u===5||u===6){r=a=i;continue e}o=o.parentNode}}r=r.return}vu(function(){var c=a,f=Fa(n),v=[];e:{var h=bu.get(e);if(h!==void 0){var S=$a,N=e;switch(e){case"keypress":if(Xr(n)===0)break e;case"keydown":case"keyup":S=Df;break;case"focusin":N="focus",S=sl;break;case"focusout":N="blur",S=sl;break;case"beforeblur":case"afterblur":S=sl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=Ui;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=wf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=$f;break;case Au:case Hu:case Wu:S=kf;break;case Vu:S=Of;break;case"scroll":S=yf;break;case"wheel":S=Bf;break;case"copy":case"cut":case"paste":S=Ef;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=Ai}var _=(t&4)!==0,L=!_&&e==="scroll",p=_?h!==null?h+"Capture":null:h;_=[];for(var d=c,m;d!==null;){m=d;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,p!==null&&(w=sr(d,p),w!=null&&_.push(dr(d,w,m)))),L)break;d=d.return}0<_.length&&(h=new S(h,N,null,n,f),v.push({event:h,listeners:_}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",h&&n!==Il&&(N=n.relatedTarget||n.fromElement)&&(Bt(N)||N[dt]))break e;if((S||h)&&(h=f.window===f?f:(h=f.ownerDocument)?h.defaultView||h.parentWindow:window,S?(N=n.relatedTarget||n.toElement,S=c,N=N?Bt(N):null,N!==null&&(L=Zt(N),N!==L||N.tag!==5&&N.tag!==6)&&(N=null)):(S=null,N=c),S!==N)){if(_=Ui,w="onMouseLeave",p="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(_=Ai,w="onPointerLeave",p="onPointerEnter",d="pointer"),L=S==null?h:an(S),m=N==null?h:an(N),h=new _(w,d+"leave",S,n,f),h.target=L,h.relatedTarget=m,w=null,Bt(f)===c&&(_=new _(p,d+"enter",N,n,f),_.target=m,_.relatedTarget=L,w=_),L=w,S&&N)t:{for(_=S,p=N,d=0,m=_;m;m=en(m))d++;for(m=0,w=p;w;w=en(w))m++;for(;0<d-m;)_=en(_),d--;for(;0<m-d;)p=en(p),m--;for(;d--;){if(_===p||p!==null&&_===p.alternate)break t;_=en(_),p=en(p)}_=null}else _=null;S!==null&&Zi(v,h,S,_,!1),N!==null&&L!==null&&Zi(v,L,N,_,!0)}}e:{if(h=c?an(c):window,S=h.nodeName&&h.nodeName.toLowerCase(),S==="select"||S==="input"&&h.type==="file")var x=Kf;else if(Vi(h))if($u)x=Jf;else{x=Yf;var E=Gf}else(S=h.nodeName)&&S.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(x=Xf);if(x&&(x=x(e,c))){Mu(v,x,n,f);break e}E&&E(e,h,c),e==="focusout"&&(E=h._wrapperState)&&E.controlled&&h.type==="number"&&Tl(h,"number",h.value)}switch(E=c?an(c):window,e){case"focusin":(Vi(E)||E.contentEditable==="true")&&(sn=E,Vl=c,Jn=null);break;case"focusout":Jn=Vl=sn=null;break;case"mousedown":bl=!0;break;case"contextmenu":case"mouseup":case"dragend":bl=!1,Yi(v,n,f);break;case"selectionchange":if(eh)break;case"keydown":case"keyup":Yi(v,n,f)}var P;if(Oa)e:{switch(e){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else rn?Du(e,n)&&(j="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Tu&&n.locale!=="ko"&&(rn||j!=="onCompositionStart"?j==="onCompositionEnd"&&rn&&(P=Ru()):(wt=f,Ma="value"in wt?wt.value:wt.textContent,rn=!0)),E=hs(c,j),0<E.length&&(j=new Bi(j,e,null,n,f),v.push({event:j,listeners:E}),P?j.data=P:(P=zu(n),P!==null&&(j.data=P)))),(P=Hf?Wf(e,n):Vf(e,n))&&(c=hs(c,"onBeforeInput"),0<c.length&&(f=new Bi("onBeforeInput","beforeinput",null,n,f),v.push({event:f,listeners:c}),f.data=P))}Qu(v,t)})}function dr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function hs(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,a=s.stateNode;s.tag===5&&a!==null&&(s=a,a=sr(e,n),a!=null&&r.unshift(dr(e,a,s)),a=sr(e,t),a!=null&&r.push(dr(e,a,s))),e=e.return}return r}function en(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Zi(e,t,n,r,s){for(var a=t._reactName,i=[];n!==null&&n!==r;){var o=n,u=o.alternate,c=o.stateNode;if(u!==null&&u===r)break;o.tag===5&&c!==null&&(o=c,s?(u=sr(n,a),u!=null&&i.unshift(dr(n,u,o))):s||(u=sr(n,a),u!=null&&i.push(dr(n,u,o)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var sh=/\r\n?/g,lh=/\u0000|\uFFFD/g;function qi(e){return(typeof e=="string"?e:""+e).replace(sh,`
`).replace(lh,"")}function Or(e,t,n){if(t=qi(t),qi(e)!==t&&n)throw Error(F(425))}function ps(){}var Ql=null,Kl=null;function Gl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yl=typeof setTimeout=="function"?setTimeout:void 0,ah=typeof clearTimeout=="function"?clearTimeout:void 0,eo=typeof Promise=="function"?Promise:void 0,ih=typeof queueMicrotask=="function"?queueMicrotask:typeof eo<"u"?function(e){return eo.resolve(null).then(e).catch(oh)}:Yl;function oh(e){setTimeout(function(){throw e})}function fl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),ir(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);ir(t)}function Et(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function to(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Ln=Math.random().toString(36).slice(2),qe="__reactFiber$"+Ln,fr="__reactProps$"+Ln,dt="__reactContainer$"+Ln,Xl="__reactEvents$"+Ln,uh="__reactListeners$"+Ln,ch="__reactHandles$"+Ln;function Bt(e){var t=e[qe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[dt]||n[qe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=to(e);e!==null;){if(n=e[qe])return n;e=to(e)}return t}e=n,n=e.parentNode}return null}function Sr(e){return e=e[qe]||e[dt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function an(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(F(33))}function $s(e){return e[fr]||null}var Jl=[],on=-1;function zt(e){return{current:e}}function K(e){0>on||(e.current=Jl[on],Jl[on]=null,on--)}function b(e,t){on++,Jl[on]=e.current,e.current=t}var Tt={},me=zt(Tt),Ne=zt(!1),bt=Tt;function wn(e,t){var n=e.type.contextTypes;if(!n)return Tt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},a;for(a in n)s[a]=t[a];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Se(e){return e=e.childContextTypes,e!=null}function ms(){K(Ne),K(me)}function no(e,t,n){if(me.current!==Tt)throw Error(F(168));b(me,t),b(Ne,n)}function Gu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(F(108,Kd(e)||"Unknown",s));return ee({},n,r)}function vs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Tt,bt=me.current,b(me,e),b(Ne,Ne.current),!0}function ro(e,t,n){var r=e.stateNode;if(!r)throw Error(F(169));n?(e=Gu(e,t,bt),r.__reactInternalMemoizedMergedChildContext=e,K(Ne),K(me),b(me,e)):K(Ne),b(Ne,n)}var lt=null,Is=!1,hl=!1;function Yu(e){lt===null?lt=[e]:lt.push(e)}function dh(e){Is=!0,Yu(e)}function Mt(){if(!hl&&lt!==null){hl=!0;var e=0,t=W;try{var n=lt;for(W=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Is=!1}catch(s){throw lt!==null&&(lt=lt.slice(e+1)),ju(Ra,Mt),s}finally{W=t,hl=!1}}return null}var un=[],cn=0,gs=null,xs=0,Te=[],De=0,Qt=null,at=1,it="";function It(e,t){un[cn++]=xs,un[cn++]=gs,gs=e,xs=t}function Xu(e,t,n){Te[De++]=at,Te[De++]=it,Te[De++]=Qt,Qt=e;var r=at;e=it;var s=32-Ve(r)-1;r&=~(1<<s),n+=1;var a=32-Ve(t)+s;if(30<a){var i=s-s%5;a=(r&(1<<i)-1).toString(32),r>>=i,s-=i,at=1<<32-Ve(t)+s|n<<s|r,it=a+e}else at=1<<a|n<<s|r,it=e}function Ba(e){e.return!==null&&(It(e,1),Xu(e,1,0))}function Aa(e){for(;e===gs;)gs=un[--cn],un[cn]=null,xs=un[--cn],un[cn]=null;for(;e===Qt;)Qt=Te[--De],Te[De]=null,it=Te[--De],Te[De]=null,at=Te[--De],Te[De]=null}var Pe=null,_e=null,Y=!1,We=null;function Ju(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function so(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Pe=e,_e=Et(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Pe=e,_e=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Qt!==null?{id:at,overflow:it}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Pe=e,_e=null,!0):!1;default:return!1}}function Zl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ql(e){if(Y){var t=_e;if(t){var n=t;if(!so(e,t)){if(Zl(e))throw Error(F(418));t=Et(n.nextSibling);var r=Pe;t&&so(e,t)?Ju(r,n):(e.flags=e.flags&-4097|2,Y=!1,Pe=e)}}else{if(Zl(e))throw Error(F(418));e.flags=e.flags&-4097|2,Y=!1,Pe=e}}}function lo(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Pe=e}function Ur(e){if(e!==Pe)return!1;if(!Y)return lo(e),Y=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Gl(e.type,e.memoizedProps)),t&&(t=_e)){if(Zl(e))throw Zu(),Error(F(418));for(;t;)Ju(e,t),t=Et(t.nextSibling)}if(lo(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(F(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){_e=Et(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}_e=null}}else _e=Pe?Et(e.stateNode.nextSibling):null;return!0}function Zu(){for(var e=_e;e;)e=Et(e.nextSibling)}function Nn(){_e=Pe=null,Y=!1}function Ha(e){We===null?We=[e]:We.push(e)}var fh=mt.ReactCurrentBatchConfig;function Un(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(F(309));var r=n.stateNode}if(!r)throw Error(F(147,e));var s=r,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(i){var o=s.refs;i===null?delete o[a]:o[a]=i},t._stringRef=a,t)}if(typeof e!="string")throw Error(F(284));if(!n._owner)throw Error(F(290,e))}return e}function Br(e,t){throw e=Object.prototype.toString.call(t),Error(F(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ao(e){var t=e._init;return t(e._payload)}function qu(e){function t(p,d){if(e){var m=p.deletions;m===null?(p.deletions=[d],p.flags|=16):m.push(d)}}function n(p,d){if(!e)return null;for(;d!==null;)t(p,d),d=d.sibling;return null}function r(p,d){for(p=new Map;d!==null;)d.key!==null?p.set(d.key,d):p.set(d.index,d),d=d.sibling;return p}function s(p,d){return p=Ft(p,d),p.index=0,p.sibling=null,p}function a(p,d,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<d?(p.flags|=2,d):m):(p.flags|=2,d)):(p.flags|=1048576,d)}function i(p){return e&&p.alternate===null&&(p.flags|=2),p}function o(p,d,m,w){return d===null||d.tag!==6?(d=jl(m,p.mode,w),d.return=p,d):(d=s(d,m),d.return=p,d)}function u(p,d,m,w){var x=m.type;return x===nn?f(p,d,m.props.children,w,m.key):d!==null&&(d.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===gt&&ao(x)===d.type)?(w=s(d,m.props),w.ref=Un(p,d,m),w.return=p,w):(w=rs(m.type,m.key,m.props,null,p.mode,w),w.ref=Un(p,d,m),w.return=p,w)}function c(p,d,m,w){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=wl(m,p.mode,w),d.return=p,d):(d=s(d,m.children||[]),d.return=p,d)}function f(p,d,m,w,x){return d===null||d.tag!==7?(d=Vt(m,p.mode,w,x),d.return=p,d):(d=s(d,m),d.return=p,d)}function v(p,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=jl(""+d,p.mode,m),d.return=p,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Lr:return m=rs(d.type,d.key,d.props,null,p.mode,m),m.ref=Un(p,null,d),m.return=p,m;case tn:return d=wl(d,p.mode,m),d.return=p,d;case gt:var w=d._init;return v(p,w(d._payload),m)}if(Vn(d)||zn(d))return d=Vt(d,p.mode,m,null),d.return=p,d;Br(p,d)}return null}function h(p,d,m,w){var x=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return x!==null?null:o(p,d,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Lr:return m.key===x?u(p,d,m,w):null;case tn:return m.key===x?c(p,d,m,w):null;case gt:return x=m._init,h(p,d,x(m._payload),w)}if(Vn(m)||zn(m))return x!==null?null:f(p,d,m,w,null);Br(p,m)}return null}function S(p,d,m,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(m)||null,o(d,p,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Lr:return p=p.get(w.key===null?m:w.key)||null,u(d,p,w,x);case tn:return p=p.get(w.key===null?m:w.key)||null,c(d,p,w,x);case gt:var E=w._init;return S(p,d,m,E(w._payload),x)}if(Vn(w)||zn(w))return p=p.get(m)||null,f(d,p,w,x,null);Br(d,w)}return null}function N(p,d,m,w){for(var x=null,E=null,P=d,j=d=0,C=null;P!==null&&j<m.length;j++){P.index>j?(C=P,P=null):C=P.sibling;var y=h(p,P,m[j],w);if(y===null){P===null&&(P=C);break}e&&P&&y.alternate===null&&t(p,P),d=a(y,d,j),E===null?x=y:E.sibling=y,E=y,P=C}if(j===m.length)return n(p,P),Y&&It(p,j),x;if(P===null){for(;j<m.length;j++)P=v(p,m[j],w),P!==null&&(d=a(P,d,j),E===null?x=P:E.sibling=P,E=P);return Y&&It(p,j),x}for(P=r(p,P);j<m.length;j++)C=S(P,p,j,m[j],w),C!==null&&(e&&C.alternate!==null&&P.delete(C.key===null?j:C.key),d=a(C,d,j),E===null?x=C:E.sibling=C,E=C);return e&&P.forEach(function(k){return t(p,k)}),Y&&It(p,j),x}function _(p,d,m,w){var x=zn(m);if(typeof x!="function")throw Error(F(150));if(m=x.call(m),m==null)throw Error(F(151));for(var E=x=null,P=d,j=d=0,C=null,y=m.next();P!==null&&!y.done;j++,y=m.next()){P.index>j?(C=P,P=null):C=P.sibling;var k=h(p,P,y.value,w);if(k===null){P===null&&(P=C);break}e&&P&&k.alternate===null&&t(p,P),d=a(k,d,j),E===null?x=k:E.sibling=k,E=k,P=C}if(y.done)return n(p,P),Y&&It(p,j),x;if(P===null){for(;!y.done;j++,y=m.next())y=v(p,y.value,w),y!==null&&(d=a(y,d,j),E===null?x=y:E.sibling=y,E=y);return Y&&It(p,j),x}for(P=r(p,P);!y.done;j++,y=m.next())y=S(P,p,j,y.value,w),y!==null&&(e&&y.alternate!==null&&P.delete(y.key===null?j:y.key),d=a(y,d,j),E===null?x=y:E.sibling=y,E=y);return e&&P.forEach(function(D){return t(p,D)}),Y&&It(p,j),x}function L(p,d,m,w){if(typeof m=="object"&&m!==null&&m.type===nn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Lr:e:{for(var x=m.key,E=d;E!==null;){if(E.key===x){if(x=m.type,x===nn){if(E.tag===7){n(p,E.sibling),d=s(E,m.props.children),d.return=p,p=d;break e}}else if(E.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===gt&&ao(x)===E.type){n(p,E.sibling),d=s(E,m.props),d.ref=Un(p,E,m),d.return=p,p=d;break e}n(p,E);break}else t(p,E);E=E.sibling}m.type===nn?(d=Vt(m.props.children,p.mode,w,m.key),d.return=p,p=d):(w=rs(m.type,m.key,m.props,null,p.mode,w),w.ref=Un(p,d,m),w.return=p,p=w)}return i(p);case tn:e:{for(E=m.key;d!==null;){if(d.key===E)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(p,d.sibling),d=s(d,m.children||[]),d.return=p,p=d;break e}else{n(p,d);break}else t(p,d);d=d.sibling}d=wl(m,p.mode,w),d.return=p,p=d}return i(p);case gt:return E=m._init,L(p,d,E(m._payload),w)}if(Vn(m))return N(p,d,m,w);if(zn(m))return _(p,d,m,w);Br(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(p,d.sibling),d=s(d,m),d.return=p,p=d):(n(p,d),d=jl(m,p.mode,w),d.return=p,p=d),i(p)):n(p,d)}return L}var Sn=qu(!0),ec=qu(!1),ys=zt(null),js=null,dn=null,Wa=null;function Va(){Wa=dn=js=null}function ba(e){var t=ys.current;K(ys),e._currentValue=t}function ea(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xn(e,t){js=e,Wa=dn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function Ie(e){var t=e._currentValue;if(Wa!==e)if(e={context:e,memoizedValue:t,next:null},dn===null){if(js===null)throw Error(F(308));dn=e,js.dependencies={lanes:0,firstContext:e}}else dn=dn.next=e;return t}var At=null;function Qa(e){At===null?At=[e]:At.push(e)}function tc(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Qa(t)):(n.next=s.next,s.next=n),t.interleaved=n,ft(e,r)}function ft(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function Ka(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function nc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ot(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function _t(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,A&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,ft(e,n)}return s=r.interleaved,s===null?(t.next=t,Qa(r)):(t.next=s.next,s.next=t),r.interleaved=t,ft(e,n)}function Jr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ta(e,n)}}function io(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};a===null?s=a=i:a=a.next=i,n=n.next}while(n!==null);a===null?s=a=t:a=a.next=t}else s=a=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:a,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ws(e,t,n,r){var s=e.updateQueue;xt=!1;var a=s.firstBaseUpdate,i=s.lastBaseUpdate,o=s.shared.pending;if(o!==null){s.shared.pending=null;var u=o,c=u.next;u.next=null,i===null?a=c:i.next=c,i=u;var f=e.alternate;f!==null&&(f=f.updateQueue,o=f.lastBaseUpdate,o!==i&&(o===null?f.firstBaseUpdate=c:o.next=c,f.lastBaseUpdate=u))}if(a!==null){var v=s.baseState;i=0,f=c=u=null,o=a;do{var h=o.lane,S=o.eventTime;if((r&h)===h){f!==null&&(f=f.next={eventTime:S,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var N=e,_=o;switch(h=t,S=n,_.tag){case 1:if(N=_.payload,typeof N=="function"){v=N.call(S,v,h);break e}v=N;break e;case 3:N.flags=N.flags&-65537|128;case 0:if(N=_.payload,h=typeof N=="function"?N.call(S,v,h):N,h==null)break e;v=ee({},v,h);break e;case 2:xt=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,h=s.effects,h===null?s.effects=[o]:h.push(o))}else S={eventTime:S,lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},f===null?(c=f=S,u=v):f=f.next=S,i|=h;if(o=o.next,o===null){if(o=s.shared.pending,o===null)break;h=o,o=h.next,h.next=null,s.lastBaseUpdate=h,s.shared.pending=null}}while(!0);if(f===null&&(u=v),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=f,t=s.shared.interleaved,t!==null){s=t;do i|=s.lane,s=s.next;while(s!==t)}else a===null&&(s.shared.lanes=0);Gt|=i,e.lanes=i,e.memoizedState=v}}function oo(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(F(191,s));s.call(r)}}}var kr={},tt=zt(kr),hr=zt(kr),pr=zt(kr);function Ht(e){if(e===kr)throw Error(F(174));return e}function Ga(e,t){switch(b(pr,t),b(hr,e),b(tt,kr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:zl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=zl(t,e)}K(tt),b(tt,t)}function kn(){K(tt),K(hr),K(pr)}function rc(e){Ht(pr.current);var t=Ht(tt.current),n=zl(t,e.type);t!==n&&(b(hr,e),b(tt,n))}function Ya(e){hr.current===e&&(K(tt),K(hr))}var Z=zt(0);function Ns(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var pl=[];function Xa(){for(var e=0;e<pl.length;e++)pl[e]._workInProgressVersionPrimary=null;pl.length=0}var Zr=mt.ReactCurrentDispatcher,ml=mt.ReactCurrentBatchConfig,Kt=0,q=null,se=null,ae=null,Ss=!1,Zn=!1,mr=0,hh=0;function fe(){throw Error(F(321))}function Ja(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qe(e[n],t[n]))return!1;return!0}function Za(e,t,n,r,s,a){if(Kt=a,q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Zr.current=e===null||e.memoizedState===null?gh:xh,e=n(r,s),Zn){a=0;do{if(Zn=!1,mr=0,25<=a)throw Error(F(301));a+=1,ae=se=null,t.updateQueue=null,Zr.current=yh,e=n(r,s)}while(Zn)}if(Zr.current=ks,t=se!==null&&se.next!==null,Kt=0,ae=se=q=null,Ss=!1,t)throw Error(F(300));return e}function qa(){var e=mr!==0;return mr=0,e}function Ze(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?q.memoizedState=ae=e:ae=ae.next=e,ae}function Oe(){if(se===null){var e=q.alternate;e=e!==null?e.memoizedState:null}else e=se.next;var t=ae===null?q.memoizedState:ae.next;if(t!==null)ae=t,se=e;else{if(e===null)throw Error(F(310));se=e,e={memoizedState:se.memoizedState,baseState:se.baseState,baseQueue:se.baseQueue,queue:se.queue,next:null},ae===null?q.memoizedState=ae=e:ae=ae.next=e}return ae}function vr(e,t){return typeof t=="function"?t(e):t}function vl(e){var t=Oe(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=se,s=r.baseQueue,a=n.pending;if(a!==null){if(s!==null){var i=s.next;s.next=a.next,a.next=i}r.baseQueue=s=a,n.pending=null}if(s!==null){a=s.next,r=r.baseState;var o=i=null,u=null,c=a;do{var f=c.lane;if((Kt&f)===f)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var v={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(o=u=v,i=r):u=u.next=v,q.lanes|=f,Gt|=f}c=c.next}while(c!==null&&c!==a);u===null?i=r:u.next=o,Qe(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do a=s.lane,q.lanes|=a,Gt|=a,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function gl(e){var t=Oe(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,a=t.memoizedState;if(s!==null){n.pending=null;var i=s=s.next;do a=e(a,i.action),i=i.next;while(i!==s);Qe(a,t.memoizedState)||(we=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function sc(){}function lc(e,t){var n=q,r=Oe(),s=t(),a=!Qe(r.memoizedState,s);if(a&&(r.memoizedState=s,we=!0),r=r.queue,ei(oc.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||ae!==null&&ae.memoizedState.tag&1){if(n.flags|=2048,gr(9,ic.bind(null,n,r,s,t),void 0,null),ie===null)throw Error(F(349));Kt&30||ac(n,t,s)}return s}function ac(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=q.updateQueue,t===null?(t={lastEffect:null,stores:null},q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ic(e,t,n,r){t.value=n,t.getSnapshot=r,uc(t)&&cc(e)}function oc(e,t,n){return n(function(){uc(t)&&cc(e)})}function uc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qe(e,n)}catch{return!0}}function cc(e){var t=ft(e,1);t!==null&&be(t,e,1,-1)}function uo(e){var t=Ze();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vr,lastRenderedState:e},t.queue=e,e=e.dispatch=vh.bind(null,q,e),[t.memoizedState,e]}function gr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=q.updateQueue,t===null?(t={lastEffect:null,stores:null},q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function dc(){return Oe().memoizedState}function qr(e,t,n,r){var s=Ze();q.flags|=e,s.memoizedState=gr(1|t,n,void 0,r===void 0?null:r)}function Os(e,t,n,r){var s=Oe();r=r===void 0?null:r;var a=void 0;if(se!==null){var i=se.memoizedState;if(a=i.destroy,r!==null&&Ja(r,i.deps)){s.memoizedState=gr(t,n,a,r);return}}q.flags|=e,s.memoizedState=gr(1|t,n,a,r)}function co(e,t){return qr(8390656,8,e,t)}function ei(e,t){return Os(2048,8,e,t)}function fc(e,t){return Os(4,2,e,t)}function hc(e,t){return Os(4,4,e,t)}function pc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mc(e,t,n){return n=n!=null?n.concat([e]):null,Os(4,4,pc.bind(null,t,e),n)}function ti(){}function vc(e,t){var n=Oe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ja(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gc(e,t){var n=Oe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ja(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function xc(e,t,n){return Kt&21?(Qe(n,t)||(n=Su(),q.lanes|=n,Gt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function ph(e,t){var n=W;W=n!==0&&4>n?n:4,e(!0);var r=ml.transition;ml.transition={};try{e(!1),t()}finally{W=n,ml.transition=r}}function yc(){return Oe().memoizedState}function mh(e,t,n){var r=Lt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},jc(e))wc(t,n);else if(n=tc(e,t,n,r),n!==null){var s=ge();be(n,e,r,s),Nc(n,t,r)}}function vh(e,t,n){var r=Lt(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(jc(e))wc(t,s);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var i=t.lastRenderedState,o=a(i,n);if(s.hasEagerState=!0,s.eagerState=o,Qe(o,i)){var u=t.interleaved;u===null?(s.next=s,Qa(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=tc(e,t,s,r),n!==null&&(s=ge(),be(n,e,r,s),Nc(n,t,r))}}function jc(e){var t=e.alternate;return e===q||t!==null&&t===q}function wc(e,t){Zn=Ss=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Nc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ta(e,n)}}var ks={readContext:Ie,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},gh={readContext:Ie,useCallback:function(e,t){return Ze().memoizedState=[e,t===void 0?null:t],e},useContext:Ie,useEffect:co,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,qr(4194308,4,pc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qr(4194308,4,e,t)},useInsertionEffect:function(e,t){return qr(4,2,e,t)},useMemo:function(e,t){var n=Ze();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ze();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=mh.bind(null,q,e),[r.memoizedState,e]},useRef:function(e){var t=Ze();return e={current:e},t.memoizedState=e},useState:uo,useDebugValue:ti,useDeferredValue:function(e){return Ze().memoizedState=e},useTransition:function(){var e=uo(!1),t=e[0];return e=ph.bind(null,e[1]),Ze().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=q,s=Ze();if(Y){if(n===void 0)throw Error(F(407));n=n()}else{if(n=t(),ie===null)throw Error(F(349));Kt&30||ac(r,t,n)}s.memoizedState=n;var a={value:n,getSnapshot:t};return s.queue=a,co(oc.bind(null,r,a,e),[e]),r.flags|=2048,gr(9,ic.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=Ze(),t=ie.identifierPrefix;if(Y){var n=it,r=at;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=mr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=hh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},xh={readContext:Ie,useCallback:vc,useContext:Ie,useEffect:ei,useImperativeHandle:mc,useInsertionEffect:fc,useLayoutEffect:hc,useMemo:gc,useReducer:vl,useRef:dc,useState:function(){return vl(vr)},useDebugValue:ti,useDeferredValue:function(e){var t=Oe();return xc(t,se.memoizedState,e)},useTransition:function(){var e=vl(vr)[0],t=Oe().memoizedState;return[e,t]},useMutableSource:sc,useSyncExternalStore:lc,useId:yc,unstable_isNewReconciler:!1},yh={readContext:Ie,useCallback:vc,useContext:Ie,useEffect:ei,useImperativeHandle:mc,useInsertionEffect:fc,useLayoutEffect:hc,useMemo:gc,useReducer:gl,useRef:dc,useState:function(){return gl(vr)},useDebugValue:ti,useDeferredValue:function(e){var t=Oe();return se===null?t.memoizedState=e:xc(t,se.memoizedState,e)},useTransition:function(){var e=gl(vr)[0],t=Oe().memoizedState;return[e,t]},useMutableSource:sc,useSyncExternalStore:lc,useId:yc,unstable_isNewReconciler:!1};function Ae(e,t){if(e&&e.defaultProps){t=ee({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ta(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ee({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Us={isMounted:function(e){return(e=e._reactInternals)?Zt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ge(),s=Lt(e),a=ot(r,s);a.payload=t,n!=null&&(a.callback=n),t=_t(e,a,s),t!==null&&(be(t,e,s,r),Jr(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ge(),s=Lt(e),a=ot(r,s);a.tag=1,a.payload=t,n!=null&&(a.callback=n),t=_t(e,a,s),t!==null&&(be(t,e,s,r),Jr(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ge(),r=Lt(e),s=ot(n,r);s.tag=2,t!=null&&(s.callback=t),t=_t(e,s,r),t!==null&&(be(t,e,r,n),Jr(t,e,r))}};function fo(e,t,n,r,s,a,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,a,i):t.prototype&&t.prototype.isPureReactComponent?!ur(n,r)||!ur(s,a):!0}function Sc(e,t,n){var r=!1,s=Tt,a=t.contextType;return typeof a=="object"&&a!==null?a=Ie(a):(s=Se(t)?bt:me.current,r=t.contextTypes,a=(r=r!=null)?wn(e,s):Tt),t=new t(n,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Us,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=a),t}function ho(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Us.enqueueReplaceState(t,t.state,null)}function na(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Ka(e);var a=t.contextType;typeof a=="object"&&a!==null?s.context=Ie(a):(a=Se(t)?bt:me.current,s.context=wn(e,a)),s.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(ta(e,t,a,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Us.enqueueReplaceState(s,s.state,null),ws(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Cn(e,t){try{var n="",r=t;do n+=Qd(r),r=r.return;while(r);var s=n}catch(a){s=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:s,digest:null}}function xl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ra(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var jh=typeof WeakMap=="function"?WeakMap:Map;function kc(e,t,n){n=ot(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Es||(Es=!0,ha=r),ra(e,t)},n}function Cc(e,t,n){n=ot(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){ra(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(n.callback=function(){ra(e,t),typeof r!="function"&&(Pt===null?Pt=new Set([this]):Pt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function po(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new jh;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=zh.bind(null,e,t,n),t.then(e,e))}function mo(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vo(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ot(-1,1),t.tag=2,_t(n,t,1))),n.lanes|=1),e)}var wh=mt.ReactCurrentOwner,we=!1;function ve(e,t,n,r){t.child=e===null?ec(t,null,n,r):Sn(t,e.child,n,r)}function go(e,t,n,r,s){n=n.render;var a=t.ref;return xn(t,s),r=Za(e,t,n,r,a,s),n=qa(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,ht(e,t,s)):(Y&&n&&Ba(t),t.flags|=1,ve(e,t,r,s),t.child)}function xo(e,t,n,r,s){if(e===null){var a=n.type;return typeof a=="function"&&!ui(a)&&a.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=a,Ec(e,t,a,r,s)):(e=rs(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&s)){var i=a.memoizedProps;if(n=n.compare,n=n!==null?n:ur,n(i,r)&&e.ref===t.ref)return ht(e,t,s)}return t.flags|=1,e=Ft(a,r),e.ref=t.ref,e.return=t,t.child=e}function Ec(e,t,n,r,s){if(e!==null){var a=e.memoizedProps;if(ur(a,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=a,(e.lanes&s)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,ht(e,t,s)}return sa(e,t,n,r,s)}function _c(e,t,n){var r=t.pendingProps,s=r.children,a=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},b(hn,Ce),Ce|=n;else{if(!(n&1073741824))return e=a!==null?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,b(hn,Ce),Ce|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=a!==null?a.baseLanes:n,b(hn,Ce),Ce|=r}else a!==null?(r=a.baseLanes|n,t.memoizedState=null):r=n,b(hn,Ce),Ce|=r;return ve(e,t,s,n),t.child}function Pc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function sa(e,t,n,r,s){var a=Se(n)?bt:me.current;return a=wn(t,a),xn(t,s),n=Za(e,t,n,r,a,s),r=qa(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,ht(e,t,s)):(Y&&r&&Ba(t),t.flags|=1,ve(e,t,n,s),t.child)}function yo(e,t,n,r,s){if(Se(n)){var a=!0;vs(t)}else a=!1;if(xn(t,s),t.stateNode===null)es(e,t),Sc(t,n,r),na(t,n,r,s),r=!0;else if(e===null){var i=t.stateNode,o=t.memoizedProps;i.props=o;var u=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ie(c):(c=Se(n)?bt:me.current,c=wn(t,c));var f=n.getDerivedStateFromProps,v=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";v||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==r||u!==c)&&ho(t,i,r,c),xt=!1;var h=t.memoizedState;i.state=h,ws(t,r,i,s),u=t.memoizedState,o!==r||h!==u||Ne.current||xt?(typeof f=="function"&&(ta(t,n,f,r),u=t.memoizedState),(o=xt||fo(t,n,o,r,h,u,c))?(v||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=c,r=o):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,nc(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:Ae(t.type,o),i.props=c,v=t.pendingProps,h=i.context,u=n.contextType,typeof u=="object"&&u!==null?u=Ie(u):(u=Se(n)?bt:me.current,u=wn(t,u));var S=n.getDerivedStateFromProps;(f=typeof S=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==v||h!==u)&&ho(t,i,r,u),xt=!1,h=t.memoizedState,i.state=h,ws(t,r,i,s);var N=t.memoizedState;o!==v||h!==N||Ne.current||xt?(typeof S=="function"&&(ta(t,n,S,r),N=t.memoizedState),(c=xt||fo(t,n,c,r,h,N,u)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,N,u),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,N,u)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=N),i.props=r,i.state=N,i.context=u,r=c):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return la(e,t,n,r,a,s)}function la(e,t,n,r,s,a){Pc(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return s&&ro(t,n,!1),ht(e,t,a);r=t.stateNode,wh.current=t;var o=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Sn(t,e.child,null,a),t.child=Sn(t,null,o,a)):ve(e,t,o,a),t.memoizedState=r.state,s&&ro(t,n,!0),t.child}function Lc(e){var t=e.stateNode;t.pendingContext?no(e,t.pendingContext,t.pendingContext!==t.context):t.context&&no(e,t.context,!1),Ga(e,t.containerInfo)}function jo(e,t,n,r,s){return Nn(),Ha(s),t.flags|=256,ve(e,t,n,r),t.child}var aa={dehydrated:null,treeContext:null,retryLane:0};function ia(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fc(e,t,n){var r=t.pendingProps,s=Z.current,a=!1,i=(t.flags&128)!==0,o;if((o=i)||(o=e!==null&&e.memoizedState===null?!1:(s&2)!==0),o?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),b(Z,s&1),e===null)return ql(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,a?(r=t.mode,a=t.child,i={mode:"hidden",children:i},!(r&1)&&a!==null?(a.childLanes=0,a.pendingProps=i):a=Hs(i,r,0,null),e=Vt(e,r,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=ia(n),t.memoizedState=aa,e):ni(t,i));if(s=e.memoizedState,s!==null&&(o=s.dehydrated,o!==null))return Nh(e,t,i,r,o,s,n);if(a){a=r.fallback,i=t.mode,s=e.child,o=s.sibling;var u={mode:"hidden",children:r.children};return!(i&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Ft(s,u),r.subtreeFlags=s.subtreeFlags&14680064),o!==null?a=Ft(o,a):(a=Vt(a,i,n,null),a.flags|=2),a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,i=e.child.memoizedState,i=i===null?ia(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},a.memoizedState=i,a.childLanes=e.childLanes&~n,t.memoizedState=aa,r}return a=e.child,e=a.sibling,r=Ft(a,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ni(e,t){return t=Hs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ar(e,t,n,r){return r!==null&&Ha(r),Sn(t,e.child,null,n),e=ni(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Nh(e,t,n,r,s,a,i){if(n)return t.flags&256?(t.flags&=-257,r=xl(Error(F(422))),Ar(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=r.fallback,s=t.mode,r=Hs({mode:"visible",children:r.children},s,0,null),a=Vt(a,s,i,null),a.flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,t.mode&1&&Sn(t,e.child,null,i),t.child.memoizedState=ia(i),t.memoizedState=aa,a);if(!(t.mode&1))return Ar(e,t,i,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var o=r.dgst;return r=o,a=Error(F(419)),r=xl(a,r,void 0),Ar(e,t,i,r)}if(o=(i&e.childLanes)!==0,we||o){if(r=ie,r!==null){switch(i&-i){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|i)?0:s,s!==0&&s!==a.retryLane&&(a.retryLane=s,ft(e,s),be(r,e,s,-1))}return oi(),r=xl(Error(F(421))),Ar(e,t,i,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=Mh.bind(null,e),s._reactRetry=t,null):(e=a.treeContext,_e=Et(s.nextSibling),Pe=t,Y=!0,We=null,e!==null&&(Te[De++]=at,Te[De++]=it,Te[De++]=Qt,at=e.id,it=e.overflow,Qt=t),t=ni(t,r.children),t.flags|=4096,t)}function wo(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ea(e.return,t,n)}function yl(e,t,n,r,s){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=s)}function Rc(e,t,n){var r=t.pendingProps,s=r.revealOrder,a=r.tail;if(ve(e,t,r.children,n),r=Z.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&wo(e,n,t);else if(e.tag===19)wo(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(b(Z,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Ns(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),yl(t,!1,s,n,a);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Ns(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}yl(t,!0,n,null,a);break;case"together":yl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function es(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Gt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(F(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Sh(e,t,n){switch(t.tag){case 3:Lc(t),Nn();break;case 5:rc(t);break;case 1:Se(t.type)&&vs(t);break;case 4:Ga(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;b(ys,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(b(Z,Z.current&1),t.flags|=128,null):n&t.child.childLanes?Fc(e,t,n):(b(Z,Z.current&1),e=ht(e,t,n),e!==null?e.sibling:null);b(Z,Z.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Rc(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),b(Z,Z.current),r)break;return null;case 22:case 23:return t.lanes=0,_c(e,t,n)}return ht(e,t,n)}var Tc,oa,Dc,zc;Tc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};oa=function(){};Dc=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Ht(tt.current);var a=null;switch(n){case"input":s=Fl(e,s),r=Fl(e,r),a=[];break;case"select":s=ee({},s,{value:void 0}),r=ee({},r,{value:void 0}),a=[];break;case"textarea":s=Dl(e,s),r=Dl(e,r),a=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ps)}Ml(n,r);var i;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var o=s[c];for(i in o)o.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(nr.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in r){var u=r[c];if(o=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==o&&(u!=null||o!=null))if(c==="style")if(o){for(i in o)!o.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&o[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(a||(a=[]),a.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,o=o?o.__html:void 0,u!=null&&o!==u&&(a=a||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(a=a||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(nr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&Q("scroll",e),a||o===u||(a=[])):(a=a||[]).push(c,u))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}};zc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Bn(e,t){if(!Y)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function he(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function kh(e,t,n){var r=t.pendingProps;switch(Aa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return he(t),null;case 1:return Se(t.type)&&ms(),he(t),null;case 3:return r=t.stateNode,kn(),K(Ne),K(me),Xa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ur(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,We!==null&&(va(We),We=null))),oa(e,t),he(t),null;case 5:Ya(t);var s=Ht(pr.current);if(n=t.type,e!==null&&t.stateNode!=null)Dc(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(F(166));return he(t),null}if(e=Ht(tt.current),Ur(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[qe]=t,r[fr]=a,e=(t.mode&1)!==0,n){case"dialog":Q("cancel",r),Q("close",r);break;case"iframe":case"object":case"embed":Q("load",r);break;case"video":case"audio":for(s=0;s<Qn.length;s++)Q(Qn[s],r);break;case"source":Q("error",r);break;case"img":case"image":case"link":Q("error",r),Q("load",r);break;case"details":Q("toggle",r);break;case"input":Li(r,a),Q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},Q("invalid",r);break;case"textarea":Ri(r,a),Q("invalid",r)}Ml(n,a),s=null;for(var i in a)if(a.hasOwnProperty(i)){var o=a[i];i==="children"?typeof o=="string"?r.textContent!==o&&(a.suppressHydrationWarning!==!0&&Or(r.textContent,o,e),s=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(a.suppressHydrationWarning!==!0&&Or(r.textContent,o,e),s=["children",""+o]):nr.hasOwnProperty(i)&&o!=null&&i==="onScroll"&&Q("scroll",r)}switch(n){case"input":Fr(r),Fi(r,a,!0);break;case"textarea":Fr(r),Ti(r);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(r.onclick=ps)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ou(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[qe]=t,e[fr]=r,Tc(e,t,!1,!1),t.stateNode=e;e:{switch(i=$l(n,r),n){case"dialog":Q("cancel",e),Q("close",e),s=r;break;case"iframe":case"object":case"embed":Q("load",e),s=r;break;case"video":case"audio":for(s=0;s<Qn.length;s++)Q(Qn[s],e);s=r;break;case"source":Q("error",e),s=r;break;case"img":case"image":case"link":Q("error",e),Q("load",e),s=r;break;case"details":Q("toggle",e),s=r;break;case"input":Li(e,r),s=Fl(e,r),Q("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=ee({},r,{value:void 0}),Q("invalid",e);break;case"textarea":Ri(e,r),s=Dl(e,r),Q("invalid",e);break;default:s=r}Ml(n,s),o=s;for(a in o)if(o.hasOwnProperty(a)){var u=o[a];a==="style"?du(e,u):a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&uu(e,u)):a==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&rr(e,u):typeof u=="number"&&rr(e,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(nr.hasOwnProperty(a)?u!=null&&a==="onScroll"&&Q("scroll",e):u!=null&&Ea(e,a,u,i))}switch(n){case"input":Fr(e),Fi(e,r,!1);break;case"textarea":Fr(e),Ti(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Rt(r.value));break;case"select":e.multiple=!!r.multiple,a=r.value,a!=null?pn(e,!!r.multiple,a,!1):r.defaultValue!=null&&pn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=ps)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return he(t),null;case 6:if(e&&t.stateNode!=null)zc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(F(166));if(n=Ht(pr.current),Ht(tt.current),Ur(t)){if(r=t.stateNode,n=t.memoizedProps,r[qe]=t,(a=r.nodeValue!==n)&&(e=Pe,e!==null))switch(e.tag){case 3:Or(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Or(r.nodeValue,n,(e.mode&1)!==0)}a&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[qe]=t,t.stateNode=r}return he(t),null;case 13:if(K(Z),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Y&&_e!==null&&t.mode&1&&!(t.flags&128))Zu(),Nn(),t.flags|=98560,a=!1;else if(a=Ur(t),r!==null&&r.dehydrated!==null){if(e===null){if(!a)throw Error(F(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(F(317));a[qe]=t}else Nn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;he(t),a=!1}else We!==null&&(va(We),We=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Z.current&1?le===0&&(le=3):oi())),t.updateQueue!==null&&(t.flags|=4),he(t),null);case 4:return kn(),oa(e,t),e===null&&cr(t.stateNode.containerInfo),he(t),null;case 10:return ba(t.type._context),he(t),null;case 17:return Se(t.type)&&ms(),he(t),null;case 19:if(K(Z),a=t.memoizedState,a===null)return he(t),null;if(r=(t.flags&128)!==0,i=a.rendering,i===null)if(r)Bn(a,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Ns(e),i!==null){for(t.flags|=128,Bn(a,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)a=n,e=r,a.flags&=14680066,i=a.alternate,i===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=i.childLanes,a.lanes=i.lanes,a.child=i.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=i.memoizedProps,a.memoizedState=i.memoizedState,a.updateQueue=i.updateQueue,a.type=i.type,e=i.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return b(Z,Z.current&1|2),t.child}e=e.sibling}a.tail!==null&&ne()>En&&(t.flags|=128,r=!0,Bn(a,!1),t.lanes=4194304)}else{if(!r)if(e=Ns(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Bn(a,!0),a.tail===null&&a.tailMode==="hidden"&&!i.alternate&&!Y)return he(t),null}else 2*ne()-a.renderingStartTime>En&&n!==1073741824&&(t.flags|=128,r=!0,Bn(a,!1),t.lanes=4194304);a.isBackwards?(i.sibling=t.child,t.child=i):(n=a.last,n!==null?n.sibling=i:t.child=i,a.last=i)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=ne(),t.sibling=null,n=Z.current,b(Z,r?n&1|2:n&1),t):(he(t),null);case 22:case 23:return ii(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ce&1073741824&&(he(t),t.subtreeFlags&6&&(t.flags|=8192)):he(t),null;case 24:return null;case 25:return null}throw Error(F(156,t.tag))}function Ch(e,t){switch(Aa(t),t.tag){case 1:return Se(t.type)&&ms(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return kn(),K(Ne),K(me),Xa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ya(t),null;case 13:if(K(Z),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(F(340));Nn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(Z),null;case 4:return kn(),null;case 10:return ba(t.type._context),null;case 22:case 23:return ii(),null;case 24:return null;default:return null}}var Hr=!1,pe=!1,Eh=typeof WeakSet=="function"?WeakSet:Set,z=null;function fn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){te(e,t,r)}else n.current=null}function ua(e,t,n){try{n()}catch(r){te(e,t,r)}}var No=!1;function _h(e,t){if(Ql=ds,e=Uu(),Ua(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break e}var i=0,o=-1,u=-1,c=0,f=0,v=e,h=null;t:for(;;){for(var S;v!==n||s!==0&&v.nodeType!==3||(o=i+s),v!==a||r!==0&&v.nodeType!==3||(u=i+r),v.nodeType===3&&(i+=v.nodeValue.length),(S=v.firstChild)!==null;)h=v,v=S;for(;;){if(v===e)break t;if(h===n&&++c===s&&(o=i),h===a&&++f===r&&(u=i),(S=v.nextSibling)!==null)break;v=h,h=v.parentNode}v=S}n=o===-1||u===-1?null:{start:o,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Kl={focusedElem:e,selectionRange:n},ds=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var N=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(N!==null){var _=N.memoizedProps,L=N.memoizedState,p=t.stateNode,d=p.getSnapshotBeforeUpdate(t.elementType===t.type?_:Ae(t.type,_),L);p.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(F(163))}}catch(w){te(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return N=No,No=!1,N}function qn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var a=s.destroy;s.destroy=void 0,a!==void 0&&ua(t,n,a)}s=s.next}while(s!==r)}}function Bs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ca(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Mc(e){var t=e.alternate;t!==null&&(e.alternate=null,Mc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[qe],delete t[fr],delete t[Xl],delete t[uh],delete t[ch])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function $c(e){return e.tag===5||e.tag===3||e.tag===4}function So(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||$c(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function da(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ps));else if(r!==4&&(e=e.child,e!==null))for(da(e,t,n),e=e.sibling;e!==null;)da(e,t,n),e=e.sibling}function fa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(fa(e,t,n),e=e.sibling;e!==null;)fa(e,t,n),e=e.sibling}var ue=null,He=!1;function vt(e,t,n){for(n=n.child;n!==null;)Ic(e,t,n),n=n.sibling}function Ic(e,t,n){if(et&&typeof et.onCommitFiberUnmount=="function")try{et.onCommitFiberUnmount(Ts,n)}catch{}switch(n.tag){case 5:pe||fn(n,t);case 6:var r=ue,s=He;ue=null,vt(e,t,n),ue=r,He=s,ue!==null&&(He?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(He?(e=ue,n=n.stateNode,e.nodeType===8?fl(e.parentNode,n):e.nodeType===1&&fl(e,n),ir(e)):fl(ue,n.stateNode));break;case 4:r=ue,s=He,ue=n.stateNode.containerInfo,He=!0,vt(e,t,n),ue=r,He=s;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var a=s,i=a.destroy;a=a.tag,i!==void 0&&(a&2||a&4)&&ua(n,t,i),s=s.next}while(s!==r)}vt(e,t,n);break;case 1:if(!pe&&(fn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){te(n,t,o)}vt(e,t,n);break;case 21:vt(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,vt(e,t,n),pe=r):vt(e,t,n);break;default:vt(e,t,n)}}function ko(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Eh),t.forEach(function(r){var s=$h.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Be(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var a=e,i=t,o=i;e:for(;o!==null;){switch(o.tag){case 5:ue=o.stateNode,He=!1;break e;case 3:ue=o.stateNode.containerInfo,He=!0;break e;case 4:ue=o.stateNode.containerInfo,He=!0;break e}o=o.return}if(ue===null)throw Error(F(160));Ic(a,i,s),ue=null,He=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){te(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Oc(t,e),t=t.sibling}function Oc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Be(t,e),Je(e),r&4){try{qn(3,e,e.return),Bs(3,e)}catch(_){te(e,e.return,_)}try{qn(5,e,e.return)}catch(_){te(e,e.return,_)}}break;case 1:Be(t,e),Je(e),r&512&&n!==null&&fn(n,n.return);break;case 5:if(Be(t,e),Je(e),r&512&&n!==null&&fn(n,n.return),e.flags&32){var s=e.stateNode;try{rr(s,"")}catch(_){te(e,e.return,_)}}if(r&4&&(s=e.stateNode,s!=null)){var a=e.memoizedProps,i=n!==null?n.memoizedProps:a,o=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{o==="input"&&a.type==="radio"&&a.name!=null&&au(s,a),$l(o,i);var c=$l(o,a);for(i=0;i<u.length;i+=2){var f=u[i],v=u[i+1];f==="style"?du(s,v):f==="dangerouslySetInnerHTML"?uu(s,v):f==="children"?rr(s,v):Ea(s,f,v,c)}switch(o){case"input":Rl(s,a);break;case"textarea":iu(s,a);break;case"select":var h=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!a.multiple;var S=a.value;S!=null?pn(s,!!a.multiple,S,!1):h!==!!a.multiple&&(a.defaultValue!=null?pn(s,!!a.multiple,a.defaultValue,!0):pn(s,!!a.multiple,a.multiple?[]:"",!1))}s[fr]=a}catch(_){te(e,e.return,_)}}break;case 6:if(Be(t,e),Je(e),r&4){if(e.stateNode===null)throw Error(F(162));s=e.stateNode,a=e.memoizedProps;try{s.nodeValue=a}catch(_){te(e,e.return,_)}}break;case 3:if(Be(t,e),Je(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ir(t.containerInfo)}catch(_){te(e,e.return,_)}break;case 4:Be(t,e),Je(e);break;case 13:Be(t,e),Je(e),s=e.child,s.flags&8192&&(a=s.memoizedState!==null,s.stateNode.isHidden=a,!a||s.alternate!==null&&s.alternate.memoizedState!==null||(li=ne())),r&4&&ko(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(c=pe)||f,Be(t,e),pe=c):Be(t,e),Je(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(z=e,f=e.child;f!==null;){for(v=z=f;z!==null;){switch(h=z,S=h.child,h.tag){case 0:case 11:case 14:case 15:qn(4,h,h.return);break;case 1:fn(h,h.return);var N=h.stateNode;if(typeof N.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,N.props=t.memoizedProps,N.state=t.memoizedState,N.componentWillUnmount()}catch(_){te(r,n,_)}}break;case 5:fn(h,h.return);break;case 22:if(h.memoizedState!==null){Eo(v);continue}}S!==null?(S.return=h,z=S):Eo(v)}f=f.sibling}e:for(f=null,v=e;;){if(v.tag===5){if(f===null){f=v;try{s=v.stateNode,c?(a=s.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(o=v.stateNode,u=v.memoizedProps.style,i=u!=null&&u.hasOwnProperty("display")?u.display:null,o.style.display=cu("display",i))}catch(_){te(e,e.return,_)}}}else if(v.tag===6){if(f===null)try{v.stateNode.nodeValue=c?"":v.memoizedProps}catch(_){te(e,e.return,_)}}else if((v.tag!==22&&v.tag!==23||v.memoizedState===null||v===e)&&v.child!==null){v.child.return=v,v=v.child;continue}if(v===e)break e;for(;v.sibling===null;){if(v.return===null||v.return===e)break e;f===v&&(f=null),v=v.return}f===v&&(f=null),v.sibling.return=v.return,v=v.sibling}}break;case 19:Be(t,e),Je(e),r&4&&ko(e);break;case 21:break;default:Be(t,e),Je(e)}}function Je(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if($c(n)){var r=n;break e}n=n.return}throw Error(F(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(rr(s,""),r.flags&=-33);var a=So(e);fa(e,a,s);break;case 3:case 4:var i=r.stateNode.containerInfo,o=So(e);da(e,o,i);break;default:throw Error(F(161))}}catch(u){te(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ph(e,t,n){z=e,Uc(e)}function Uc(e,t,n){for(var r=(e.mode&1)!==0;z!==null;){var s=z,a=s.child;if(s.tag===22&&r){var i=s.memoizedState!==null||Hr;if(!i){var o=s.alternate,u=o!==null&&o.memoizedState!==null||pe;o=Hr;var c=pe;if(Hr=i,(pe=u)&&!c)for(z=s;z!==null;)i=z,u=i.child,i.tag===22&&i.memoizedState!==null?_o(s):u!==null?(u.return=i,z=u):_o(s);for(;a!==null;)z=a,Uc(a),a=a.sibling;z=s,Hr=o,pe=c}Co(e)}else s.subtreeFlags&8772&&a!==null?(a.return=s,z=a):Co(e)}}function Co(e){for(;z!==null;){var t=z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||Bs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Ae(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&oo(t,a,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}oo(t,i,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var v=f.dehydrated;v!==null&&ir(v)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(F(163))}pe||t.flags&512&&ca(t)}catch(h){te(t,t.return,h)}}if(t===e){z=null;break}if(n=t.sibling,n!==null){n.return=t.return,z=n;break}z=t.return}}function Eo(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,z=n;break}z=t.return}}function _o(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Bs(4,t)}catch(u){te(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){te(t,s,u)}}var a=t.return;try{ca(t)}catch(u){te(t,a,u)}break;case 5:var i=t.return;try{ca(t)}catch(u){te(t,i,u)}}}catch(u){te(t,t.return,u)}if(t===e){z=null;break}var o=t.sibling;if(o!==null){o.return=t.return,z=o;break}z=t.return}}var Lh=Math.ceil,Cs=mt.ReactCurrentDispatcher,ri=mt.ReactCurrentOwner,$e=mt.ReactCurrentBatchConfig,A=0,ie=null,re=null,ce=0,Ce=0,hn=zt(0),le=0,xr=null,Gt=0,As=0,si=0,er=null,je=null,li=0,En=1/0,st=null,Es=!1,ha=null,Pt=null,Wr=!1,Nt=null,_s=0,tr=0,pa=null,ts=-1,ns=0;function ge(){return A&6?ne():ts!==-1?ts:ts=ne()}function Lt(e){return e.mode&1?A&2&&ce!==0?ce&-ce:fh.transition!==null?(ns===0&&(ns=Su()),ns):(e=W,e!==0||(e=window.event,e=e===void 0?16:Fu(e.type)),e):1}function be(e,t,n,r){if(50<tr)throw tr=0,pa=null,Error(F(185));wr(e,n,r),(!(A&2)||e!==ie)&&(e===ie&&(!(A&2)&&(As|=n),le===4&&jt(e,ce)),ke(e,r),n===1&&A===0&&!(t.mode&1)&&(En=ne()+500,Is&&Mt()))}function ke(e,t){var n=e.callbackNode;ff(e,t);var r=cs(e,e===ie?ce:0);if(r===0)n!==null&&Mi(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Mi(n),t===1)e.tag===0?dh(Po.bind(null,e)):Yu(Po.bind(null,e)),ih(function(){!(A&6)&&Mt()}),n=null;else{switch(ku(r)){case 1:n=Ra;break;case 4:n=wu;break;case 16:n=us;break;case 536870912:n=Nu;break;default:n=us}n=Kc(n,Bc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Bc(e,t){if(ts=-1,ns=0,A&6)throw Error(F(327));var n=e.callbackNode;if(yn()&&e.callbackNode!==n)return null;var r=cs(e,e===ie?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ps(e,r);else{t=r;var s=A;A|=2;var a=Hc();(ie!==e||ce!==t)&&(st=null,En=ne()+500,Wt(e,t));do try{Th();break}catch(o){Ac(e,o)}while(!0);Va(),Cs.current=a,A=s,re!==null?t=0:(ie=null,ce=0,t=le)}if(t!==0){if(t===2&&(s=Al(e),s!==0&&(r=s,t=ma(e,s))),t===1)throw n=xr,Wt(e,0),jt(e,r),ke(e,ne()),n;if(t===6)jt(e,r);else{if(s=e.current.alternate,!(r&30)&&!Fh(s)&&(t=Ps(e,r),t===2&&(a=Al(e),a!==0&&(r=a,t=ma(e,a))),t===1))throw n=xr,Wt(e,0),jt(e,r),ke(e,ne()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(F(345));case 2:Ot(e,je,st);break;case 3:if(jt(e,r),(r&130023424)===r&&(t=li+500-ne(),10<t)){if(cs(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){ge(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Yl(Ot.bind(null,e,je,st),t);break}Ot(e,je,st);break;case 4:if(jt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var i=31-Ve(r);a=1<<i,i=t[i],i>s&&(s=i),r&=~a}if(r=s,r=ne()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Lh(r/1960))-r,10<r){e.timeoutHandle=Yl(Ot.bind(null,e,je,st),r);break}Ot(e,je,st);break;case 5:Ot(e,je,st);break;default:throw Error(F(329))}}}return ke(e,ne()),e.callbackNode===n?Bc.bind(null,e):null}function ma(e,t){var n=er;return e.current.memoizedState.isDehydrated&&(Wt(e,t).flags|=256),e=Ps(e,t),e!==2&&(t=je,je=n,t!==null&&va(t)),e}function va(e){je===null?je=e:je.push.apply(je,e)}function Fh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],a=s.getSnapshot;s=s.value;try{if(!Qe(a(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function jt(e,t){for(t&=~si,t&=~As,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function Po(e){if(A&6)throw Error(F(327));yn();var t=cs(e,0);if(!(t&1))return ke(e,ne()),null;var n=Ps(e,t);if(e.tag!==0&&n===2){var r=Al(e);r!==0&&(t=r,n=ma(e,r))}if(n===1)throw n=xr,Wt(e,0),jt(e,t),ke(e,ne()),n;if(n===6)throw Error(F(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Ot(e,je,st),ke(e,ne()),null}function ai(e,t){var n=A;A|=1;try{return e(t)}finally{A=n,A===0&&(En=ne()+500,Is&&Mt())}}function Yt(e){Nt!==null&&Nt.tag===0&&!(A&6)&&yn();var t=A;A|=1;var n=$e.transition,r=W;try{if($e.transition=null,W=1,e)return e()}finally{W=r,$e.transition=n,A=t,!(A&6)&&Mt()}}function ii(){Ce=hn.current,K(hn)}function Wt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ah(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(Aa(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ms();break;case 3:kn(),K(Ne),K(me),Xa();break;case 5:Ya(r);break;case 4:kn();break;case 13:K(Z);break;case 19:K(Z);break;case 10:ba(r.type._context);break;case 22:case 23:ii()}n=n.return}if(ie=e,re=e=Ft(e.current,null),ce=Ce=t,le=0,xr=null,si=As=Gt=0,je=er=null,At!==null){for(t=0;t<At.length;t++)if(n=At[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,a=n.pending;if(a!==null){var i=a.next;a.next=s,r.next=i}n.pending=r}At=null}return e}function Ac(e,t){do{var n=re;try{if(Va(),Zr.current=ks,Ss){for(var r=q.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ss=!1}if(Kt=0,ae=se=q=null,Zn=!1,mr=0,ri.current=null,n===null||n.return===null){le=1,xr=t,re=null;break}e:{var a=e,i=n.return,o=n,u=t;if(t=ce,o.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,f=o,v=f.tag;if(!(f.mode&1)&&(v===0||v===11||v===15)){var h=f.alternate;h?(f.updateQueue=h.updateQueue,f.memoizedState=h.memoizedState,f.lanes=h.lanes):(f.updateQueue=null,f.memoizedState=null)}var S=mo(i);if(S!==null){S.flags&=-257,vo(S,i,o,a,t),S.mode&1&&po(a,c,t),t=S,u=c;var N=t.updateQueue;if(N===null){var _=new Set;_.add(u),t.updateQueue=_}else N.add(u);break e}else{if(!(t&1)){po(a,c,t),oi();break e}u=Error(F(426))}}else if(Y&&o.mode&1){var L=mo(i);if(L!==null){!(L.flags&65536)&&(L.flags|=256),vo(L,i,o,a,t),Ha(Cn(u,o));break e}}a=u=Cn(u,o),le!==4&&(le=2),er===null?er=[a]:er.push(a),a=i;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var p=kc(a,u,t);io(a,p);break e;case 1:o=u;var d=a.type,m=a.stateNode;if(!(a.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Pt===null||!Pt.has(m)))){a.flags|=65536,t&=-t,a.lanes|=t;var w=Cc(a,o,t);io(a,w);break e}}a=a.return}while(a!==null)}Vc(n)}catch(x){t=x,re===n&&n!==null&&(re=n=n.return);continue}break}while(!0)}function Hc(){var e=Cs.current;return Cs.current=ks,e===null?ks:e}function oi(){(le===0||le===3||le===2)&&(le=4),ie===null||!(Gt&268435455)&&!(As&268435455)||jt(ie,ce)}function Ps(e,t){var n=A;A|=2;var r=Hc();(ie!==e||ce!==t)&&(st=null,Wt(e,t));do try{Rh();break}catch(s){Ac(e,s)}while(!0);if(Va(),A=n,Cs.current=r,re!==null)throw Error(F(261));return ie=null,ce=0,le}function Rh(){for(;re!==null;)Wc(re)}function Th(){for(;re!==null&&!nf();)Wc(re)}function Wc(e){var t=Qc(e.alternate,e,Ce);e.memoizedProps=e.pendingProps,t===null?Vc(e):re=t,ri.current=null}function Vc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ch(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,re=null;return}}else if(n=kh(n,t,Ce),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);le===0&&(le=5)}function Ot(e,t,n){var r=W,s=$e.transition;try{$e.transition=null,W=1,Dh(e,t,n,r)}finally{$e.transition=s,W=r}return null}function Dh(e,t,n,r){do yn();while(Nt!==null);if(A&6)throw Error(F(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(F(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(hf(e,a),e===ie&&(re=ie=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Wr||(Wr=!0,Kc(us,function(){return yn(),null})),a=(n.flags&15990)!==0,n.subtreeFlags&15990||a){a=$e.transition,$e.transition=null;var i=W;W=1;var o=A;A|=4,ri.current=null,_h(e,n),Oc(n,e),qf(Kl),ds=!!Ql,Kl=Ql=null,e.current=n,Ph(n),rf(),A=o,W=i,$e.transition=a}else e.current=n;if(Wr&&(Wr=!1,Nt=e,_s=s),a=e.pendingLanes,a===0&&(Pt=null),af(n.stateNode),ke(e,ne()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Es)throw Es=!1,e=ha,ha=null,e;return _s&1&&e.tag!==0&&yn(),a=e.pendingLanes,a&1?e===pa?tr++:(tr=0,pa=e):tr=0,Mt(),null}function yn(){if(Nt!==null){var e=ku(_s),t=$e.transition,n=W;try{if($e.transition=null,W=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,_s=0,A&6)throw Error(F(331));var s=A;for(A|=4,z=e.current;z!==null;){var a=z,i=a.child;if(z.flags&16){var o=a.deletions;if(o!==null){for(var u=0;u<o.length;u++){var c=o[u];for(z=c;z!==null;){var f=z;switch(f.tag){case 0:case 11:case 15:qn(8,f,a)}var v=f.child;if(v!==null)v.return=f,z=v;else for(;z!==null;){f=z;var h=f.sibling,S=f.return;if(Mc(f),f===c){z=null;break}if(h!==null){h.return=S,z=h;break}z=S}}}var N=a.alternate;if(N!==null){var _=N.child;if(_!==null){N.child=null;do{var L=_.sibling;_.sibling=null,_=L}while(_!==null)}}z=a}}if(a.subtreeFlags&2064&&i!==null)i.return=a,z=i;else e:for(;z!==null;){if(a=z,a.flags&2048)switch(a.tag){case 0:case 11:case 15:qn(9,a,a.return)}var p=a.sibling;if(p!==null){p.return=a.return,z=p;break e}z=a.return}}var d=e.current;for(z=d;z!==null;){i=z;var m=i.child;if(i.subtreeFlags&2064&&m!==null)m.return=i,z=m;else e:for(i=d;z!==null;){if(o=z,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Bs(9,o)}}catch(x){te(o,o.return,x)}if(o===i){z=null;break e}var w=o.sibling;if(w!==null){w.return=o.return,z=w;break e}z=o.return}}if(A=s,Mt(),et&&typeof et.onPostCommitFiberRoot=="function")try{et.onPostCommitFiberRoot(Ts,e)}catch{}r=!0}return r}finally{W=n,$e.transition=t}}return!1}function Lo(e,t,n){t=Cn(n,t),t=kc(e,t,1),e=_t(e,t,1),t=ge(),e!==null&&(wr(e,1,t),ke(e,t))}function te(e,t,n){if(e.tag===3)Lo(e,e,n);else for(;t!==null;){if(t.tag===3){Lo(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Pt===null||!Pt.has(r))){e=Cn(n,e),e=Cc(t,e,1),t=_t(t,e,1),e=ge(),t!==null&&(wr(t,1,e),ke(t,e));break}}t=t.return}}function zh(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ge(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(ce&n)===n&&(le===4||le===3&&(ce&130023424)===ce&&500>ne()-li?Wt(e,0):si|=n),ke(e,t)}function bc(e,t){t===0&&(e.mode&1?(t=Dr,Dr<<=1,!(Dr&130023424)&&(Dr=4194304)):t=1);var n=ge();e=ft(e,t),e!==null&&(wr(e,t,n),ke(e,n))}function Mh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),bc(e,n)}function $h(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(F(314))}r!==null&&r.delete(t),bc(e,n)}var Qc;Qc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,Sh(e,t,n);we=!!(e.flags&131072)}else we=!1,Y&&t.flags&1048576&&Xu(t,xs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;es(e,t),e=t.pendingProps;var s=wn(t,me.current);xn(t,n),s=Za(null,t,r,e,s,n);var a=qa();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Se(r)?(a=!0,vs(t)):a=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Ka(t),s.updater=Us,t.stateNode=s,s._reactInternals=t,na(t,r,e,n),t=la(null,t,r,!0,a,n)):(t.tag=0,Y&&a&&Ba(t),ve(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(es(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=Oh(r),e=Ae(r,e),s){case 0:t=sa(null,t,r,e,n);break e;case 1:t=yo(null,t,r,e,n);break e;case 11:t=go(null,t,r,e,n);break e;case 14:t=xo(null,t,r,Ae(r.type,e),n);break e}throw Error(F(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ae(r,s),sa(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ae(r,s),yo(e,t,r,s,n);case 3:e:{if(Lc(t),e===null)throw Error(F(387));r=t.pendingProps,a=t.memoizedState,s=a.element,nc(e,t),ws(t,r,null,n);var i=t.memoizedState;if(r=i.element,a.isDehydrated)if(a={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){s=Cn(Error(F(423)),t),t=jo(e,t,r,n,s);break e}else if(r!==s){s=Cn(Error(F(424)),t),t=jo(e,t,r,n,s);break e}else for(_e=Et(t.stateNode.containerInfo.firstChild),Pe=t,Y=!0,We=null,n=ec(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Nn(),r===s){t=ht(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return rc(t),e===null&&ql(t),r=t.type,s=t.pendingProps,a=e!==null?e.memoizedProps:null,i=s.children,Gl(r,s)?i=null:a!==null&&Gl(r,a)&&(t.flags|=32),Pc(e,t),ve(e,t,i,n),t.child;case 6:return e===null&&ql(t),null;case 13:return Fc(e,t,n);case 4:return Ga(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Sn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ae(r,s),go(e,t,r,s,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,a=t.memoizedProps,i=s.value,b(ys,r._currentValue),r._currentValue=i,a!==null)if(Qe(a.value,i)){if(a.children===s.children&&!Ne.current){t=ht(e,t,n);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var o=a.dependencies;if(o!==null){i=a.child;for(var u=o.firstContext;u!==null;){if(u.context===r){if(a.tag===1){u=ot(-1,n&-n),u.tag=2;var c=a.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}a.lanes|=n,u=a.alternate,u!==null&&(u.lanes|=n),ea(a.return,n,t),o.lanes|=n;break}u=u.next}}else if(a.tag===10)i=a.type===t.type?null:a.child;else if(a.tag===18){if(i=a.return,i===null)throw Error(F(341));i.lanes|=n,o=i.alternate,o!==null&&(o.lanes|=n),ea(i,n,t),i=a.sibling}else i=a.child;if(i!==null)i.return=a;else for(i=a;i!==null;){if(i===t){i=null;break}if(a=i.sibling,a!==null){a.return=i.return,i=a;break}i=i.return}a=i}ve(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,xn(t,n),s=Ie(s),r=r(s),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,s=Ae(r,t.pendingProps),s=Ae(r.type,s),xo(e,t,r,s,n);case 15:return Ec(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ae(r,s),es(e,t),t.tag=1,Se(r)?(e=!0,vs(t)):e=!1,xn(t,n),Sc(t,r,s),na(t,r,s,n),la(null,t,r,!0,e,n);case 19:return Rc(e,t,n);case 22:return _c(e,t,n)}throw Error(F(156,t.tag))};function Kc(e,t){return ju(e,t)}function Ih(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new Ih(e,t,n,r)}function ui(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Oh(e){if(typeof e=="function")return ui(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Pa)return 11;if(e===La)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function rs(e,t,n,r,s,a){var i=2;if(r=e,typeof e=="function")ui(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case nn:return Vt(n.children,s,a,t);case _a:i=8,s|=8;break;case El:return e=ze(12,n,t,s|2),e.elementType=El,e.lanes=a,e;case _l:return e=ze(13,n,t,s),e.elementType=_l,e.lanes=a,e;case Pl:return e=ze(19,n,t,s),e.elementType=Pl,e.lanes=a,e;case ru:return Hs(n,s,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case tu:i=10;break e;case nu:i=9;break e;case Pa:i=11;break e;case La:i=14;break e;case gt:i=16,r=null;break e}throw Error(F(130,e==null?e:typeof e,""))}return t=ze(i,n,t,s),t.elementType=e,t.type=r,t.lanes=a,t}function Vt(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Hs(e,t,n,r){return e=ze(22,e,r,t),e.elementType=ru,e.lanes=n,e.stateNode={isHidden:!1},e}function jl(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function wl(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uh(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=tl(0),this.expirationTimes=tl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tl(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function ci(e,t,n,r,s,a,i,o,u){return e=new Uh(e,t,n,o,u),t===1?(t=1,a===!0&&(t|=8)):t=0,a=ze(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ka(a),e}function Bh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:tn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Gc(e){if(!e)return Tt;e=e._reactInternals;e:{if(Zt(e)!==e||e.tag!==1)throw Error(F(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Se(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(F(171))}if(e.tag===1){var n=e.type;if(Se(n))return Gu(e,n,t)}return t}function Yc(e,t,n,r,s,a,i,o,u){return e=ci(n,r,!0,e,s,a,i,o,u),e.context=Gc(null),n=e.current,r=ge(),s=Lt(n),a=ot(r,s),a.callback=t??null,_t(n,a,s),e.current.lanes=s,wr(e,s,r),ke(e,r),e}function Ws(e,t,n,r){var s=t.current,a=ge(),i=Lt(s);return n=Gc(n),t.context===null?t.context=n:t.pendingContext=n,t=ot(a,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=_t(s,t,i),e!==null&&(be(e,s,i,a),Jr(e,s,i)),i}function Ls(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Fo(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function di(e,t){Fo(e,t),(e=e.alternate)&&Fo(e,t)}function Ah(){return null}var Xc=typeof reportError=="function"?reportError:function(e){console.error(e)};function fi(e){this._internalRoot=e}Vs.prototype.render=fi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(F(409));Ws(e,t,null,null)};Vs.prototype.unmount=fi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yt(function(){Ws(null,e,null,null)}),t[dt]=null}};function Vs(e){this._internalRoot=e}Vs.prototype.unstable_scheduleHydration=function(e){if(e){var t=_u();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yt.length&&t!==0&&t<yt[n].priority;n++);yt.splice(n,0,e),n===0&&Lu(e)}};function hi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function bs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ro(){}function Hh(e,t,n,r,s){if(s){if(typeof r=="function"){var a=r;r=function(){var c=Ls(i);a.call(c)}}var i=Yc(t,r,e,0,null,!1,!1,"",Ro);return e._reactRootContainer=i,e[dt]=i.current,cr(e.nodeType===8?e.parentNode:e),Yt(),i}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var o=r;r=function(){var c=Ls(u);o.call(c)}}var u=ci(e,0,!1,null,null,!1,!1,"",Ro);return e._reactRootContainer=u,e[dt]=u.current,cr(e.nodeType===8?e.parentNode:e),Yt(function(){Ws(t,u,n,r)}),u}function Qs(e,t,n,r,s){var a=n._reactRootContainer;if(a){var i=a;if(typeof s=="function"){var o=s;s=function(){var u=Ls(i);o.call(u)}}Ws(t,i,e,s)}else i=Hh(n,t,e,s,r);return Ls(i)}Cu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=bn(t.pendingLanes);n!==0&&(Ta(t,n|1),ke(t,ne()),!(A&6)&&(En=ne()+500,Mt()))}break;case 13:Yt(function(){var r=ft(e,1);if(r!==null){var s=ge();be(r,e,1,s)}}),di(e,1)}};Da=function(e){if(e.tag===13){var t=ft(e,134217728);if(t!==null){var n=ge();be(t,e,134217728,n)}di(e,134217728)}};Eu=function(e){if(e.tag===13){var t=Lt(e),n=ft(e,t);if(n!==null){var r=ge();be(n,e,t,r)}di(e,t)}};_u=function(){return W};Pu=function(e,t){var n=W;try{return W=e,t()}finally{W=n}};Ol=function(e,t,n){switch(t){case"input":if(Rl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=$s(r);if(!s)throw Error(F(90));lu(r),Rl(r,s)}}}break;case"textarea":iu(e,n);break;case"select":t=n.value,t!=null&&pn(e,!!n.multiple,t,!1)}};pu=ai;mu=Yt;var Wh={usingClientEntryPoint:!1,Events:[Sr,an,$s,fu,hu,ai]},An={findFiberByHostInstance:Bt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Vh={bundleType:An.bundleType,version:An.version,rendererPackageName:An.rendererPackageName,rendererConfig:An.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:mt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=xu(e),e===null?null:e.stateNode},findFiberByHostInstance:An.findFiberByHostInstance||Ah,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Vr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Vr.isDisabled&&Vr.supportsFiber)try{Ts=Vr.inject(Vh),et=Vr}catch{}}Fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Wh;Fe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!hi(t))throw Error(F(200));return Bh(e,t,null,n)};Fe.createRoot=function(e,t){if(!hi(e))throw Error(F(299));var n=!1,r="",s=Xc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=ci(e,1,!1,null,null,n,!1,r,s),e[dt]=t.current,cr(e.nodeType===8?e.parentNode:e),new fi(t)};Fe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(F(188)):(e=Object.keys(e).join(","),Error(F(268,e)));return e=xu(t),e=e===null?null:e.stateNode,e};Fe.flushSync=function(e){return Yt(e)};Fe.hydrate=function(e,t,n){if(!bs(t))throw Error(F(200));return Qs(null,e,t,!0,n)};Fe.hydrateRoot=function(e,t,n){if(!hi(e))throw Error(F(405));var r=n!=null&&n.hydratedSources||null,s=!1,a="",i=Xc;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Yc(t,null,e,1,n??null,s,!1,a,i),e[dt]=t.current,cr(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Vs(t)};Fe.render=function(e,t,n){if(!bs(t))throw Error(F(200));return Qs(null,e,t,!1,n)};Fe.unmountComponentAtNode=function(e){if(!bs(e))throw Error(F(40));return e._reactRootContainer?(Yt(function(){Qs(null,null,e,!1,function(){e._reactRootContainer=null,e[dt]=null})}),!0):!1};Fe.unstable_batchedUpdates=ai;Fe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!bs(n))throw Error(F(200));if(e==null||e._reactInternals===void 0)throw Error(F(38));return Qs(e,t,n,!1,r)};Fe.version="18.3.1-next-f1338f8080-20240426";function Jc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Jc)}catch(e){console.error(e)}}Jc(),Jo.exports=Fe;var bh=Jo.exports,To=bh;kl.createRoot=To.createRoot,kl.hydrateRoot=To.hydrateRoot;var pi={};Object.defineProperty(pi,"__esModule",{value:!0});pi.parse=Zh;pi.serialize=qh;const Qh=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Kh=/^[\u0021-\u003A\u003C-\u007E]*$/,Gh=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Yh=/^[\u0020-\u003A\u003D-\u007E]*$/,Xh=Object.prototype.toString,Jh=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Zh(e,t){const n=new Jh,r=e.length;if(r<2)return n;const s=(t==null?void 0:t.decode)||ep;let a=0;do{const i=e.indexOf("=",a);if(i===-1)break;const o=e.indexOf(";",a),u=o===-1?r:o;if(i>u){a=e.lastIndexOf(";",i-1)+1;continue}const c=Do(e,a,i),f=zo(e,i,c),v=e.slice(c,f);if(n[v]===void 0){let h=Do(e,i+1,u),S=zo(e,u,h);const N=s(e.slice(h,S));n[v]=N}a=u+1}while(a<r);return n}function Do(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function zo(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function qh(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!Qh.test(e))throw new TypeError(`argument name is invalid: ${e}`);const s=r(t);if(!Kh.test(s))throw new TypeError(`argument val is invalid: ${t}`);let a=e+"="+s;if(!n)return a;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);a+="; Max-Age="+n.maxAge}if(n.domain){if(!Gh.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);a+="; Domain="+n.domain}if(n.path){if(!Yh.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);a+="; Path="+n.path}if(n.expires){if(!tp(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);a+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(a+="; HttpOnly"),n.secure&&(a+="; Secure"),n.partitioned&&(a+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return a}function ep(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function tp(e){return Xh.call(e)==="[object Date]"}var Mo="popstate";function np(e={}){function t(r,s){let{pathname:a,search:i,hash:o}=r.location;return ga("",{pathname:a,search:i,hash:o},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:yr(s)}return sp(t,n,null,e)}function X(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ke(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function rp(){return Math.random().toString(36).substring(2,10)}function $o(e,t){return{usr:e.state,key:e.key,idx:t}}function ga(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Fn(t):t,state:n,key:t&&t.key||r||rp()}}function yr({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Fn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function sp(e,t,n,r={}){let{window:s=document.defaultView,v5Compat:a=!1}=r,i=s.history,o="POP",u=null,c=f();c==null&&(c=0,i.replaceState({...i.state,idx:c},""));function f(){return(i.state||{idx:null}).idx}function v(){o="POP";let L=f(),p=L==null?null:L-c;c=L,u&&u({action:o,location:_.location,delta:p})}function h(L,p){o="PUSH";let d=ga(_.location,L,p);c=f()+1;let m=$o(d,c),w=_.createHref(d);try{i.pushState(m,"",w)}catch(x){if(x instanceof DOMException&&x.name==="DataCloneError")throw x;s.location.assign(w)}a&&u&&u({action:o,location:_.location,delta:1})}function S(L,p){o="REPLACE";let d=ga(_.location,L,p);c=f();let m=$o(d,c),w=_.createHref(d);i.replaceState(m,"",w),a&&u&&u({action:o,location:_.location,delta:0})}function N(L){return lp(L)}let _={get action(){return o},get location(){return e(s,i)},listen(L){if(u)throw new Error("A history only accepts one active listener");return s.addEventListener(Mo,v),u=L,()=>{s.removeEventListener(Mo,v),u=null}},createHref(L){return t(s,L)},createURL:N,encodeLocation(L){let p=N(L);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:h,replace:S,go(L){return i.go(L)}};return _}function lp(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),X(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:yr(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function Zc(e,t,n="/"){return ap(e,t,n,!1)}function ap(e,t,n,r){let s=typeof t=="string"?Fn(t):t,a=pt(s.pathname||"/",n);if(a==null)return null;let i=qc(e);ip(i);let o=null;for(let u=0;o==null&&u<i.length;++u){let c=xp(a);o=vp(i[u],c,r)}return o}function qc(e,t=[],n=[],r=""){let s=(a,i,o)=>{let u={relativePath:o===void 0?a.path||"":o,caseSensitive:a.caseSensitive===!0,childrenIndex:i,route:a};u.relativePath.startsWith("/")&&(X(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let c=ut([r,u.relativePath]),f=n.concat(u);a.children&&a.children.length>0&&(X(a.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),qc(a.children,t,f,c)),!(a.path==null&&!a.index)&&t.push({path:c,score:pp(c,a.index),routesMeta:f})};return e.forEach((a,i)=>{var o;if(a.path===""||!((o=a.path)!=null&&o.includes("?")))s(a,i);else for(let u of ed(a.path))s(a,i,u)}),t}function ed(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),a=n.replace(/\?$/,"");if(r.length===0)return s?[a,""]:[a];let i=ed(r.join("/")),o=[];return o.push(...i.map(u=>u===""?a:[a,u].join("/"))),s&&o.push(...i),o.map(u=>e.startsWith("/")&&u===""?"/":u)}function ip(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:mp(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var op=/^:[\w-]+$/,up=3,cp=2,dp=1,fp=10,hp=-2,Io=e=>e==="*";function pp(e,t){let n=e.split("/"),r=n.length;return n.some(Io)&&(r+=hp),t&&(r+=cp),n.filter(s=>!Io(s)).reduce((s,a)=>s+(op.test(a)?up:a===""?dp:fp),r)}function mp(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function vp(e,t,n=!1){let{routesMeta:r}=e,s={},a="/",i=[];for(let o=0;o<r.length;++o){let u=r[o],c=o===r.length-1,f=a==="/"?t:t.slice(a.length)||"/",v=Fs({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},f),h=u.route;if(!v&&c&&n&&!r[r.length-1].route.index&&(v=Fs({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},f)),!v)return null;Object.assign(s,v.params),i.push({params:s,pathname:ut([a,v.pathname]),pathnameBase:Np(ut([a,v.pathnameBase])),route:h}),v.pathnameBase!=="/"&&(a=ut([a,v.pathnameBase]))}return i}function Fs(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=gp(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let a=s[0],i=a.replace(/(.)\/+$/,"$1"),o=s.slice(1);return{params:r.reduce((c,{paramName:f,isOptional:v},h)=>{if(f==="*"){let N=o[h]||"";i=a.slice(0,a.length-N.length).replace(/(.)\/+$/,"$1")}const S=o[h];return v&&!S?c[f]=void 0:c[f]=(S||"").replace(/%2F/g,"/"),c},{}),pathname:a,pathnameBase:i,pattern:e}}function gp(e,t=!1,n=!0){Ke(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,u)=>(r.push({paramName:o,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function xp(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ke(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function pt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function yp(e,t="/"){let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?Fn(e):e;return{pathname:n?n.startsWith("/")?n:jp(n,t):t,search:Sp(r),hash:kp(s)}}function jp(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function Nl(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function wp(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function mi(e){let t=wp(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function vi(e,t,n,r=!1){let s;typeof e=="string"?s=Fn(e):(s={...e},X(!s.pathname||!s.pathname.includes("?"),Nl("?","pathname","search",s)),X(!s.pathname||!s.pathname.includes("#"),Nl("#","pathname","hash",s)),X(!s.search||!s.search.includes("#"),Nl("#","search","hash",s)));let a=e===""||s.pathname==="",i=a?"/":s.pathname,o;if(i==null)o=n;else{let v=t.length-1;if(!r&&i.startsWith("..")){let h=i.split("/");for(;h[0]==="..";)h.shift(),v-=1;s.pathname=h.join("/")}o=v>=0?t[v]:"/"}let u=yp(s,o),c=i&&i!=="/"&&i.endsWith("/"),f=(a||i===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||f)&&(u.pathname+="/"),u}var ut=e=>e.join("/").replace(/\/\/+/g,"/"),Np=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Sp=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,kp=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Cp(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var td=["POST","PUT","PATCH","DELETE"];new Set(td);var Ep=["GET",...td];new Set(Ep);var Rn=g.createContext(null);Rn.displayName="DataRouter";var Ks=g.createContext(null);Ks.displayName="DataRouterState";var nd=g.createContext({isTransitioning:!1});nd.displayName="ViewTransition";var _p=g.createContext(new Map);_p.displayName="Fetchers";var Pp=g.createContext(null);Pp.displayName="Await";var Ge=g.createContext(null);Ge.displayName="Navigation";var Cr=g.createContext(null);Cr.displayName="Location";var nt=g.createContext({outlet:null,matches:[],isDataRoute:!1});nt.displayName="Route";var gi=g.createContext(null);gi.displayName="RouteError";function Lp(e,{relative:t}={}){X(Tn(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=g.useContext(Ge),{hash:s,pathname:a,search:i}=Er(e,{relative:t}),o=a;return n!=="/"&&(o=a==="/"?n:ut([n,a])),r.createHref({pathname:o,search:i,hash:s})}function Tn(){return g.useContext(Cr)!=null}function $t(){return X(Tn(),"useLocation() may be used only in the context of a <Router> component."),g.useContext(Cr).location}var rd="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function sd(e){g.useContext(Ge).static||g.useLayoutEffect(e)}function ld(){let{isDataRoute:e}=g.useContext(nt);return e?Hp():Fp()}function Fp(){X(Tn(),"useNavigate() may be used only in the context of a <Router> component.");let e=g.useContext(Rn),{basename:t,navigator:n}=g.useContext(Ge),{matches:r}=g.useContext(nt),{pathname:s}=$t(),a=JSON.stringify(mi(r)),i=g.useRef(!1);return sd(()=>{i.current=!0}),g.useCallback((u,c={})=>{if(Ke(i.current,rd),!i.current)return;if(typeof u=="number"){n.go(u);return}let f=vi(u,JSON.parse(a),s,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:ut([t,f.pathname])),(c.replace?n.replace:n.push)(f,c.state,c)},[t,n,a,s,e])}g.createContext(null);function Er(e,{relative:t}={}){let{matches:n}=g.useContext(nt),{pathname:r}=$t(),s=JSON.stringify(mi(n));return g.useMemo(()=>vi(e,JSON.parse(s),r,t==="path"),[e,s,r,t])}function Rp(e,t){return ad(e,t)}function ad(e,t,n,r){var p;X(Tn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=g.useContext(Ge),{matches:a}=g.useContext(nt),i=a[a.length-1],o=i?i.params:{},u=i?i.pathname:"/",c=i?i.pathnameBase:"/",f=i&&i.route;{let d=f&&f.path||"";id(u,!f||d.endsWith("*")||d.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${d}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${d}"> to <Route path="${d==="/"?"*":`${d}/*`}">.`)}let v=$t(),h;if(t){let d=typeof t=="string"?Fn(t):t;X(c==="/"||((p=d.pathname)==null?void 0:p.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${d.pathname}" was given in the \`location\` prop.`),h=d}else h=v;let S=h.pathname||"/",N=S;if(c!=="/"){let d=c.replace(/^\//,"").split("/");N="/"+S.replace(/^\//,"").split("/").slice(d.length).join("/")}let _=Zc(e,{pathname:N});Ke(f||_!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),Ke(_==null||_[_.length-1].route.element!==void 0||_[_.length-1].route.Component!==void 0||_[_.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=$p(_&&_.map(d=>Object.assign({},d,{params:Object.assign({},o,d.params),pathname:ut([c,s.encodeLocation?s.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?c:ut([c,s.encodeLocation?s.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),a,n,r);return t&&L?g.createElement(Cr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:"POP"}},L):L}function Tp(){let e=Ap(),t=Cp(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:r},a={padding:"2px 4px",backgroundColor:r},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=g.createElement(g.Fragment,null,g.createElement("p",null,"💿 Hey developer 👋"),g.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",g.createElement("code",{style:a},"ErrorBoundary")," or"," ",g.createElement("code",{style:a},"errorElement")," prop on your route.")),g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),n?g.createElement("pre",{style:s},n):null,i)}var Dp=g.createElement(Tp,null),zp=class extends g.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?g.createElement(nt.Provider,{value:this.props.routeContext},g.createElement(gi.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Mp({routeContext:e,match:t,children:n}){let r=g.useContext(Rn);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),g.createElement(nt.Provider,{value:e},n)}function $p(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=n==null?void 0:n.errors;if(a!=null){let u=s.findIndex(c=>c.route.id&&(a==null?void 0:a[c.route.id])!==void 0);X(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(a).join(",")}`),s=s.slice(0,Math.min(s.length,u+1))}let i=!1,o=-1;if(n)for(let u=0;u<s.length;u++){let c=s[u];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(o=u),c.route.id){let{loaderData:f,errors:v}=n,h=c.route.loader&&!f.hasOwnProperty(c.route.id)&&(!v||v[c.route.id]===void 0);if(c.route.lazy||h){i=!0,o>=0?s=s.slice(0,o+1):s=[s[0]];break}}}return s.reduceRight((u,c,f)=>{let v,h=!1,S=null,N=null;n&&(v=a&&c.route.id?a[c.route.id]:void 0,S=c.route.errorElement||Dp,i&&(o<0&&f===0?(id("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,N=null):o===f&&(h=!0,N=c.route.hydrateFallbackElement||null)));let _=t.concat(s.slice(0,f+1)),L=()=>{let p;return v?p=S:h?p=N:c.route.Component?p=g.createElement(c.route.Component,null):c.route.element?p=c.route.element:p=u,g.createElement(Mp,{match:c,routeContext:{outlet:u,matches:_,isDataRoute:n!=null},children:p})};return n&&(c.route.ErrorBoundary||c.route.errorElement||f===0)?g.createElement(zp,{location:n.location,revalidation:n.revalidation,component:S,error:v,children:L(),routeContext:{outlet:null,matches:_,isDataRoute:!0}}):L()},null)}function xi(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ip(e){let t=g.useContext(Rn);return X(t,xi(e)),t}function Op(e){let t=g.useContext(Ks);return X(t,xi(e)),t}function Up(e){let t=g.useContext(nt);return X(t,xi(e)),t}function yi(e){let t=Up(e),n=t.matches[t.matches.length-1];return X(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function Bp(){return yi("useRouteId")}function Ap(){var r;let e=g.useContext(gi),t=Op("useRouteError"),n=yi("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function Hp(){let{router:e}=Ip("useNavigate"),t=yi("useNavigate"),n=g.useRef(!1);return sd(()=>{n.current=!0}),g.useCallback(async(s,a={})=>{Ke(n.current,rd),n.current&&(typeof s=="number"?e.navigate(s):await e.navigate(s,{fromRouteId:t,...a}))},[e,t])}var Oo={};function id(e,t,n){!t&&!Oo[e]&&(Oo[e]=!0,Ke(!1,n))}g.memo(Wp);function Wp({routes:e,future:t,state:n}){return ad(e,void 0,n,t)}function Vp({to:e,replace:t,state:n,relative:r}){X(Tn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:s}=g.useContext(Ge);Ke(!s,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:a}=g.useContext(nt),{pathname:i}=$t(),o=ld(),u=vi(e,mi(a),i,r==="path"),c=JSON.stringify(u);return g.useEffect(()=>{o(JSON.parse(c),{replace:t,state:n,relative:r})},[o,c,r,t,n]),null}function Ut(e){X(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function bp({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:s,static:a=!1}){X(!Tn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),o=g.useMemo(()=>({basename:i,navigator:s,static:a,future:{}}),[i,s,a]);typeof n=="string"&&(n=Fn(n));let{pathname:u="/",search:c="",hash:f="",state:v=null,key:h="default"}=n,S=g.useMemo(()=>{let N=pt(u,i);return N==null?null:{location:{pathname:N,search:c,hash:f,state:v,key:h},navigationType:r}},[i,u,c,f,v,h,r]);return Ke(S!=null,`<Router basename="${i}"> is not able to match the URL "${u}${c}${f}" because it does not start with the basename, so the <Router> won't render anything.`),S==null?null:g.createElement(Ge.Provider,{value:o},g.createElement(Cr.Provider,{children:t,value:S}))}function Qp({children:e,location:t}){return Rp(xa(e),t)}function xa(e,t=[]){let n=[];return g.Children.forEach(e,(r,s)=>{if(!g.isValidElement(r))return;let a=[...t,s];if(r.type===g.Fragment){n.push.apply(n,xa(r.props.children,a));return}X(r.type===Ut,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),X(!r.props.index||!r.props.children,"An index route cannot have child routes.");let i={id:r.props.id||a.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=xa(r.props.children,a)),n.push(i)}),n}var ss="get",ls="application/x-www-form-urlencoded";function Gs(e){return e!=null&&typeof e.tagName=="string"}function Kp(e){return Gs(e)&&e.tagName.toLowerCase()==="button"}function Gp(e){return Gs(e)&&e.tagName.toLowerCase()==="form"}function Yp(e){return Gs(e)&&e.tagName.toLowerCase()==="input"}function Xp(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Jp(e,t){return e.button===0&&(!t||t==="_self")&&!Xp(e)}var br=null;function Zp(){if(br===null)try{new FormData(document.createElement("form"),0),br=!1}catch{br=!0}return br}var qp=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Sl(e){return e!=null&&!qp.has(e)?(Ke(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ls}"`),null):e}function em(e,t){let n,r,s,a,i;if(Gp(e)){let o=e.getAttribute("action");r=o?pt(o,t):null,n=e.getAttribute("method")||ss,s=Sl(e.getAttribute("enctype"))||ls,a=new FormData(e)}else if(Kp(e)||Yp(e)&&(e.type==="submit"||e.type==="image")){let o=e.form;if(o==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||o.getAttribute("action");if(r=u?pt(u,t):null,n=e.getAttribute("formmethod")||o.getAttribute("method")||ss,s=Sl(e.getAttribute("formenctype"))||Sl(o.getAttribute("enctype"))||ls,a=new FormData(o,e),!Zp()){let{name:c,type:f,value:v}=e;if(f==="image"){let h=c?`${c}.`:"";a.append(`${h}x`,"0"),a.append(`${h}y`,"0")}else c&&a.append(c,v)}}else{if(Gs(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ss,r=null,s=ls,i=e}return a&&s==="text/plain"&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:s,formData:a,body:i}}function ji(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function tm(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function nm(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function rm(e,t,n){let r=await Promise.all(e.map(async s=>{let a=t.routes[s.route.id];if(a){let i=await tm(a,n);return i.links?i.links():[]}return[]}));return im(r.flat(1).filter(nm).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function Uo(e,t,n,r,s,a){let i=(u,c)=>n[c]?u.route.id!==n[c].route.id:!0,o=(u,c)=>{var f;return n[c].pathname!==u.pathname||((f=n[c].route.path)==null?void 0:f.endsWith("*"))&&n[c].params["*"]!==u.params["*"]};return a==="assets"?t.filter((u,c)=>i(u,c)||o(u,c)):a==="data"?t.filter((u,c)=>{var v;let f=r.routes[u.route.id];if(!f||!f.hasLoader)return!1;if(i(u,c)||o(u,c))return!0;if(u.route.shouldRevalidate){let h=u.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:((v=n[0])==null?void 0:v.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function sm(e,t,{includeHydrateFallback:n}={}){return lm(e.map(r=>{let s=t.routes[r.route.id];if(!s)return[];let a=[s.module];return s.clientActionModule&&(a=a.concat(s.clientActionModule)),s.clientLoaderModule&&(a=a.concat(s.clientLoaderModule)),n&&s.hydrateFallbackModule&&(a=a.concat(s.hydrateFallbackModule)),s.imports&&(a=a.concat(s.imports)),a}).flat(1))}function lm(e){return[...new Set(e)]}function am(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function im(e,t){let n=new Set;return new Set(t),e.reduce((r,s)=>{let a=JSON.stringify(am(s));return n.has(a)||(n.add(a),r.push({key:a,link:s})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var om=new Set([100,101,204,205]);function um(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&pt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function od(){let e=g.useContext(Rn);return ji(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function cm(){let e=g.useContext(Ks);return ji(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var wi=g.createContext(void 0);wi.displayName="FrameworkContext";function ud(){let e=g.useContext(wi);return ji(e,"You must render this element inside a <HydratedRouter> element"),e}function dm(e,t){let n=g.useContext(wi),[r,s]=g.useState(!1),[a,i]=g.useState(!1),{onFocus:o,onBlur:u,onMouseEnter:c,onMouseLeave:f,onTouchStart:v}=t,h=g.useRef(null);g.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let _=p=>{p.forEach(d=>{i(d.isIntersecting)})},L=new IntersectionObserver(_,{threshold:.5});return h.current&&L.observe(h.current),()=>{L.disconnect()}}},[e]),g.useEffect(()=>{if(r){let _=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(_)}}},[r]);let S=()=>{s(!0)},N=()=>{s(!1),i(!1)};return n?e!=="intent"?[a,h,{}]:[a,h,{onFocus:Hn(o,S),onBlur:Hn(u,N),onMouseEnter:Hn(c,S),onMouseLeave:Hn(f,N),onTouchStart:Hn(v,S)}]:[!1,h,{}]}function Hn(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function fm({page:e,...t}){let{router:n}=od(),r=g.useMemo(()=>Zc(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?g.createElement(pm,{page:e,matches:r,...t}):null}function hm(e){let{manifest:t,routeModules:n}=ud(),[r,s]=g.useState([]);return g.useEffect(()=>{let a=!1;return rm(e,t,n).then(i=>{a||s(i)}),()=>{a=!0}},[e,t,n]),r}function pm({page:e,matches:t,...n}){let r=$t(),{manifest:s,routeModules:a}=ud(),{basename:i}=od(),{loaderData:o,matches:u}=cm(),c=g.useMemo(()=>Uo(e,t,u,s,r,"data"),[e,t,u,s,r]),f=g.useMemo(()=>Uo(e,t,u,s,r,"assets"),[e,t,u,s,r]),v=g.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let N=new Set,_=!1;if(t.forEach(p=>{var m;let d=s.routes[p.route.id];!d||!d.hasLoader||(!c.some(w=>w.route.id===p.route.id)&&p.route.id in o&&((m=a[p.route.id])!=null&&m.shouldRevalidate)||d.hasClientLoader?_=!0:N.add(p.route.id))}),N.size===0)return[];let L=um(e,i);return _&&N.size>0&&L.searchParams.set("_routes",t.filter(p=>N.has(p.route.id)).map(p=>p.route.id).join(",")),[L.pathname+L.search]},[i,o,r,s,c,t,e,a]),h=g.useMemo(()=>sm(f,s),[f,s]),S=hm(f);return g.createElement(g.Fragment,null,v.map(N=>g.createElement("link",{key:N,rel:"prefetch",as:"fetch",href:N,...n})),h.map(N=>g.createElement("link",{key:N,rel:"modulepreload",href:N,...n})),S.map(({key:N,link:_})=>g.createElement("link",{key:N,..._})))}function mm(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var cd=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{cd&&(window.__reactRouterVersion="7.6.3")}catch{}function vm({basename:e,children:t,window:n}){let r=g.useRef();r.current==null&&(r.current=np({window:n,v5Compat:!0}));let s=r.current,[a,i]=g.useState({action:s.action,location:s.location}),o=g.useCallback(u=>{g.startTransition(()=>i(u))},[i]);return g.useLayoutEffect(()=>s.listen(o),[s,o]),g.createElement(bp,{basename:e,children:t,location:a.location,navigationType:a.action,navigator:s})}var dd=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,fd=g.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:s,reloadDocument:a,replace:i,state:o,target:u,to:c,preventScrollReset:f,viewTransition:v,...h},S){let{basename:N}=g.useContext(Ge),_=typeof c=="string"&&dd.test(c),L,p=!1;if(typeof c=="string"&&_&&(L=c,cd))try{let C=new URL(window.location.href),y=c.startsWith("//")?new URL(C.protocol+c):new URL(c),k=pt(y.pathname,N);y.origin===C.origin&&k!=null?c=k+y.search+y.hash:p=!0}catch{Ke(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let d=Lp(c,{relative:s}),[m,w,x]=dm(r,h),E=ym(c,{replace:i,state:o,target:u,preventScrollReset:f,relative:s,viewTransition:v});function P(C){t&&t(C),C.defaultPrevented||E(C)}let j=g.createElement("a",{...h,...x,href:L||d,onClick:p||a?t:P,ref:mm(S,w),target:u,"data-discover":!_&&n==="render"?"true":void 0});return m&&!_?g.createElement(g.Fragment,null,j,g.createElement(fm,{page:d})):j});fd.displayName="Link";var ya=g.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:s=!1,style:a,to:i,viewTransition:o,children:u,...c},f){let v=Er(i,{relative:c.relative}),h=$t(),S=g.useContext(Ks),{navigator:N,basename:_}=g.useContext(Ge),L=S!=null&&km(v)&&o===!0,p=N.encodeLocation?N.encodeLocation(v).pathname:v.pathname,d=h.pathname,m=S&&S.navigation&&S.navigation.location?S.navigation.location.pathname:null;n||(d=d.toLowerCase(),m=m?m.toLowerCase():null,p=p.toLowerCase()),m&&_&&(m=pt(m,_)||m);const w=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let x=d===p||!s&&d.startsWith(p)&&d.charAt(w)==="/",E=m!=null&&(m===p||!s&&m.startsWith(p)&&m.charAt(p.length)==="/"),P={isActive:x,isPending:E,isTransitioning:L},j=x?t:void 0,C;typeof r=="function"?C=r(P):C=[r,x?"active":null,E?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let y=typeof a=="function"?a(P):a;return g.createElement(fd,{...c,"aria-current":j,className:C,ref:f,style:y,to:i,viewTransition:o},typeof u=="function"?u(P):u)});ya.displayName="NavLink";var gm=g.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:s,state:a,method:i=ss,action:o,onSubmit:u,relative:c,preventScrollReset:f,viewTransition:v,...h},S)=>{let N=Nm(),_=Sm(o,{relative:c}),L=i.toLowerCase()==="get"?"get":"post",p=typeof o=="string"&&dd.test(o),d=m=>{if(u&&u(m),m.defaultPrevented)return;m.preventDefault();let w=m.nativeEvent.submitter,x=(w==null?void 0:w.getAttribute("formmethod"))||i;N(w||m.currentTarget,{fetcherKey:t,method:x,navigate:n,replace:s,state:a,relative:c,preventScrollReset:f,viewTransition:v})};return g.createElement("form",{ref:S,method:L,action:_,onSubmit:r?u:d,...h,"data-discover":!p&&e==="render"?"true":void 0})});gm.displayName="Form";function xm(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function hd(e){let t=g.useContext(Rn);return X(t,xm(e)),t}function ym(e,{target:t,replace:n,state:r,preventScrollReset:s,relative:a,viewTransition:i}={}){let o=ld(),u=$t(),c=Er(e,{relative:a});return g.useCallback(f=>{if(Jp(f,t)){f.preventDefault();let v=n!==void 0?n:yr(u)===yr(c);o(e,{replace:v,state:r,preventScrollReset:s,relative:a,viewTransition:i})}},[u,o,c,n,r,t,e,s,a,i])}var jm=0,wm=()=>`__${String(++jm)}__`;function Nm(){let{router:e}=hd("useSubmit"),{basename:t}=g.useContext(Ge),n=Bp();return g.useCallback(async(r,s={})=>{let{action:a,method:i,encType:o,formData:u,body:c}=em(r,t);if(s.navigate===!1){let f=s.fetcherKey||wm();await e.fetch(f,n,s.action||a,{preventScrollReset:s.preventScrollReset,formData:u,body:c,formMethod:s.method||i,formEncType:s.encType||o,flushSync:s.flushSync})}else await e.navigate(s.action||a,{preventScrollReset:s.preventScrollReset,formData:u,body:c,formMethod:s.method||i,formEncType:s.encType||o,replace:s.replace,state:s.state,fromRouteId:n,flushSync:s.flushSync,viewTransition:s.viewTransition})},[e,t,n])}function Sm(e,{relative:t}={}){let{basename:n}=g.useContext(Ge),r=g.useContext(nt);X(r,"useFormAction must be used inside a RouteContext");let[s]=r.matches.slice(-1),a={...Er(e||".",{relative:t})},i=$t();if(e==null){a.search=i.search;let o=new URLSearchParams(a.search),u=o.getAll("index");if(u.some(f=>f==="")){o.delete("index"),u.filter(v=>v).forEach(v=>o.append("index",v));let f=o.toString();a.search=f?`?${f}`:""}}return(!e||e===".")&&s.route.index&&(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(a.pathname=a.pathname==="/"?n:ut([n,a.pathname])),yr(a)}function km(e,t={}){let n=g.useContext(nd);X(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=hd("useViewTransitionState"),s=Er(e,{relative:t.relative});if(!n.isTransitioning)return!1;let a=pt(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=pt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Fs(s.pathname,i)!=null||Fs(s.pathname,a)!=null}[...om];function Cm(){const[e,t]=g.useState(!1),n=[{path:"/google-workspace-scanner",label:"1. Google Workspace Scanner",icon:"📁",description:"Thu thập thông tin người dùng và thực hiện scan files từ Google Workspace"},{path:"/migration",label:"Migration Tool",icon:"🚀",description:"Công cụ migration file từ Drive sang Lark"},{path:"/download",label:"Download Files",icon:"📥",description:"Download files từ Google Drive về local"},{path:"/storage",label:"Storage Comparison",icon:"📊",description:"So sánh dung lượng đã download vs Google Drive"},{path:"/users",label:"Users Overview",icon:"👥",description:"Xem tổng quan users và file structure"}],r=()=>{t(!e)};return l.jsxs("nav",{className:"navigation",children:[l.jsxs("div",{className:"nav-container",children:[l.jsxs("div",{className:"nav-brand",children:[l.jsx("div",{className:"brand-icon",children:"🏢"}),l.jsxs("div",{className:"brand-text",children:[l.jsx("div",{className:"brand-title",children:"Drive to Lark"}),l.jsx("div",{className:"brand-subtitle",children:"Migration Platform"})]})]}),l.jsx("div",{className:"nav-links",children:n.map(s=>l.jsxs(ya,{to:s.path,className:({isActive:a})=>`nav-link ${a?"active":""}`,title:s.description,children:[l.jsx("span",{className:"nav-icon",children:s.icon}),l.jsx("span",{className:"nav-text",children:s.label})]},s.path))}),l.jsxs("div",{className:"nav-actions",children:[l.jsx("button",{className:"action-btn",title:"Thông báo",children:"🔔"}),l.jsx("button",{className:"action-btn",title:"Cài đặt",children:"⚙️"}),l.jsx("div",{className:"user-menu",children:l.jsx("button",{className:"user-avatar",title:"User menu",children:"👤"})})]}),l.jsx("button",{className:"mobile-menu-btn",onClick:r,"aria-label":"Toggle mobile menu",children:e?"✕":"☰"})]}),e&&l.jsx("div",{className:"mobile-menu",children:l.jsxs("div",{className:"mobile-menu-content",children:[n.map(s=>l.jsxs(ya,{to:s.path,className:({isActive:a})=>`mobile-nav-link ${a?"active":""}`,onClick:()=>t(!1),children:[l.jsx("span",{className:"mobile-nav-icon",children:s.icon}),l.jsxs("div",{className:"mobile-nav-text",children:[l.jsx("div",{className:"mobile-nav-label",children:s.label}),l.jsx("div",{className:"mobile-nav-desc",children:s.description})]})]},s.path)),l.jsxs("div",{className:"mobile-actions",children:[l.jsx("button",{className:"mobile-action-btn",children:"🔔 Thông báo"}),l.jsx("button",{className:"mobile-action-btn",children:"⚙️ Cài đặt"})]})]})})]})}const Ni=async(e,t={})=>{try{const n=await fetch(e,{headers:{"Content-Type":"application/json",...t.headers},...t});if(!n.ok){let r;try{r=await n.json()}catch{r={message:n.statusText}}const s=Em(n.status,r.message),a=new Error(s);throw a.status=n.status,a.statusText=n.statusText,a.originalMessage=r.message,a.response={status:n.status,statusText:n.statusText,data:r},a}try{return await n.json()}catch{return await n.text()}}catch(n){throw n.status||(n.message=`Lỗi kết nối: ${n.message}`),n}},Em=(e,t)=>{const r={400:"Yêu cầu không hợp lệ",401:"Không có quyền truy cập",403:"Bị cấm truy cập",404:"Không tìm thấy tài nguyên",408:"Hết thời gian chờ",429:"Quá nhiều yêu cầu, vui lòng thử lại sau",500:"Lỗi máy chủ nội bộ",502:"Lỗi gateway",503:"Dịch vụ không khả dụng",504:"Hết thời gian chờ gateway"}[e]||`Lỗi HTTP ${e}`;return t&&t!=="Internal Server Error"&&t!=="Bad Request"?`${r}: ${t}`:r},oe=(e,t={})=>Ni(e,{method:"GET",...t}),Me=(e,t=null,n={})=>Ni(e,{method:"POST",body:t?JSON.stringify(t):null,...n}),_m=(e,t={})=>Ni(e,{method:"DELETE",...t}),Ee=e=>{var t,n,r;if(typeof e=="string")return{message:e,details:null,code:null};if(e instanceof Error){let s=[];return e.originalMessage&&e.originalMessage!==e.message&&s.push(`Thông báo gốc: ${e.originalMessage}`),e.status&&s.push(`Mã lỗi HTTP: ${e.status}`),(t=e.response)!=null&&t.data&&s.push(`Chi tiết API: ${JSON.stringify(e.response.data,null,2)}`),{message:e.message,details:s.length>0?s.join(`

`):null,code:e.status||e.code||null}}return e.response?{message:((n=e.response.data)==null?void 0:n.message)||e.response.statusText||"Lỗi API",details:((r=e.response.data)==null?void 0:r.details)||`HTTP ${e.response.status}`,code:e.response.status}:{message:e.message||"Lỗi không xác định",details:JSON.stringify(e,null,2),code:null}},Xt=({error:e,title:t="Đã xảy ra lỗi",onRetry:n=null,onDismiss:r=null,showDetails:s=!0,className:a=""})=>{if(!e)return null;const o=(()=>{var u,c;return typeof e=="string"?{message:e,details:null,code:null}:e instanceof Error?{message:e.message,details:e.stack,code:e.code||null}:e.response?{message:((u=e.response.data)==null?void 0:u.message)||e.response.statusText||"Lỗi API",details:((c=e.response.data)==null?void 0:c.details)||`HTTP ${e.response.status}`,code:e.response.status}:e.status?{message:e.message||"Lỗi kết nối",details:`HTTP ${e.status}: ${e.statusText}`,code:e.status}:{message:e.message||"Lỗi không xác định",details:JSON.stringify(e,null,2),code:null}})();return l.jsx("div",{className:`error-display ${a}`,children:l.jsxs("div",{className:"error-content",children:[l.jsxs("div",{className:"error-header",children:[l.jsx("div",{className:"error-icon",children:"❌"}),l.jsx("div",{className:"error-title",children:t}),r&&l.jsx("button",{onClick:r,className:"error-dismiss","aria-label":"Đóng",children:"✕"})]}),l.jsxs("div",{className:"error-body",children:[l.jsx("div",{className:"error-message",children:o.message}),o.code&&l.jsxs("div",{className:"error-code",children:["Mã lỗi: ",o.code]}),s&&o.details&&l.jsxs("details",{className:"error-details",children:[l.jsx("summary",{children:"Chi tiết lỗi"}),l.jsx("pre",{className:"error-details-content",children:o.details})]})]}),n&&l.jsx("div",{className:"error-actions",children:l.jsx("button",{onClick:n,className:"btn btn-primary btn-small",children:"🔄 Thử lại"})})]})})},Pm=({userEmail:e,onFolderSelected:t})=>{const[n,r]=g.useState("/"),[s,a]=g.useState("root"),[i,o]=g.useState([]),[u,c]=g.useState(!1),[f,v]=g.useState(null),[h,S]=g.useState([{id:"root",name:"My Drive",path:"/"}]);g.useEffect(()=>{e&&e!=="ALL_USERS"&&N(s)},[s,e]);const N=async m=>{if(!(!e||e==="ALL_USERS")){c(!0),v(null);try{const w=await oe(`/api/folders/list?userEmail=${encodeURIComponent(e)}&parentId=${m}`);o(w.folders||[])}catch(w){console.error("Error loading folders:",w),v(w),o([])}finally{c(!1)}}},_=async m=>{try{const x=(await oe(`/api/folders/resolve-id?userEmail=${encodeURIComponent(e)}&folderId=${m.id}`)).path||`${n}/${m.name}`.replace(/\/+/g,"/");a(m.id),r(x),S(E=>[...E,{id:m.id,name:m.name,path:x}])}catch(w){console.error("Error navigating to folder:",w),v(w)}},L=()=>{if(h.length>1){const m=h.slice(0,-1),w=m[m.length-1];S(m),a(w.id),r(w.path)}},p=m=>{if(m<h.length-1){const w=h.slice(0,m+1),x=w[w.length-1];S(w),a(x.id),r(x.path)}},d=()=>{const m=h[h.length-1];t({id:m.id,name:m.name,path:m.path})};return e?e==="ALL_USERS"?l.jsx("div",{className:"folder-browser",children:l.jsx("p",{className:"info-message",children:'📁 Folder browsing is not available when "All Users" is selected. The scan will include all accessible files from all users.'})}):l.jsxs("div",{className:"folder-browser",children:[l.jsxs("div",{className:"browser-header",children:[l.jsx("div",{className:"breadcrumb",children:h.map((m,w)=>l.jsxs("span",{className:"breadcrumb-item",children:[w>0&&l.jsx("span",{className:"breadcrumb-separator",children:"/"}),l.jsx("button",{onClick:()=>p(w),className:`breadcrumb-link ${w===h.length-1?"current":""}`,disabled:w===h.length-1,children:m.name})]},m.id))}),l.jsxs("div",{className:"browser-actions",children:[l.jsx("button",{onClick:L,disabled:h.length<=1,className:"btn btn-secondary btn-small",children:"⬆️ Up"}),l.jsx("button",{onClick:d,className:"btn btn-primary btn-small",disabled:s==="root",children:"✅ Select This Folder"})]})]}),l.jsxs("div",{className:"folder-list",children:[u&&l.jsxs("div",{className:"loading-state",children:[l.jsx("div",{className:"spinner"}),l.jsx("p",{children:"Loading folders..."})]}),f&&l.jsx(Xt,{error:f,title:"Lỗi tải danh sách thư mục",onRetry:()=>N(s),onDismiss:()=>v(null),className:"inline compact"}),!u&&!f&&i.length===0&&l.jsx("div",{className:"empty-state",children:l.jsx("p",{children:"📁 No folders found in this directory"})}),!u&&!f&&i.length>0&&l.jsx("div",{className:"folders-grid",children:i.map(m=>l.jsxs("div",{className:"folder-item",onClick:()=>_(m),children:[l.jsx("div",{className:"folder-icon",children:"📁"}),l.jsxs("div",{className:"folder-info",children:[l.jsx("div",{className:"folder-name",children:m.name}),l.jsx("div",{className:"folder-meta",children:m.modifiedTime&&l.jsxs("span",{className:"folder-date",children:["Modified: ",new Date(m.modifiedTime).toLocaleDateString()]})})]}),l.jsx("div",{className:"folder-arrow",children:"➡️"})]},m.id))})]}),l.jsx("div",{className:"browser-footer",children:l.jsxs("div",{className:"current-selection",children:[l.jsx("strong",{children:"Current folder:"})," ",n]})})]}):l.jsx("div",{className:"folder-browser",children:l.jsx("p",{className:"info-message",children:"Please select a user to browse folders"})})},Lm=({onScopeSelected:e,userEmail:t})=>{const[n,r]=g.useState("all"),[s,a]=g.useState(null),[i,o]=g.useState(10),[u,c]=g.useState(!0),[f,v]=g.useState({includeGoogleDocs:!0,includeImages:!0,includePDFs:!0,includeOfficeFiles:!0,includeOthers:!1}),h=d=>{r(d),d==="all"&&a(null)},S=d=>{a(d),r("folder")},N=(d,m)=>{v(w=>({...w,[d]:m}))},_=()=>{const d=[];return f.includeGoogleDocs&&d.push("application/vnd.google-apps.document","application/vnd.google-apps.spreadsheet","application/vnd.google-apps.presentation"),f.includeImages&&d.push("image/jpeg","image/png","image/gif","image/bmp"),f.includePDFs&&d.push("application/pdf"),f.includeOfficeFiles&&d.push("application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation"),d.push("application/vnd.google-apps.folder"),d},L=()=>{if(!t){alert("Please enter your email address");return}const d={maxDepth:i,includeSharedDrives:u,filterMimeTypes:_(),folderId:n==="folder"?s==null?void 0:s.id:null,folderPath:n==="folder"?s==null?void 0:s.path:null};e(n,d)},p=()=>!t||n==="folder"&&!s&&t!=="ALL_USERS"?!1:Object.values(f).some(d=>d);return l.jsxs("div",{className:"scope-selector",children:[l.jsx("h2",{children:"📁 Select Migration Scope"}),l.jsxs("div",{className:"scope-options",children:[l.jsx("div",{className:"scope-option",children:l.jsxs("label",{className:"radio-label",children:[l.jsx("input",{type:"radio",name:"scope",value:"all",checked:n==="all",onChange:d=>h(d.target.value)}),l.jsx("span",{className:"radio-custom"}),l.jsxs("div",{className:"option-content",children:[l.jsx("h3",{children:"🌐 Entire Drive"}),l.jsx("p",{children:"Scan and migrate all files from your Google Drive"})]})]})}),l.jsx("div",{className:"scope-option",children:l.jsxs("label",{className:"radio-label",children:[l.jsx("input",{type:"radio",name:"scope",value:"folder",checked:n==="folder",onChange:d=>h(d.target.value)}),l.jsx("span",{className:"radio-custom"}),l.jsxs("div",{className:"option-content",children:[l.jsx("h3",{children:"📂 Specific Folder"}),l.jsx("p",{children:"Choose a specific folder to migrate"})]})]})})]}),n==="folder"&&l.jsxs("div",{className:"folder-selection",children:[l.jsx("h3",{children:"Choose Folder"}),s?l.jsxs("div",{className:"selected-folder",children:[l.jsx("span",{className:"folder-icon",children:"📁"}),l.jsx("span",{className:"folder-path",children:s.path||s.name}),l.jsx("button",{onClick:()=>a(null),className:"btn btn-secondary btn-small",children:"Change"})]}):l.jsx(Pm,{userEmail:t,onFolderSelected:S})]}),l.jsxs("div",{className:"scan-options",children:[l.jsx("h3",{children:"⚙️ Scan Options"}),l.jsxs("div",{className:"option-group",children:[l.jsxs("label",{className:"option-label",children:["Maximum Folder Depth:",l.jsx("input",{type:"number",min:"1",max:"100",value:i,onChange:d=>o(parseInt(d.target.value)),className:"number-input"})]}),l.jsx("small",{children:"Limit how deep to scan nested folders (1-100)"})]}),l.jsxs("div",{className:"option-group",children:[l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:u,onChange:d=>c(d.target.checked)}),l.jsx("span",{className:"checkbox-custom"}),"Include Shared Drives"]}),l.jsx("small",{children:"Scan files from shared/team drives"})]})]}),l.jsxs("div",{className:"file-filters",children:[l.jsx("h3",{children:"📄 File Type Filters"}),l.jsxs("div",{className:"filter-grid",children:[l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:f.includeGoogleDocs,onChange:d=>N("includeGoogleDocs",d.target.checked)}),l.jsx("span",{className:"checkbox-custom"}),"Google Docs, Sheets, Slides"]}),l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:f.includeImages,onChange:d=>N("includeImages",d.target.checked)}),l.jsx("span",{className:"checkbox-custom"}),"Images (JPG, PNG, GIF)"]}),l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:f.includePDFs,onChange:d=>N("includePDFs",d.target.checked)}),l.jsx("span",{className:"checkbox-custom"}),"PDF Documents"]}),l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:f.includeOfficeFiles,onChange:d=>N("includeOfficeFiles",d.target.checked)}),l.jsx("span",{className:"checkbox-custom"}),"Office Files (Word, Excel, PowerPoint)"]}),l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:f.includeOthers,onChange:d=>N("includeOthers",d.target.checked)}),l.jsx("span",{className:"checkbox-custom"}),"Other File Types"]})]})]}),l.jsx("div",{className:"action-buttons",children:l.jsx("button",{onClick:L,disabled:!p(),className:"btn btn-primary btn-large",children:"🔍 Start Scanning"})}),!p()&&l.jsxs("div",{className:"validation-message",children:[!t&&l.jsx("p",{children:"⚠️ Please select a user"}),n==="folder"&&!s&&t!=="ALL_USERS"&&l.jsx("p",{children:"⚠️ Please select a folder"}),!Object.values(f).some(d=>d)&&l.jsx("p",{children:"⚠️ Please select at least one file type"})]})]})},pd=({tree:e,selectedFiles:t,onFileSelect:n,onSelectAll:r,onSelectAllToggle:s})=>{const[a,i]=g.useState(new Set),o=x=>{const E=new Set(a);E.has(x)?E.delete(x):E.add(x),i(E)},u=x=>t.some(E=>E.id===x),c=x=>{let E=[];return x.children&&x.children.forEach(P=>{P.type==="file"?E.push(P):P.type==="folder"&&(E=E.concat(c(P)))}),E},f=x=>{const E=c(x);if(E.length===0)return"none";const P=E.filter(j=>t.some(C=>C.id===j.id));return P.length===0?"none":P.length===E.length?"all":"some"},v=(x,E)=>{const P=c(x);if(E){const j=[...t];P.forEach(C=>{t.some(y=>y.id===C.id)||j.push(C)}),r(j)}else{const j=P.map(y=>y.id),C=t.filter(y=>!j.includes(y.id));r(C)}},h=(x,E)=>E==="folder"?"📁":x!=null&&x.startsWith("image/")?"🖼️":x!=null&&x.startsWith("video/")?"🎥":x!=null&&x.startsWith("audio/")?"🎵":x!=null&&x.includes("pdf")?"📄":x!=null&&x.includes("document")||x!=null&&x.includes("word")?"📝":x!=null&&x.includes("spreadsheet")||x!=null&&x.includes("excel")?"📊":x!=null&&x.includes("presentation")||x!=null&&x.includes("powerpoint")?"📈":x!=null&&x.includes("zip")||x!=null&&x.includes("archive")?"📦":"📄",S=x=>{if(!x)return"0 B";const E=["B","KB","MB","GB"],P=Math.floor(Math.log(x)/Math.log(1024));return Math.round(x/Math.pow(1024,P)*100)/100+" "+E[P]},N=x=>{switch(x){case"downloaded":return"✅";case"failed":return"❌";case"not_downloaded":case null:case void 0:return"⏳";default:return"⏳"}},_=x=>{switch(x){case"downloaded":return"Downloaded";case"failed":return"Failed";case"not_downloaded":case null:case void 0:return"Not Downloaded";default:return"Not Downloaded"}},L=(x,E=0)=>{const P=x.type==="folder",j=a.has(x.id),C=x.children&&x.children.length>0,y=!P&&u(x.id),k=P?f(x):null,D=P&&c(x).length>0;return l.jsxs("div",{className:"tree-node",children:[l.jsxs("div",{className:`tree-item ${y?"selected":""} ${k==="all"?"folder-all-selected":k==="some"?"folder-some-selected":""}`,style:{paddingLeft:`${E*20+10}px`},children:[P&&l.jsx("button",{className:`tree-toggle ${C?"":"empty"}`,onClick:()=>o(x.id),disabled:!C,children:C?j?"▼":"▶":"○"}),l.jsxs("label",{className:"tree-checkbox",children:[l.jsx("input",{type:"checkbox",checked:P?k==="all":y,ref:P?M=>{M&&(M.indeterminate=k==="some")}:null,onChange:M=>{P?v(x,M.target.checked):n(x,M.target.checked)},disabled:P&&!D}),l.jsx("span",{className:"checkbox-custom"})]}),l.jsx("span",{className:"tree-icon",children:h(x.mime_type,x.type)}),l.jsx("span",{className:"tree-name",title:x.name,children:x.name}),l.jsx("div",{className:"tree-info",children:P?l.jsxs("span",{className:"folder-stats",children:[x.fileCount>0&&`${x.fileCount} files`,x.fileCount>0&&x.folderCount>0&&", ",x.folderCount>0&&`${x.folderCount} folders`,x.totalSize>0&&` (${S(x.totalSize)})`,k==="some"&&l.jsx("span",{className:"selection-indicator",children:" - Partially Selected"}),k==="all"&&D&&l.jsx("span",{className:"selection-indicator",children:" - All Selected"})]}):l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"file-size",children:S(x.size)}),x.modified_time&&l.jsx("span",{className:"file-date",children:new Date(x.modified_time).toLocaleDateString()}),l.jsx("span",{className:`download-status ${x.download_status||"not_downloaded"}`,title:_(x.download_status),children:N(x.download_status)})]})})]}),P&&j&&C&&l.jsx("div",{className:"tree-children",children:x.children.sort((M,U)=>M.type!==U.type?M.type==="folder"?-1:1:M.name.toLowerCase().localeCompare(U.name.toLowerCase())).map(M=>L(M,E+1))})]},x.id)},p=x=>{let E=[];return x.forEach(P=>{P.type==="file"&&E.push(P),P.children&&(E=E.concat(p(P.children)))}),E},d=p(e),m=t.length,w=d.length;return l.jsxs("div",{className:"tree-view",children:[l.jsxs("div",{className:"tree-header",children:[l.jsx("div",{className:"tree-controls",children:l.jsxs("label",{className:"select-all-checkbox",children:[l.jsx("input",{type:"checkbox",checked:m===w&&w>0,onChange:x=>{const E=x.target.checked;r(E?d:[]),s&&s(E)}}),l.jsx("span",{className:"checkbox-custom"}),l.jsxs("span",{children:["Select All (",w," files)"]})]})}),l.jsx("div",{className:"tree-stats",children:l.jsxs("span",{children:["Selected: ",m," / ",w]})})]}),l.jsx("div",{className:"tree-content",children:e.sort((x,E)=>x.type!==E.type?x.type==="folder"?-1:1:x.name.toLowerCase().localeCompare(E.name.toLowerCase())).map(x=>L(x))})]})},md=({stats:e,selectedFiles:t})=>{const n=o=>{if(!o)return"0 B";const u=["B","KB","MB","GB","TB"],c=Math.floor(Math.log(o)/Math.log(1024));return Math.round(o/Math.pow(1024,c)*100)/100+" "+u[c]},r=()=>t.reduce((o,u)=>o+(u.size||0),0),s=()=>e!=null&&e.fileTypes?Object.entries(e.fileTypes).sort(([,o],[,u])=>u-o).slice(0,5).map(([o,u])=>({type:o.split("/").pop()||o,count:u,fullType:o})):[];if(!e)return l.jsx("div",{className:"statistics",children:l.jsxs("div",{className:"stats-loading",children:[l.jsx("div",{className:"spinner"}),l.jsx("p",{children:"Loading statistics..."})]})});const a=s(),i=r();return l.jsxs("div",{className:"statistics",children:[l.jsx("h3",{children:"📊 Scan Statistics"}),l.jsxs("div",{className:"stats-grid",children:[l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"📁"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-number",children:e.totalFolders.toLocaleString()}),l.jsx("div",{className:"stat-label",children:"Folders"})]})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"📄"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-number",children:e.totalFiles.toLocaleString()}),l.jsx("div",{className:"stat-label",children:"Files"})]})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"💾"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-number",children:n(e.totalSize)}),l.jsx("div",{className:"stat-label",children:"Total Size"})]})]}),l.jsxs("div",{className:"stat-card selected",children:[l.jsx("div",{className:"stat-icon",children:"✅"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-number",children:t.length.toLocaleString()}),l.jsx("div",{className:"stat-label",children:"Selected Files"})]})]}),l.jsxs("div",{className:"stat-card selected",children:[l.jsx("div",{className:"stat-icon",children:"📦"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-number",children:n(i)}),l.jsx("div",{className:"stat-label",children:"Selected Size"})]})]}),l.jsxs("div",{className:"stat-card selected",children:[l.jsx("div",{className:"stat-icon",children:"📈"}),l.jsxs("div",{className:"stat-content",children:[l.jsxs("div",{className:"stat-number",children:[e.totalFiles>0?Math.round(t.length/e.totalFiles*100):0,"%"]}),l.jsx("div",{className:"stat-label",children:"Selection Rate"})]})]})]}),a.length>0&&l.jsxs("div",{className:"file-types-section",children:[l.jsx("h4",{children:"📋 Top File Types"}),l.jsx("div",{className:"file-types-list",children:a.map(({type:o,count:u,fullType:c})=>l.jsxs("div",{className:"file-type-item",children:[l.jsxs("div",{className:"file-type-info",children:[l.jsx("span",{className:"file-type-name",title:c,children:o}),l.jsx("span",{className:"file-type-count",children:u.toLocaleString()})]}),l.jsx("div",{className:"file-type-bar",children:l.jsx("div",{className:"file-type-progress",style:{width:`${u/e.totalFiles*100}%`}})})]},c))})]})]})},Fm=({scanSession:e,onFilesSelected:t,selectedFiles:n,onProceedToMigration:r,onTreeDataLoaded:s,onSelectAllToggle:a})=>{const[i,o]=g.useState([]),[u,c]=g.useState(null),[f,v]=g.useState(!0),[h,S]=g.useState(null),[N,_]=g.useState("tree"),[L,p]=g.useState({search:"",mimeType:"all",downloadStatus:"all"});g.useEffect(()=>{e!=null&&e.id&&d()},[e,L]);const d=async()=>{if(!(e!=null&&e.id)){console.log("No scan session ID available");return}console.log("Loading tree data for session:",e.id),v(!0),S(null);try{const E=new URLSearchParams({sessionId:e.id,...L});console.log("Calling API:",`/api/scan/files/tree?${E}`);const P=await oe(`/api/scan/files/tree?${E}`);console.log("Tree data received:",P),o(P.tree||[]),c(P.stats||null),s&&s(P.tree||[])}catch(E){console.error("Error loading tree data:",E),S(E),o([]),c(null)}finally{v(!1)}},m=(E,P)=>{let j;P?j=[...n,E]:j=n.filter(C=>C.id!==E.id),t(j)},w=E=>{t(E)},x=(E,P)=>{p(j=>({...j,[E]:P}))};return f?l.jsx("div",{className:"file-list",children:l.jsxs("div",{className:"loading-state",children:[l.jsx("div",{className:"spinner"}),l.jsx("p",{children:"Loading files..."})]})}):h?l.jsx("div",{className:"file-list",children:l.jsx(Xt,{error:h,title:"Lỗi tải danh sách file",onRetry:d,onDismiss:()=>S(null)})}):l.jsxs("div",{className:"file-list",children:[l.jsx("h2",{children:"📋 Select Files to Migrate"}),l.jsx(md,{stats:u,selectedFiles:n}),l.jsx("div",{className:"file-filters",children:l.jsxs("div",{className:"filter-row",children:[l.jsx("input",{type:"text",placeholder:"Search files...",value:L.search,onChange:E=>x("search",E.target.value),className:"search-input"}),l.jsxs("select",{value:L.mimeType,onChange:E=>x("mimeType",E.target.value),className:"filter-select",children:[l.jsx("option",{value:"all",children:"All File Types"}),l.jsx("option",{value:"application/vnd.google-apps.document",children:"Google Docs"}),l.jsx("option",{value:"application/vnd.google-apps.spreadsheet",children:"Google Sheets"}),l.jsx("option",{value:"application/vnd.google-apps.presentation",children:"Google Slides"}),l.jsx("option",{value:"application/pdf",children:"PDF Files"}),l.jsx("option",{value:"image/",children:"Images"}),l.jsx("option",{value:"application/vnd.google-apps.folder",children:"Folders"})]}),l.jsxs("select",{value:L.downloadStatus,onChange:E=>x("downloadStatus",E.target.value),className:"filter-select",children:[l.jsx("option",{value:"all",children:"All Download Status"}),l.jsx("option",{value:"not_downloaded",children:"Not Downloaded"}),l.jsx("option",{value:"downloaded",children:"Downloaded"}),l.jsx("option",{value:"failed",children:"Failed"})]})]})}),l.jsx(pd,{tree:i,selectedFiles:n,onFileSelect:m,onSelectAll:w,onSelectAllToggle:a}),l.jsx("div",{className:"action-buttons",children:l.jsxs("button",{onClick:r,disabled:n.length===0,className:"btn btn-primary btn-large",children:["Continue with ",n.length," Selected Files"]})})]})},Rm=({scanSession:e,onCancel:t})=>{const[n,r]=g.useState(0),[s,a]=g.useState(null),[i,o]=g.useState(null);g.useEffect(()=>{e&&u()},[e]);const u=()=>{if(!e)return;const h=e.scanned_files||0,S=e.total_files||0;if(S>0?r(h/S*100):r(0),e.started_at&&h>0){const N=new Date(e.started_at),L=new Date-N,p=h/L;if(S>h&&p>0){const m=(S-h)/p;a(m)}}},c=h=>{if(!h)return"Calculating...";const S=Math.floor(h/1e3),N=Math.floor(S/60),_=Math.floor(N/60);return _>0?`${_}h ${N%60}m`:N>0?`${N}m ${S%60}s`:`${S}s`},f=h=>{if(!h)return"0 B";const S=["B","KB","MB","GB","TB"];let N=h,_=0;for(;N>=1024&&_<S.length-1;)N/=1024,_++;return`${N.toFixed(1)} ${S[_]}`},v=async()=>{if(o(null),e!=null&&e.id)try{await Me(`/api/scan/cancel/${e.id}`)}catch(h){console.error("Error cancelling scan:",h),o(h);return}t()};return e?l.jsxs("div",{className:"scan-progress",children:[l.jsx("h2",{children:"🔍 Scanning Your Drive"}),l.jsxs("div",{className:"progress-overview",children:[l.jsxs("div",{className:"progress-bar-container",children:[l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:"progress-fill",style:{width:`${Math.min(n,100)}%`}})}),l.jsx("div",{className:"progress-text",children:e.status==="completed"?"100%":`${Math.round(n)}%`})]}),l.jsxs("div",{className:"scan-stats",children:[l.jsxs("div",{className:"stat-item",children:[l.jsx("div",{className:"stat-value",children:e.scanned_files||0}),l.jsx("div",{className:"stat-label",children:"Files Scanned"})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("div",{className:"stat-value",children:e.folders_processed||0}),l.jsx("div",{className:"stat-label",children:"Folders Processed"})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("div",{className:"stat-value",children:e.current_depth||0}),l.jsx("div",{className:"stat-label",children:"Current Depth"})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("div",{className:"stat-value",children:f(e.total_size||0)}),l.jsx("div",{className:"stat-label",children:"Total Size"})]})]})]}),l.jsxs("div",{className:"scan-details",children:[l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Status:"}),l.jsxs("span",{className:`detail-value status-${e.status}`,children:[e.status==="running"&&"🔄 Scanning...",e.status==="completed"&&"✅ Completed",e.status==="failed"&&"❌ Failed",e.status==="cancelled"&&"🛑 Cancelled"]})]}),l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Started:"}),l.jsx("span",{className:"detail-value",children:e.started_at?new Date(e.started_at).toLocaleString():"N/A"})]}),e.status==="running"&&s&&l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Estimated Time Remaining:"}),l.jsx("span",{className:"detail-value",children:c(s)})]}),e.scan_duration&&l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Duration:"}),l.jsx("span",{className:"detail-value",children:c(e.scan_duration)})]}),e.error_message&&l.jsxs("div",{className:"detail-row error",children:[l.jsx("span",{className:"detail-label",children:"Error:"}),l.jsx("span",{className:"detail-value",children:e.error_message})]})]}),i&&l.jsx(Xt,{error:i,title:"Lỗi hủy quét",onDismiss:()=>o(null),onRetry:v,className:"inline compact"}),e.status==="running"&&l.jsx("div",{className:"scan-actions",children:l.jsx("button",{onClick:v,className:"btn btn-secondary",children:"🛑 Hủy quét"})}),e.status==="completed"&&l.jsx("div",{className:"scan-summary",children:l.jsxs("div",{className:"summary-card",children:[l.jsx("h3",{children:"✅ Scan Completed Successfully!"}),l.jsxs("p",{children:["Found ",l.jsx("strong",{children:e.total_files})," files totaling ",l.jsx("strong",{children:f(e.total_size)})]}),l.jsxs("p",{children:["Scanned ",l.jsx("strong",{children:e.folders_processed})," folders with maximum depth of ",l.jsx("strong",{children:e.current_depth})," levels"]})]})}),e.status==="failed"&&l.jsx(Xt,{error:new Error(e.error_message||"Đã xảy ra lỗi không xác định trong quá trình quét."),title:"Quét thất bại",onRetry:t,showDetails:!0})]}):l.jsx("div",{className:"scan-progress",children:l.jsxs("div",{className:"loading-state",children:[l.jsx("div",{className:"spinner"}),l.jsx("p",{children:"Initializing scan..."})]})})},Tm=({value:e,onChange:t,className:n="",disabled:r=!1})=>{const[s,a]=g.useState([]),[i,o]=g.useState(!1),[u,c]=g.useState(!1),[f,v]=g.useState(""),[h,S]=g.useState(null),N=g.useRef(null),_=g.useRef(null);g.useEffect(()=>{L()},[]),g.useEffect(()=>{const C=y=>{N.current&&!N.current.contains(y.target)&&c(!1)};return document.addEventListener("mousedown",C),()=>{document.removeEventListener("mousedown",C)}},[]);const L=async()=>{o(!0),S(null);try{const y=(await oe("/api/scan/users")).users||[],k=[{id:"ALL_USERS",email:"ALL_USERS",fullName:"Tất cả users",isAllOption:!0},...y.map(D=>({id:D.userId||D.id,email:D.email,fullName:D.fullName||D.email,givenName:D.givenName,familyName:D.familyName,lastLoginTime:D.lastLoginTime,suspended:D.suspended}))];a(k)}catch(C){console.error("Error loading users:",C),S("Không thể tải danh sách users")}finally{o(!1)}},p=s.filter(C=>{var k,D,M,U;if(!f)return!0;const y=f.toLowerCase();return((k=C.email)==null?void 0:k.toLowerCase().includes(y))||((D=C.fullName)==null?void 0:D.toLowerCase().includes(y))||((M=C.givenName)==null?void 0:M.toLowerCase().includes(y))||((U=C.familyName)==null?void 0:U.toLowerCase().includes(y))}),d=s.find(C=>C.email===e),m=d?d.isAllOption?d.fullName:d.email:e||"",w=C=>{t(C.email),c(!1),v("")},x=()=>{r||c(!u)},E=()=>{r||c(!0)},P=C=>{v(C.target.value),c(!0)},j=C=>{C.key==="Escape"&&(c(!1),v(""))};return l.jsxs("div",{className:`user-selector ${n}`,ref:N,children:[l.jsxs("div",{className:"user-selector-input-container",children:[l.jsx("input",{ref:_,type:"text",className:"user-selector-input",placeholder:i?"Đang tải users...":"Chọn user...",value:u?f:m,onChange:P,onClick:x,onFocus:E,onKeyDown:j,disabled:r||i,autoComplete:"off"}),l.jsx("div",{className:`user-selector-arrow ${u?"open":""}`,onClick:x,children:"▼"})]}),u&&l.jsxs("div",{className:"user-selector-dropdown",children:[i&&l.jsxs("div",{className:"user-selector-loading",children:[l.jsx("div",{className:"spinner"}),"Đang tải users..."]}),h&&l.jsxs("div",{className:"user-selector-error",children:[h,l.jsx("button",{className:"retry-btn",onClick:L,type:"button",children:"Thử lại"})]}),!i&&!h&&p.length===0&&l.jsx("div",{className:"user-selector-no-results",children:"Không tìm thấy user nào"}),!i&&!h&&p.length>0&&l.jsx("div",{className:"user-selector-list",children:p.map(C=>l.jsx("div",{className:`user-selector-item ${C.isAllOption?"all-users-option":""} ${C.email===e?"selected":""} ${C.suspended?"suspended":""}`,onClick:()=>w(C),children:l.jsxs("div",{className:"user-info",children:[l.jsx("div",{className:"user-email",children:C.isAllOption?l.jsxs("span",{className:"all-users-label",children:["🌐 ",C.fullName]}):l.jsxs(l.Fragment,{children:[C.email,C.suspended&&l.jsx("span",{className:"suspended-badge",children:"Suspended"})]})}),!C.isAllOption&&C.fullName&&C.fullName!==C.email&&l.jsx("div",{className:"user-name",children:C.fullName}),!C.isAllOption&&C.lastLoginTime&&l.jsxs("div",{className:"user-last-login",children:["Đăng nhập cuối: ",new Date(C.lastLoginTime).toLocaleString("vi-VN")]})]})},C.id))})]})]})},Dm=({message:e,type:t="error",duration:n=5e3,onClose:r,showDetails:s=!1,details:a=null})=>{const[i,o]=g.useState(!0),[u,c]=g.useState(!1);g.useEffect(()=>{if(n>0){const S=setTimeout(()=>{f()},n);return()=>clearTimeout(S)}},[n]);const f=()=>{o(!1),setTimeout(()=>{r&&r()},300)},v=()=>{switch(t){case"success":return"✅";case"warning":return"⚠️";case"info":return"ℹ️";case"error":default:return"❌"}},h=()=>{switch(t){case"success":return"toast-success";case"warning":return"toast-warning";case"info":return"toast-info";case"error":default:return"toast-error"}};return i?l.jsx("div",{className:`toast ${h()} ${i?"toast-visible":"toast-hidden"}`,children:l.jsxs("div",{className:"toast-content",children:[l.jsxs("div",{className:"toast-header",children:[l.jsx("span",{className:"toast-icon",children:v()}),l.jsx("span",{className:"toast-message",children:e}),l.jsxs("div",{className:"toast-actions",children:[s&&a&&l.jsx("button",{onClick:()=>c(!u),className:"toast-details-btn","aria-label":u?"Ẩn chi tiết":"Hiện chi tiết",children:u?"▼":"▶"}),l.jsx("button",{onClick:f,className:"toast-close-btn","aria-label":"Đóng",children:"✕"})]})]}),u&&a&&l.jsx("div",{className:"toast-details",children:l.jsx("pre",{className:"toast-details-content",children:a})})]})}):null},Ys=({toasts:e,onRemoveToast:t})=>l.jsx("div",{className:"toast-container",children:e.map(n=>l.jsx(Dm,{message:n.message,type:n.type,duration:n.duration,showDetails:n.showDetails,details:n.details,onClose:()=>t(n.id)},n.id))}),_r=()=>{const[e,t]=g.useState([]),n=(c,f="error",v={})=>{const h=Date.now()+Math.random(),S={id:h,message:c,type:f,duration:v.duration||5e3,showDetails:v.showDetails||!1,details:v.details||null,...v};return t(N=>[...N,S]),h};return{toasts:e,addToast:n,removeToast:c=>{t(f=>f.filter(v=>v.id!==c))},clearToasts:()=>{t([])},showError:(c,f={})=>n(c,"error",f),showSuccess:(c,f={})=>n(c,"success",f),showWarning:(c,f={})=>n(c,"warning",f),showInfo:(c,f={})=>n(c,"info",f)}};function zm(){const[e,t]=g.useState("scope"),[n,r]=g.useState(null),[s,a]=g.useState([]),[i,o]=g.useState("ALL_USERS"),[u,c]=g.useState(null),[f,v]=g.useState(!1),{toasts:h,removeToast:S,showError:N,showSuccess:_,showWarning:L}=_r(),p=async(E,P)=>{console.log("Scope selected:",E,P),c(null),v(!0),t("scanning");try{const j=await Me("/api/scan/start",{userEmail:i,scope:E,...P});r(j),_("Bắt đầu quét thành công!"),d(j.sessionId)}catch(j){console.error("Error starting scan:",j);const C=Ee(j);c(j),N(`Lỗi bắt đầu quét: ${C.message}`,{showDetails:!0,details:C.details,duration:8e3}),t("scope")}finally{v(!1)}},d=async E=>{const P=setInterval(async()=>{try{const j=await oe(`/api/scan/status/${E}`);if(r(j),j.status==="completed")clearInterval(P),_(`Quét hoàn thành! Tìm thấy ${j.total_files||0} file.`),t("files");else if(j.status==="failed"){clearInterval(P);const C=j.error_message||"Quét thất bại";c(new Error(C)),N(`Quét thất bại: ${C}`,{duration:8e3}),t("scope")}}catch(j){console.error("Error polling scan progress:",j),clearInterval(P);const C=Ee(j);c(j),N(`Lỗi kiểm tra tiến trình quét: ${C.message}`,{showDetails:!0,details:C.details,duration:8e3}),t("scope")}},2e3)},m=E=>{a(E)},w=()=>{s.length>0&&t("migration")},x=async()=>{c(null),v(!0);try{const E=await Me("/api/migration/start",{userEmail:i,scanSessionId:n.id,selectedFiles:s.map(P=>P.id)});console.log("Migration started:",E),_(`Bắt đầu migration ${s.length} file thành công!`)}catch(E){console.error("Error starting migration:",E);const P=Ee(E);c(E),N(`Lỗi bắt đầu migration: ${P.message}`,{showDetails:!0,details:P.details,duration:8e3})}finally{v(!1)}};return l.jsxs("div",{className:"app",children:[l.jsxs("header",{className:"app-header",children:[l.jsx("h1",{children:"🚀 Drive-to-Lark Migrator"}),l.jsx("div",{className:"user-info",children:l.jsx(Tm,{value:i,onChange:E=>o(E),className:"user-email-selector",disabled:f})})]}),l.jsxs("main",{className:"app-main",children:[l.jsxs("div",{className:"step-indicator",children:[l.jsx("div",{className:`step ${e==="scope"?"active":e!=="scope"?"completed":""}`,children:"1. Select Scope"}),l.jsx("div",{className:`step ${e==="scanning"?"active":""}`,children:"2. Scanning"}),l.jsx("div",{className:`step ${e==="files"?"active":""}`,children:"3. Select Files"}),l.jsx("div",{className:`step ${e==="migration"?"active":""}`,children:"4. Migration"})]}),!1,u&&l.jsx(Xt,{error:u,title:"Lỗi trong quá trình xử lý",onDismiss:()=>c(null),onRetry:()=>{c(null)}}),l.jsxs("div",{className:"step-content",children:[e==="scope"&&l.jsx(Lm,{onScopeSelected:p,userEmail:i,loading:f}),e==="scanning"&&l.jsx(Rm,{scanSession:n,onCancel:()=>t("scope")}),e==="files"&&l.jsx(Fm,{scanSession:n,onFilesSelected:m,selectedFiles:s,onProceedToMigration:w}),e==="migration"&&l.jsxs("div",{className:"migration-step",children:[l.jsx("h2",{children:"Ready to Migrate"}),l.jsxs("p",{children:["Selected ",s.length," files for migration"]}),l.jsx("button",{onClick:x,className:"btn btn-primary",disabled:s.length===0,children:"Start Migration"})]})]})]}),l.jsx(Ys,{toasts:h,onRemoveToast:S})]})}const Mm=({users:e,onSubmit:t,loading:n,initialData:r})=>{const[s,a]=g.useState({name:"",selectedUsers:[],downloadPath:"",concurrentDownloads:3,maxRetries:3,stopOnError:!0,continueOnError:!1,skipMimeTypes:"",processingOrder:"created_at"}),[i,o]=g.useState(!1),[u,c]=g.useState(""),[f,v]=g.useState("name"),[h,S]=g.useState({}),[N,_]=g.useState({});g.useEffect(()=>{r&&a({name:r.name||"",selectedUsers:r.selected_users||[],downloadPath:r.download_path||"",concurrentDownloads:r.concurrent_downloads||3,maxRetries:r.max_retries||3,stopOnError:r.stop_on_error!==void 0?r.stop_on_error:!0,continueOnError:r.continue_on_error!==void 0?r.continue_on_error:!1,skipMimeTypes:(r.skip_mime_types||[]).join(", "),processingOrder:r.processing_order||"created_at"})},[r]);const L=e.filter(j=>{const C=u.toLowerCase();return j.primary_email.toLowerCase().includes(C)||j.full_name&&j.full_name.toLowerCase().includes(C)}).sort((j,C)=>{switch(f){case"fileCount":return(C.fileCount||0)-(j.fileCount||0);case"totalSize":return(C.totalSize||0)-(j.totalSize||0);case"name":default:const y=(j.full_name||j.primary_email).toLowerCase(),k=(C.full_name||C.primary_email).toLowerCase();return y.localeCompare(k)}}),p=j=>{const{name:C,value:y}=j.target;a(k=>({...k,[C]:y})),h[C]&&S(k=>({...k,[C]:null}))},d=j=>{const{name:C,checked:y}=j.target;C==="stopOnError"?a(k=>({...k,stopOnError:y,continueOnError:!y})):C==="continueOnError"&&a(k=>({...k,continueOnError:y,stopOnError:!y}))},m=j=>{a(C=>{const y=C.selectedUsers.includes(j);let k;return y?k=C.selectedUsers.filter(D=>D!==j):k=[...C.selectedUsers,j],k.length===e.length?o(!0):o(!1),{...C,selectedUsers:k}}),h.selectedUsers&&S(C=>({...C,selectedUsers:null}))},w=()=>{const j=!i;o(j),a(j?C=>({...C,selectedUsers:e.map(y=>y.primary_email)}):C=>({...C,selectedUsers:[]})),h.selectedUsers&&S(C=>({...C,selectedUsers:null}))},x=async j=>{try{const C=await oe(`/api/download/users/${encodeURIComponent(j)}/undownloaded-files`);if(C.success){const y=C.data.length;_(k=>({...k,[j]:{...k[j],undownloadedCount:y}})),y>0&&!s.selectedUsers.includes(j)&&m(j)}}catch(C){console.error("Error getting undownloaded files:",C)}},E=j=>{j.preventDefault();const C={};if(s.name.trim()||(C.name="Session name is required"),s.selectedUsers.length===0&&(C.selectedUsers="Please select at least one user"),s.downloadPath.trim()||(C.downloadPath="Download path is required"),Object.keys(C).length>0){S(C);return}const y=s.skipMimeTypes.split(",").map(k=>k.trim()).filter(k=>k.length>0);t({...s,skipMimeTypes:y})},P=j=>{if(j===0)return"0 Bytes";const C=1024,y=["Bytes","KB","MB","GB","TB"],k=Math.floor(Math.log(j)/Math.log(C));return parseFloat((j/Math.pow(C,k)).toFixed(2))+" "+y[k]};return l.jsxs("div",{className:"download-config-form",children:[l.jsx("h2",{children:"Configure Download Session"}),l.jsxs("form",{onSubmit:E,children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"name",children:"Session Name"}),l.jsx("input",{type:"text",id:"name",name:"name",value:s.name,onChange:p,placeholder:"Enter a name for this download session",disabled:n}),h.name&&l.jsx("div",{className:"error-message",children:h.name})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"downloadPath",children:"Download Path"}),l.jsx("input",{type:"text",id:"downloadPath",name:"downloadPath",value:s.downloadPath,onChange:p,placeholder:"Enter local path to save files (e.g., /Users/<USER>/Downloads)",disabled:n}),h.downloadPath&&l.jsx("div",{className:"error-message",children:h.downloadPath}),l.jsx("div",{className:"help-text",children:"Files will be saved in this directory, organized by user email"})]}),l.jsxs("div",{className:"form-row",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"concurrentDownloads",children:"Concurrent Downloads"}),l.jsx("input",{type:"number",id:"concurrentDownloads",name:"concurrentDownloads",value:s.concurrentDownloads,onChange:p,min:"1",max:"10",disabled:n}),l.jsx("div",{className:"help-text",children:"Number of files to download simultaneously"})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"maxRetries",children:"Max Retries"}),l.jsx("input",{type:"number",id:"maxRetries",name:"maxRetries",value:s.maxRetries,onChange:p,min:"0",max:"10",disabled:n}),l.jsx("div",{className:"help-text",children:"Number of times to retry failed downloads"})]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Error Handling"}),l.jsxs("div",{className:"error-handling-options",children:[l.jsx("div",{className:"radio-option",children:l.jsxs("label",{children:[l.jsx("input",{type:"radio",name:"stopOnError",checked:s.stopOnError,onChange:d,disabled:n}),l.jsxs("div",{className:"option-content",children:[l.jsx("div",{className:"option-title",children:"Stop on Error (Default)"}),l.jsx("div",{className:"option-description",children:"Stop the entire download process if any error occurs and log the error"})]})]})}),l.jsx("div",{className:"radio-option",children:l.jsxs("label",{children:[l.jsx("input",{type:"radio",name:"continueOnError",checked:s.continueOnError,onChange:d,disabled:n}),l.jsxs("div",{className:"option-content",children:[l.jsx("div",{className:"option-title",children:"Continue on Error"}),l.jsx("div",{className:"option-description",children:"Continue downloading other files if an error occurs and log errors for later review"})]})]})})]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"skipMimeTypes",children:"Skip MIME Types (Optional)"}),l.jsx("input",{type:"text",id:"skipMimeTypes",name:"skipMimeTypes",value:s.skipMimeTypes,onChange:p,placeholder:"e.g., application/vnd.google-apps.shortcut, application/vnd.google-apps.folder",disabled:n}),l.jsxs("div",{className:"help-text",children:["Comma-separated list of MIME types to skip during migration. Common examples:",l.jsx("br",{}),"• application/vnd.google-apps.shortcut (shortcuts)",l.jsx("br",{}),"• application/vnd.google-apps.folder (folders)"]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"processingOrder",children:"Processing Order"}),l.jsxs("select",{id:"processingOrder",name:"processingOrder",value:s.processingOrder,onChange:p,disabled:n,children:[l.jsx("option",{value:"created_at",children:"Default (Creation Time)"}),l.jsx("option",{value:"user_email",children:"By User Email (Process all files for one user first)"}),l.jsx("option",{value:"size_asc",children:"By File Size (Smallest First)"}),l.jsx("option",{value:"size_desc",children:"By File Size (Largest First)"})]}),l.jsx("div",{className:"help-text",children:"Choose the order in which files will be processed during download"})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Select Users to Download"}),h.selectedUsers&&l.jsx("div",{className:"error-message",children:h.selectedUsers}),l.jsxs("div",{className:"user-selection-header",children:[l.jsx("div",{className:"search-box",children:l.jsx("input",{type:"text",placeholder:"Search users...",value:u,onChange:j=>c(j.target.value),disabled:n})}),l.jsxs("div",{className:"sort-box",children:[l.jsx("label",{htmlFor:"sortBy",children:"Sort by:"}),l.jsxs("select",{id:"sortBy",value:f,onChange:j=>v(j.target.value),disabled:n,children:[l.jsx("option",{value:"name",children:"Name"}),l.jsx("option",{value:"fileCount",children:"Number of Files"}),l.jsx("option",{value:"totalSize",children:"Total File Size"})]})]}),l.jsx("div",{className:"select-all",children:l.jsxs("label",{children:[l.jsx("input",{type:"checkbox",checked:i,onChange:w,disabled:n}),"Select All Users"]})})]}),l.jsx("div",{className:"users-list",children:L.length===0?l.jsx("div",{className:"no-users",children:"No users found"}):L.map(j=>l.jsxs("div",{className:"user-item",children:[l.jsxs("label",{className:"user-checkbox",children:[l.jsx("input",{type:"checkbox",checked:s.selectedUsers.includes(j.primary_email),onChange:()=>m(j.primary_email),disabled:n}),l.jsxs("div",{className:"user-info-single-line",children:[l.jsx("span",{className:"user-name",children:j.full_name||j.primary_email}),l.jsxs("span",{className:"user-email",children:["(",j.primary_email,")"]}),l.jsxs("span",{className:"user-stats-inline",children:["• ",j.fileCount," files • ",P(j.totalSize)]})]})]}),l.jsx("button",{type:"button",className:"select-undownloaded-btn",onClick:()=>x(j.primary_email),disabled:n,title:"Select all undownloaded files for this user",children:"📥 Select Undownloaded"})]},j.user_id))})]}),l.jsx("div",{className:"form-actions",children:l.jsx("button",{type:"submit",className:"btn btn-primary",disabled:n,children:n?"Creating...":"Create Download Session"})})]})]})},$m=({session:e,onPause:t,onCancel:n,onComplete:r})=>{var C;const[s,a]=g.useState(null),[i,o]=g.useState([]),[u,c]=g.useState(!0),[f,v]=g.useState(1),[h]=g.useState(20),[S,N]=g.useState("all");g.useEffect(()=>{if(e!=null&&e.id){_(),L();const y=setInterval(()=>{_(),L()},2e3);return()=>clearInterval(y)}},[e==null?void 0:e.id,f,S]),g.useEffect(()=>{var y,k;(((y=s==null?void 0:s.session)==null?void 0:y.status)==="completed"||((k=s==null?void 0:s.session)==null?void 0:k.status)==="failed")&&(r==null||r())},[(C=s==null?void 0:s.session)==null?void 0:C.status,r]);const _=async()=>{try{const y=await oe(`/api/download/sessions/${e.id}/progress`);y.success&&a(y.data)}catch(y){console.error("Error loading progress:",y)}finally{c(!1)}},L=async()=>{try{const y=new URLSearchParams({page:f.toString(),limit:h.toString()});S!=="all"&&y.append("status",S);const k=await oe(`/api/download/sessions/${e.id}/items?${y}`);k.success&&o(k.data.items)}catch(y){console.error("Error loading download items:",y)}},p=y=>{if(y===0)return"0 Bytes";const k=1024,D=["Bytes","KB","MB","GB","TB"],M=Math.floor(Math.log(y)/Math.log(k));return parseFloat((y/Math.pow(k,M)).toFixed(2))+" "+D[M]},d=y=>{if(!y)return"-";const k=Math.floor(y/3600),D=Math.floor(y%3600/60),M=y%60;return k>0?`${k}h ${D}m ${M}s`:D>0?`${D}m ${M}s`:`${M}s`},m=y=>{switch(y){case"pending":return"⏳";case"downloading":return"📥";case"completed":return"✅";case"failed":return"❌";case"skipped":return"⏭️";default:return"❓"}},w=y=>{switch(y){case"pending":return"status-pending";case"downloading":return"status-downloading";case"completed":return"status-completed";case"failed":return"status-failed";case"skipped":return"status-skipped";default:return"status-unknown"}};if(u)return l.jsxs("div",{className:"download-progress loading",children:[l.jsx("div",{className:"loading-spinner"}),l.jsx("p",{children:"Loading progress..."})]});if(!s)return l.jsx("div",{className:"download-progress error",children:l.jsx("p",{children:"Failed to load progress data"})});const{session:x,progress:E}=s,P=x.progress_percentage||0,j=x.size_progress_percentage||0;return l.jsxs("div",{className:"download-progress",children:[l.jsxs("div",{className:"progress-header",children:[l.jsx("h2",{children:"Download Progress"}),l.jsx("div",{className:"session-status",children:l.jsx("span",{className:`status-badge ${w(x.status)}`,children:x.status})})]}),l.jsxs("div",{className:"overall-progress",children:[l.jsxs("div",{className:"progress-stats",children:[l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-value",children:x.downloaded_files||0}),l.jsx("div",{className:"stat-label",children:"Downloaded"})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-value",children:x.total_files||0}),l.jsx("div",{className:"stat-label",children:"Total Files"})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-value",children:p(x.downloaded_size||0)}),l.jsx("div",{className:"stat-label",children:"Downloaded Size"})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-value",children:p(x.total_size||0)}),l.jsx("div",{className:"stat-label",children:"Total Size"})]})]}),l.jsxs("div",{className:"progress-bars",children:[l.jsxs("div",{className:"progress-bar-container",children:[l.jsxs("div",{className:"progress-label",children:["Files Progress: ",P.toFixed(1),"%"]}),l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:"progress-fill",style:{width:`${P}%`}})})]}),l.jsxs("div",{className:"progress-bar-container",children:[l.jsxs("div",{className:"progress-label",children:["Size Progress: ",j.toFixed(1),"%"]}),l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:"progress-fill size-progress",style:{width:`${j}%`}})})]})]}),x.current_file_name&&l.jsxs("div",{className:"current-activity",children:[l.jsx("div",{className:"activity-icon",children:"📥"}),l.jsxs("div",{className:"activity-text",children:[l.jsx("div",{className:"current-user",children:x.current_user_email}),l.jsx("div",{className:"current-file",children:x.current_file_name})]})]}),l.jsx("div",{className:"session-duration",children:l.jsxs("span",{children:["Duration: ",d(x.duration_seconds)]})})]}),l.jsxs("div",{className:"status-summary",children:[l.jsxs("div",{className:"status-item",children:[l.jsx("span",{className:"status-icon",children:"⏳"}),l.jsx("span",{className:"status-count",children:E.pending||0}),l.jsx("span",{className:"status-text",children:"Pending"})]}),l.jsxs("div",{className:"status-item",children:[l.jsx("span",{className:"status-icon",children:"📥"}),l.jsx("span",{className:"status-count",children:E.downloading||0}),l.jsx("span",{className:"status-text",children:"Downloading"})]}),l.jsxs("div",{className:"status-item",children:[l.jsx("span",{className:"status-icon",children:"✅"}),l.jsx("span",{className:"status-count",children:E.completed||0}),l.jsx("span",{className:"status-text",children:"Completed"})]}),l.jsxs("div",{className:"status-item",children:[l.jsx("span",{className:"status-icon",children:"❌"}),l.jsx("span",{className:"status-count",children:E.failed||0}),l.jsx("span",{className:"status-text",children:"Failed"})]}),l.jsxs("div",{className:"status-item",children:[l.jsx("span",{className:"status-icon",children:"⏭️"}),l.jsx("span",{className:"status-count",children:E.skipped||0}),l.jsx("span",{className:"status-text",children:"Skipped"})]})]}),x.status==="running"&&l.jsxs("div",{className:"control-buttons",children:[l.jsx("button",{className:"btn btn-warning",onClick:t,children:"⏸️ Pause Download"}),l.jsx("button",{className:"btn btn-danger",onClick:n,children:"❌ Cancel Download"})]}),l.jsxs("div",{className:"download-items",children:[l.jsxs("div",{className:"items-header",children:[l.jsx("h3",{children:"Download Items"}),l.jsx("div",{className:"items-filter",children:l.jsxs("select",{value:S,onChange:y=>N(y.target.value),children:[l.jsx("option",{value:"all",children:"All Status"}),l.jsx("option",{value:"pending",children:"Pending"}),l.jsx("option",{value:"downloading",children:"Downloading"}),l.jsx("option",{value:"completed",children:"Completed"}),l.jsx("option",{value:"failed",children:"Failed"}),l.jsx("option",{value:"skipped",children:"Skipped"})]})})]}),l.jsx("div",{className:"items-list",children:i.map(y=>l.jsxs("div",{className:"download-item",children:[l.jsx("div",{className:"item-status",children:l.jsx("span",{className:`status-icon ${w(y.status)}`,children:m(y.status)})}),l.jsxs("div",{className:"item-info",children:[l.jsx("div",{className:"item-name",children:y.file_name}),l.jsx("div",{className:"item-path",children:y.file_path}),l.jsx("div",{className:"item-user",children:y.user_email})]}),l.jsxs("div",{className:"item-details",children:[l.jsx("div",{className:"item-size",children:p(y.file_size)}),y.error_message&&l.jsxs("div",{className:"item-error",title:y.error_message,children:["Error: ",y.error_message.substring(0,50),"..."]}),y.retry_count>0&&l.jsxs("div",{className:"item-retry",children:["Retries: ",y.retry_count]})]})]},y.id))}),i.length===0&&l.jsx("div",{className:"no-items",children:"No items found for the selected filter"})]})]})},Im=({session:e,onStartNew:t})=>{const[n,r]=g.useState(null),[s,a]=g.useState([]),[i,o]=g.useState(!0),[u,c]=g.useState(!1),[f,v]=g.useState(!1);g.useEffect(()=>{e!=null&&e.id&&(h(),S())},[e==null?void 0:e.id]);const h=async()=>{try{const j=await oe(`/api/download/sessions/${e.id}/progress`);j.success&&r(j.data)}catch(j){console.error("Error loading report data:",j)}finally{o(!1)}},S=async()=>{try{const j=await oe(`/api/download/sessions/${e.id}/items?status=failed&limit=100`);j.success&&a(j.data.items)}catch(j){console.error("Error loading failed items:",j)}},N=()=>{const j={session:n.session,progress:n.progress,failedItems:s,exportedAt:new Date().toISOString()},C=JSON.stringify(j,null,2),y=new Blob([C],{type:"application/json"}),k=URL.createObjectURL(y),D=document.createElement("a");D.href=k,D.download=`download-report-${e.id}.json`,document.body.appendChild(D),D.click(),document.body.removeChild(D),URL.revokeObjectURL(k)},_=()=>{if(s.length===0){alert("No failed items to export");return}const C=[["File Name","File Path","User Email","Error Message","Retry Count"].join(","),...s.map(M=>[`"${M.file_name}"`,`"${M.file_path}"`,`"${M.user_email}"`,`"${M.error_message||""}"`,M.retry_count||0].join(","))].join(`
`),y=new Blob([C],{type:"text/csv"}),k=URL.createObjectURL(y),D=document.createElement("a");D.href=k,D.download=`failed-downloads-${e.id}.csv`,document.body.appendChild(D),D.click(),document.body.removeChild(D),URL.revokeObjectURL(k)},L=j=>{if(j===0)return"0 Bytes";const C=1024,y=["Bytes","KB","MB","GB","TB"],k=Math.floor(Math.log(j)/Math.log(C));return parseFloat((j/Math.pow(C,k)).toFixed(2))+" "+y[k]},p=j=>{if(!j)return"-";const C=Math.floor(j/3600),y=Math.floor(j%3600/60),k=j%60;return C>0?`${C}h ${y}m ${k}s`:y>0?`${y}m ${k}s`:`${k}s`},d=()=>{var y;if(!((y=n==null?void 0:n.session)!=null&&y.total_files))return 0;const j=n.progress.completed||0,C=n.session.total_files;return(j/C*100).toFixed(1)},m=j=>{switch(j){case"completed":return"#27ae60";case"failed":return"#e74c3c";case"cancelled":return"#95a5a6";default:return"#6c757d"}};if(i)return l.jsxs("div",{className:"download-report loading",children:[l.jsx("div",{className:"loading-spinner"}),l.jsx("p",{children:"Loading report..."})]});if(!n)return l.jsx("div",{className:"download-report error",children:l.jsx("p",{children:"Failed to load report data"})});const{session:w,progress:x}=n,E=w.status==="completed",P=(x.failed||0)>0;return l.jsxs("div",{className:"download-report",children:[l.jsxs("div",{className:"report-header",children:[l.jsx("h2",{children:"Download Report"}),l.jsxs("div",{className:"session-status",style:{color:m(w.status)},children:[w.status==="completed"?"✅":w.status==="failed"?"❌":"⚠️",w.status.toUpperCase()]})]}),l.jsxs("div",{className:"summary-cards",children:[l.jsxs("div",{className:"summary-card success",children:[l.jsx("div",{className:"card-icon",children:"✅"}),l.jsxs("div",{className:"card-content",children:[l.jsx("div",{className:"card-value",children:x.completed||0}),l.jsx("div",{className:"card-label",children:"Files Downloaded"})]})]}),l.jsxs("div",{className:"summary-card total",children:[l.jsx("div",{className:"card-icon",children:"📁"}),l.jsxs("div",{className:"card-content",children:[l.jsx("div",{className:"card-value",children:w.total_files||0}),l.jsx("div",{className:"card-label",children:"Total Files"})]})]}),l.jsxs("div",{className:"summary-card size",children:[l.jsx("div",{className:"card-icon",children:"💾"}),l.jsxs("div",{className:"card-content",children:[l.jsx("div",{className:"card-value",children:L(w.downloaded_size||0)}),l.jsx("div",{className:"card-label",children:"Downloaded Size"})]})]}),l.jsxs("div",{className:"summary-card rate",children:[l.jsx("div",{className:"card-icon",children:"📊"}),l.jsxs("div",{className:"card-content",children:[l.jsxs("div",{className:"card-value",children:[d(),"%"]}),l.jsx("div",{className:"card-label",children:"Success Rate"})]})]})]}),l.jsxs("div",{className:"session-details",children:[l.jsx("h3",{children:"Session Details"}),l.jsxs("div",{className:"details-grid",children:[l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Session Name:"}),l.jsx("span",{className:"detail-value",children:w.name})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Started:"}),l.jsx("span",{className:"detail-value",children:w.started_at?new Date(w.started_at).toLocaleString():"-"})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Completed:"}),l.jsx("span",{className:"detail-value",children:w.completed_at?new Date(w.completed_at).toLocaleString():"-"})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Duration:"}),l.jsx("span",{className:"detail-value",children:p(w.duration_seconds)})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Download Path:"}),l.jsx("span",{className:"detail-value",children:w.download_path})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Concurrent Downloads:"}),l.jsx("span",{className:"detail-value",children:w.concurrent_downloads})]})]})]}),l.jsxs("div",{className:"status-breakdown",children:[l.jsx("h3",{children:"Status Breakdown"}),l.jsxs("div",{className:"status-grid",children:[l.jsxs("div",{className:"status-item completed",children:[l.jsx("div",{className:"status-count",children:x.completed||0}),l.jsx("div",{className:"status-label",children:"Completed"})]}),l.jsxs("div",{className:"status-item failed",children:[l.jsx("div",{className:"status-count",children:x.failed||0}),l.jsx("div",{className:"status-label",children:"Failed"})]}),l.jsxs("div",{className:"status-item skipped",children:[l.jsx("div",{className:"status-count",children:x.skipped||0}),l.jsx("div",{className:"status-label",children:"Skipped"})]}),l.jsxs("div",{className:"status-item pending",children:[l.jsx("div",{className:"status-count",children:x.pending||0}),l.jsx("div",{className:"status-label",children:"Pending"})]})]})]}),P&&l.jsxs("div",{className:"failed-items-section",children:[l.jsxs("div",{className:"section-header",children:[l.jsxs("h3",{children:["Failed Downloads (",s.length,")"]}),l.jsxs("button",{className:"btn btn-secondary",onClick:()=>c(!u),children:[u?"Hide":"Show"," Failed Items"]})]}),u&&l.jsx("div",{className:"failed-items-list",children:s.map(j=>l.jsxs("div",{className:"failed-item",children:[l.jsxs("div",{className:"item-info",children:[l.jsx("div",{className:"item-name",children:j.file_name}),l.jsx("div",{className:"item-path",children:j.file_path}),l.jsx("div",{className:"item-user",children:j.user_email})]}),l.jsxs("div",{className:"item-error",children:[l.jsx("div",{className:"error-message",children:j.error_message}),l.jsxs("div",{className:"retry-count",children:["Retries: ",j.retry_count||0]})]})]},j.id))})]}),w.continue_on_error&&w.error_log&&w.error_log.length>0&&l.jsxs("div",{className:"error-log-section",children:[l.jsxs("div",{className:"section-header",children:[l.jsxs("h3",{children:["Error Log (",w.error_log.length," errors)"]}),l.jsxs("button",{className:"btn btn-secondary",onClick:()=>v(!f),children:[f?"Hide":"Show"," Error Log"]})]}),f&&l.jsx("div",{className:"error-log-list",children:w.error_log.map((j,C)=>l.jsxs("div",{className:"error-log-item",children:[l.jsx("div",{className:"error-timestamp",children:new Date(j.timestamp).toLocaleString()}),l.jsxs("div",{className:"error-details",children:[l.jsxs("div",{className:"error-file-info",children:[l.jsx("div",{className:"error-file-name",children:j.file_name}),l.jsx("div",{className:"error-file-path",children:j.file_path}),l.jsx("div",{className:"error-user-email",children:j.user_email})]}),l.jsxs("div",{className:"error-message-detail",children:[l.jsx("strong",{children:"Error:"})," ",j.error_message]}),l.jsxs("div",{className:"error-retry-info",children:["Retry attempt: ",j.retry_count||0]})]})]},C))})]}),l.jsxs("div",{className:"report-actions",children:[l.jsx("button",{className:"btn btn-primary",onClick:t,children:"🆕 Start New Download"}),l.jsx("button",{className:"btn btn-secondary",onClick:N,children:"📄 Export Report"}),P&&l.jsx("button",{className:"btn btn-warning",onClick:_,children:"📋 Export Failed Items"})]}),l.jsx("div",{className:`result-message ${E?"success":"failure"}`,children:E?l.jsxs("div",{children:[l.jsx("h4",{children:"🎉 Download Completed Successfully!"}),l.jsxs("p",{children:["All files have been downloaded to: ",l.jsx("strong",{children:w.download_path})]})]}):l.jsxs("div",{children:[l.jsx("h4",{children:"⚠️ Download Completed with Issues"}),l.jsxs("p",{children:[x.completed||0," files downloaded successfully,",x.failed||0," files failed. Check the failed items above for details."]})]})})]})},vd=g.createContext(),Om=({children:e})=>{const t=_r();return l.jsxs(vd.Provider,{value:t,children:[e,l.jsx(Ys,{toasts:t.toasts,onRemoveToast:t.removeToast})]})},Um=()=>{const e=g.useContext(vd);if(!e)throw new Error("useToast must be used within a ToastProvider");return e},Bm=()=>{const[e,t]=g.useState("config"),[n,r]=g.useState([]),[s,a]=g.useState(null),[i,o]=g.useState(!1),[u,c]=g.useState([]),{showError:f,showSuccess:v,showWarning:h,showInfo:S}=Um();g.useEffect(()=>{N(),_()},[]);const N=async()=>{try{const y=await oe("/api/download/sessions");y.success&&r(y.data)}catch(y){console.error("Error loading sessions:",y),f("Failed to load download sessions")}},_=async()=>{try{const y=await oe("/api/download/users");y.success&&c(y.data)}catch(y){console.error("Error loading users:",y),f("Failed to load users")}},L=async y=>{try{o(!0);const k=await Me("/api/download/sessions",y);k.success?(a(k.data),t("progress"),await N(),v("Download session created successfully")):f(k.error||"Failed to create download session")}catch(k){console.error("Error creating session:",k),f("Failed to create download session")}finally{o(!1)}},p=async y=>{try{o(!0);const k=await Me(`/api/download/sessions/${y}/start`);k.success?(await N(),v("Download started successfully")):f(k.error||"Failed to start download")}catch(k){console.error("Error starting session:",k),f("Failed to start download")}finally{o(!1)}},d=async y=>{try{const k=await Me(`/api/download/sessions/${y}/pause`);k.success?(await N(),S("Download paused")):f(k.error||"Failed to pause download")}catch(k){console.error("Error pausing session:",k),f("Failed to pause download")}},m=async y=>{try{const k=await Me(`/api/download/sessions/${y}/cancel`);k.success?(await N(),h("Download cancelled")):f(k.error||"Failed to cancel download")}catch(k){console.error("Error cancelling session:",k),f("Failed to cancel download")}},w=async y=>{if(window.confirm("Are you sure you want to delete this session?"))try{const k=await _m(`/api/download/sessions/${y}`);k.success?(await N(),v("Session deleted successfully")):f(k.error||"Failed to delete session")}catch(k){console.error("Error deleting session:",k),f("Failed to delete session")}},x=y=>{a(y),y.status==="running"?t("progress"):y.status==="completed"||y.status==="failed"?t("report"):t("config")},E=()=>{a(null),t("config"),N()},P=y=>{if(y===0)return"0 Bytes";const k=1024,D=["Bytes","KB","MB","GB","TB"],M=Math.floor(Math.log(y)/Math.log(k));return parseFloat((y/Math.pow(k,M)).toFixed(2))+" "+D[M]},j=y=>{if(!y)return"-";const k=Math.floor(y/3600),D=Math.floor(y%3600/60),M=y%60;return k>0?`${k}h ${D}m ${M}s`:D>0?`${D}m ${M}s`:`${M}s`},C=y=>{switch(y){case"pending":return"status-pending";case"running":return"status-running";case"paused":return"status-paused";case"completed":return"status-completed";case"failed":return"status-failed";case"cancelled":return"status-cancelled";default:return"status-unknown"}};return l.jsxs("div",{className:"download-page",children:[l.jsxs("div",{className:"page-header",children:[l.jsx("h1",{children:"📥 Download Files from Google Drive"}),l.jsx("p",{children:"Download files from Google Drive to local storage with progress tracking"})]}),s?l.jsxs("div",{className:"session-detail",children:[l.jsxs("div",{className:"session-nav",children:[l.jsx("button",{className:"btn btn-secondary",onClick:E,children:"← Back to Sessions"}),l.jsx("h2",{children:s.name})]}),e==="config"&&l.jsx(Mm,{users:u,onSubmit:L,loading:i,initialData:s}),e==="progress"&&l.jsx($m,{session:s,onPause:()=>d(s.id),onCancel:()=>m(s.id),onComplete:()=>t("report")}),e==="report"&&l.jsx(Im,{session:s,onStartNew:()=>t("config")})]}):l.jsxs("div",{className:"sessions-overview",children:[l.jsxs("div",{className:"actions-bar",children:[l.jsx("button",{className:"btn btn-primary",onClick:()=>{a({name:"",status:"new"}),t("config")},children:"➕ Create New Download Session"}),l.jsx("button",{className:"btn btn-secondary",onClick:N,children:"🔄 Refresh"})]}),l.jsxs("div",{className:"sessions-list",children:[l.jsx("h2",{children:"Download Sessions"}),n.length===0?l.jsx("div",{className:"empty-state",children:l.jsx("p",{children:"No download sessions found. Create your first session to get started."})}):l.jsx("div",{className:"sessions-grid",children:n.map(y=>l.jsxs("div",{className:"session-card",children:[l.jsxs("div",{className:"session-header",children:[l.jsx("h3",{children:y.name}),l.jsx("span",{className:`status-badge ${C(y.status)}`,children:y.status})]}),l.jsxs("div",{className:"session-stats",children:[l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"label",children:"Files:"}),l.jsxs("span",{className:"value",children:[y.downloaded_files||0," / ",y.total_files||0]})]}),l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"label",children:"Size:"}),l.jsxs("span",{className:"value",children:[P(y.downloaded_size||0)," / ",P(y.total_size||0)]})]}),l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"label",children:"Progress:"}),l.jsxs("span",{className:"value",children:[y.progress_percentage||0,"%"]})]}),l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"label",children:"Duration:"}),l.jsx("span",{className:"value",children:j(y.duration_seconds)})]})]}),l.jsxs("div",{className:"session-actions",children:[l.jsx("button",{className:"btn btn-sm btn-primary",onClick:()=>x(y),children:"👁️ View"}),y.status==="pending"&&l.jsx("button",{className:"btn btn-sm btn-success",onClick:()=>p(y.id),disabled:i,children:"▶️ Start"}),y.status==="running"&&l.jsx("button",{className:"btn btn-sm btn-warning",onClick:()=>d(y.id),children:"⏸️ Pause"}),(y.status==="running"||y.status==="paused")&&l.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>m(y.id),children:"❌ Cancel"}),(y.status==="completed"||y.status==="failed"||y.status==="cancelled")&&l.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>w(y.id),children:"🗑️ Delete"})]})]},y.id))})]})]})]})};function Am({users:e,selectedUser:t,onUserSelect:n,loading:r,viewMode:s="grid"}){if(r)return l.jsxs("div",{className:"user-list-loading",children:[l.jsx("div",{className:"loading-spinner"}),l.jsx("p",{children:"Đang tải danh sách người dùng..."})]});if(e.length===0)return l.jsxs("div",{className:"user-list-empty",children:[l.jsx("div",{className:"empty-icon",children:"👥"}),l.jsx("p",{children:"Không tìm thấy người dùng nào"})]});const a=(c,f)=>f?f.split(" ").map(v=>v[0]).join("").toUpperCase().slice(0,2):c.split("@")[0].slice(0,2).toUpperCase(),i=c=>c.name||c.email.split("@")[0],o=c=>({files:c.total_files||0,size:u(c.total_size||0),lastActive:c.last_active?new Date(c.last_active).toLocaleDateString("vi-VN"):"Chưa rõ"}),u=c=>{if(c===0)return"0 B";const f=1024,v=["B","KB","MB","GB","TB"],h=Math.floor(Math.log(c)/Math.log(f));return parseFloat((c/Math.pow(f,h)).toFixed(1))+" "+v[h]};return l.jsx("div",{className:`user-list ${s}`,children:e.map(c=>{const f=(t==null?void 0:t.email)===c.email,v=o(c),h=a(c.email,c.name),S=i(c);return l.jsxs("div",{className:`user-item ${f?"selected":""}`,onClick:()=>n(c),role:"button",tabIndex:0,onKeyDown:N=>{(N.key==="Enter"||N.key===" ")&&n(c)},children:[l.jsxs("div",{className:"user-avatar",children:[c.avatar?l.jsx("img",{src:c.avatar,alt:S}):l.jsx("div",{className:"user-initials",children:h}),f&&l.jsx("div",{className:"selection-indicator",children:"✓"})]}),l.jsxs("div",{className:"user-info",children:[l.jsxs("div",{className:"user-primary",children:[l.jsx("div",{className:"user-name",title:S,children:S}),l.jsx("div",{className:"user-email",title:c.email,children:c.email})]}),l.jsxs("div",{className:"user-stats",children:[l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-icon",children:"📁"}),l.jsx("span",{className:"stat-value",children:v.files})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-icon",children:"💾"}),l.jsx("span",{className:"stat-value",children:v.size})]}),s==="list"&&l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-icon",children:"📅"}),l.jsx("span",{className:"stat-value",children:v.lastActive})]})]}),c.status&&l.jsxs("div",{className:`user-status status-${c.status}`,children:[c.status==="active"&&"🟢 Hoạt động",c.status==="inactive"&&"🟡 Không hoạt động",c.status==="blocked"&&"🔴 Bị chặn"]})]}),s==="grid"&&l.jsx("div",{className:"user-actions",children:l.jsx("button",{className:"btn-action",onClick:N=>{N.stopPropagation(),console.log("Quick action for",c.email)},title:"Hành động nhanh",children:"⚡"})})]},c.email)})})}function Hm({value:e,onChange:t,placeholder:n="Tìm kiếm...",onFilter:r}){const[s,a]=g.useState(!1),[i,o]=g.useState(!1),u=g.useRef(null),c=()=>{var v;t(""),(v=u.current)==null||v.focus()},f=v=>{v.key==="Escape"&&c()};return g.useEffect(()=>{const v=h=>{u.current&&!u.current.contains(h.target)&&o(!1)};return document.addEventListener("mousedown",v),()=>document.removeEventListener("mousedown",v)},[]),l.jsxs("div",{className:`user-search ${s?"focused":""}`,children:[l.jsxs("div",{className:"search-input-container",children:[l.jsx("div",{className:"search-icon",children:"🔍"}),l.jsx("input",{ref:u,type:"text",value:e,onChange:v=>t(v.target.value),onFocus:()=>a(!0),onBlur:()=>a(!1),onKeyDown:f,placeholder:n,className:"search-input"}),e&&l.jsx("button",{className:"clear-button",onClick:c,title:"Xóa tìm kiếm",children:"✕"}),r&&l.jsx("button",{className:`filter-button ${i?"active":""}`,onClick:()=>o(!i),title:"Bộ lọc",children:"⚙️"})]}),i&&r&&l.jsxs("div",{className:"search-filters",children:[l.jsxs("div",{className:"filter-group",children:[l.jsx("label",{className:"filter-label",children:"Trạng thái:"}),l.jsxs("div",{className:"filter-options",children:[l.jsxs("label",{className:"filter-option",children:[l.jsx("input",{type:"checkbox"})," Hoạt động"]}),l.jsxs("label",{className:"filter-option",children:[l.jsx("input",{type:"checkbox"})," Không hoạt động"]}),l.jsxs("label",{className:"filter-option",children:[l.jsx("input",{type:"checkbox"})," Bị chặn"]})]})]}),l.jsxs("div",{className:"filter-group",children:[l.jsx("label",{className:"filter-label",children:"Kích thước dữ liệu:"}),l.jsx("div",{className:"filter-range",children:l.jsxs("select",{className:"filter-select",children:[l.jsx("option",{value:"",children:"Tất cả"}),l.jsx("option",{value:"small",children:"Dưới 1GB"}),l.jsx("option",{value:"medium",children:"1GB - 10GB"}),l.jsx("option",{value:"large",children:"Trên 10GB"})]})})]}),l.jsxs("div",{className:"filter-actions",children:[l.jsx("button",{className:"btn-filter btn-apply",children:"Áp dụng"}),l.jsx("button",{className:"btn-filter btn-reset",children:"Đặt lại"})]})]})]})}function Wm({users:e,selectedUser:t,userFiles:n}){const r=()=>{const i=e.length,o=e.filter(f=>f.status==="active").length,u=e.reduce((f,v)=>f+(v.total_files||0),0),c=e.reduce((f,v)=>f+(v.total_size||0),0);return{totalUsers:i,activeUsers:o,totalFiles:u,totalSize:c,selectedUserFiles:n.length,selectedUserSize:n.reduce((f,v)=>f+(v.size||0),0)}},s=i=>{if(i===0)return"0 B";const o=1024,u=["B","KB","MB","GB","TB"],c=Math.floor(Math.log(i)/Math.log(o));return parseFloat((i/Math.pow(o,c)).toFixed(1))+" "+u[c]},a=r();return l.jsxs("div",{className:"user-stats",children:[l.jsxs("div",{className:"stats-section global-stats",children:[l.jsx("h3",{children:"📊 Thống kê tổng quan"}),l.jsxs("div",{className:"stats-grid",children:[l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"👥"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-value",children:a.totalUsers}),l.jsx("div",{className:"stat-label",children:"Tổng người dùng"})]})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"🟢"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-value",children:a.activeUsers}),l.jsx("div",{className:"stat-label",children:"Đang hoạt động"})]})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"📁"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-value",children:a.totalFiles.toLocaleString()}),l.jsx("div",{className:"stat-label",children:"Tổng files"})]})]}),l.jsxs("div",{className:"stat-card",children:[l.jsx("div",{className:"stat-icon",children:"💾"}),l.jsxs("div",{className:"stat-content",children:[l.jsx("div",{className:"stat-value",children:s(a.totalSize)}),l.jsx("div",{className:"stat-label",children:"Tổng dung lượng"})]})]})]})]}),t&&l.jsxs("div",{className:"stats-section selected-stats",children:[l.jsx("h3",{children:"🎯 Thông tin được chọn"}),l.jsxs("div",{className:"selected-user-info",children:[l.jsxs("div",{className:"selected-user-header",children:[l.jsx("div",{className:"selected-user-avatar",children:t.avatar?l.jsx("img",{src:t.avatar,alt:t.name||t.email}):l.jsx("div",{className:"selected-user-initials",children:(t.name||t.email).slice(0,2).toUpperCase()})}),l.jsxs("div",{className:"selected-user-details",children:[l.jsx("div",{className:"selected-user-name",children:t.name||t.email.split("@")[0]}),l.jsx("div",{className:"selected-user-email",children:t.email})]})]}),l.jsxs("div",{className:"selected-stats-grid",children:[l.jsxs("div",{className:"selected-stat",children:[l.jsx("span",{className:"selected-stat-icon",children:"📁"}),l.jsx("span",{className:"selected-stat-value",children:a.selectedUserFiles}),l.jsx("span",{className:"selected-stat-label",children:"files đã tải"})]}),l.jsxs("div",{className:"selected-stat",children:[l.jsx("span",{className:"selected-stat-icon",children:"💾"}),l.jsx("span",{className:"selected-stat-value",children:s(a.selectedUserSize)}),l.jsx("span",{className:"selected-stat-label",children:"dung lượng"})]}),l.jsxs("div",{className:"selected-stat",children:[l.jsx("span",{className:"selected-stat-icon",children:"📂"}),l.jsx("span",{className:"selected-stat-value",children:n.filter(i=>i.mimeType==="application/vnd.google-apps.folder").length}),l.jsx("span",{className:"selected-stat-label",children:"thư mục"})]})]})]})]})]})}function Vm(){const[e,t]=g.useState([]),[n,r]=g.useState(null),[s,a]=g.useState([]),[i,o]=g.useState(null),[u,c]=g.useState([]),[f,v]=g.useState(!1),[h,S]=g.useState(!1),[N,_]=g.useState(null),[L,p]=g.useState(""),[d,m]=g.useState("grid"),[w,x]=g.useState(!1),{toasts:E,removeToast:P,showError:j,showSuccess:C,showWarning:y}=_r();g.useEffect(()=>{k()},[]);const k=async()=>{var V;v(!0),_(null);try{const R=await oe("/api/scan/users");t(R.users||[]),C(`Đã tải ${((V=R.users)==null?void 0:V.length)||0} người dùng`)}catch(R){console.error("Error loading users:",R);const $=Ee(R);_(R),j(`Lỗi tải danh sách người dùng: ${$.message}`,{showDetails:!0,details:$.details,duration:8e3})}finally{v(!1)}},D=async V=>{S(!0),_(null);try{const R=await oe(`/api/scan/users/${V.email}/files`);a(Array.isArray(R.tree)?R.tree:[]),o(R.stats||null),r(V),c([]),C(`Đã tải cấu trúc thư mục của ${V.email}`)}catch(R){console.error("Error loading user files:",R);const $=Ee(R);_(R),j(`Lỗi tải thư mục của ${V.email}: ${$.message}`,{showDetails:!0,details:$.details,duration:8e3}),a([]),o(null),c([])}finally{S(!1)}},M=V=>{(n==null?void 0:n.email)===V.email?(r(null),setUserFiles([])):D(V)},U=()=>{k()},Ye=()=>{n&&D(n)},Xe=e.filter(V=>{var R;return V.email.toLowerCase().includes(L.toLowerCase())||((R=V.name)==null?void 0:R.toLowerCase().includes(L.toLowerCase()))});return l.jsxs("div",{className:"users-overview",children:[l.jsxs("header",{className:"users-overview-header",children:[l.jsxs("div",{className:"header-content",children:[l.jsx("h1",{children:"📁 Quản lý Users & Files"}),l.jsxs("div",{className:"header-actions",children:[l.jsx(Hm,{value:L,onChange:p,placeholder:"Tìm kiếm theo email hoặc tên..."}),l.jsxs("div",{className:"view-controls",children:[l.jsx("button",{className:`btn-icon ${d==="grid"?"active":""}`,onClick:()=>m("grid"),title:"Chế độ lưới",children:"⊞"}),l.jsx("button",{className:`btn-icon ${d==="list"?"active":""}`,onClick:()=>m("list"),title:"Chế độ danh sách",children:"☰"}),l.jsx("button",{className:"btn-icon",onClick:()=>x(!w),title:w?"Mở rộng sidebar":"Thu gọn sidebar",children:w?"▶":"◀"})]}),l.jsx("button",{className:"btn btn-secondary",onClick:U,disabled:f,children:"🔄 Làm mới"})]})]}),l.jsx(Wm,{users:e,selectedUser:n,userFiles:s})]}),l.jsxs("main",{className:`users-overview-main ${w?"sidebar-collapsed":""}`,children:[l.jsxs("aside",{className:"users-sidebar",children:[l.jsxs("div",{className:"sidebar-header",children:[l.jsxs("h2",{children:["👥 Người dùng (",Xe.length,")"]}),n&&l.jsx("button",{className:"btn-small btn-ghost",onClick:()=>{r(null),setUserFiles([])},title:"Bỏ chọn user",children:"✕"})]}),N&&l.jsx(Xt,{error:N,title:"Lỗi tải dữ liệu",onDismiss:()=>_(null),onRetry:()=>{n?D(n):k()},compact:!0}),l.jsx(Am,{users:Xe,selectedUser:n,onUserSelect:M,loading:f,viewMode:d})]}),l.jsx("section",{className:"file-tree-content",children:n?l.jsxs("div",{className:"file-tree-container",children:[l.jsxs("div",{className:"file-tree-header",children:[l.jsxs("h2",{children:["📂 Thư mục của ",n.email]}),l.jsxs("div",{className:"file-tree-actions",children:[l.jsxs("span",{className:"file-count",children:[Array.isArray(s)?s.length:0," mục"]}),l.jsx("button",{className:"btn-small btn-secondary",onClick:Ye,disabled:h,children:"🔄 Làm mới"})]})]}),l.jsx(md,{stats:i,selectedFiles:u}),l.jsx(pd,{tree:Array.isArray(s)?s:[],selectedFiles:u,onFileSelect:(V,R)=>{let $;R?$=[...u,V]:$=u.filter(O=>O.id!==V.id),c($)},onSelectAll:V=>c(V)})]}):l.jsx("div",{className:"empty-state",children:l.jsxs("div",{className:"empty-state-content",children:[l.jsx("div",{className:"empty-icon",children:"👆"}),l.jsx("h3",{children:"Chọn một người dùng"}),l.jsx("p",{children:"Chọn một người dùng từ danh sách bên trái để xem cấu trúc thư mục của họ"}),e.length===0&&!f&&l.jsxs("div",{className:"empty-users",children:[l.jsx("p",{children:"Không có người dùng nào. Hãy kiểm tra kết nối API."}),l.jsx("button",{className:"btn btn-primary",onClick:U,children:"Thử lại"})]})]})})})]}),l.jsx(Ys,{toasts:E,onRemoveToast:P})]})}function bm(){const[e,t]=g.useState([]),[n,r]=g.useState([]),[s,a]=g.useState(!1),[i,o]=g.useState(!1),[u,c]=g.useState(!1),[f,v]=g.useState(null),[h,S]=g.useState(null),{toasts:N,removeToast:_,showError:L,showSuccess:p,showWarning:d}=_r();g.useEffect(()=>{m()},[]);const m=async(k=!1)=>{var D;o(!0),v(null);try{const U=await oe(k?"/api/scan/users?forceScan=true":"/api/scan/users");t(U.users||[]),p(`Tải thành công ${((D=U.users)==null?void 0:D.length)||0} người dùng`)}catch(M){console.error("Error fetching users:",M);const U=Ee(M);v(M),L(`Lỗi tải danh sách người dùng: ${U.message}`,{showDetails:!0,details:U.details,duration:8e3})}finally{o(!1)}},w=k=>{r(D=>D.some(U=>U.userId===k.userId)?D.filter(U=>U.userId!==k.userId):[...D,k])},x=()=>{n.length===e.length?r([]):r([...e])},E=async()=>{if(n.length===0){d("Vui lòng chọn ít nhất một người dùng để scan");return}c(!0),v(null);try{const k=n.map(M=>M.email),D=await Me("/api/scan/start",{scope:"selected_users",userEmails:k,forceScan:s});S(D),p(`Bắt đầu scan thành công cho ${n.length} người dùng!`),P(D.sessionId)}catch(k){console.error("Error starting scan:",k);const D=Ee(k);v(k),L(`Lỗi bắt đầu scan: ${D.message}`,{showDetails:!0,details:D.details,duration:8e3})}finally{c(!1)}},P=async k=>{const D=setInterval(async()=>{try{const M=await oe(`/api/scan/status/${k}`);if(S(M),M.status==="completed")clearInterval(D),p(`Scan hoàn thành! Tìm thấy ${M.total_files||0} file.`);else if(M.status==="failed"){clearInterval(D);const U=M.error_message||"Scan thất bại";v(new Error(U)),L(`Scan thất bại: ${U}`,{duration:8e3})}}catch(M){console.error("Error polling scan progress:",M),clearInterval(D);const U=Ee(M);v(M),L(`Lỗi kiểm tra tiến trình scan: ${U.message}`,{showDetails:!0,details:U.details,duration:8e3})}},2e3)},j=k=>k?new Date(k).toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"Chưa có thông tin",C=k=>{if(k===0)return"0 Bytes";const D=1024,M=["Bytes","KB","MB","GB","TB"],U=Math.floor(Math.log(k)/Math.log(D));return parseFloat((k/Math.pow(D,U)).toFixed(2))+" "+M[U]},y=k=>k?k.split(" ").map(D=>D.charAt(0)).join("").toUpperCase().slice(0,2):"??";return l.jsxs("div",{className:"app",children:[l.jsxs("header",{className:"app-header",children:[l.jsx("h1",{children:"📁 Google Workspace Scanner"}),l.jsx("p",{children:"Thu thập thông tin người dùng và thực hiện scan files từ Google Workspace"})]}),l.jsxs("main",{className:"app-main",children:[f&&l.jsx(Xt,{error:f,title:"Lỗi trong quá trình xử lý",onDismiss:()=>v(null),onRetry:()=>{v(null),m(s)}}),e.length>0&&l.jsxs("div",{className:"stats-overview",children:[l.jsxs("div",{className:"stats-card primary",children:[l.jsx("div",{className:"stat-number",children:e.length}),l.jsx("div",{className:"stat-label",children:"Tổng số người dùng"})]}),l.jsxs("div",{className:"stats-card success",children:[l.jsx("div",{className:"stat-number",children:e.filter(k=>!k.suspended).length}),l.jsx("div",{className:"stat-label",children:"Đang hoạt động"})]}),l.jsxs("div",{className:"stats-card warning",children:[l.jsx("div",{className:"stat-number",children:e.filter(k=>k.suspended).length}),l.jsx("div",{className:"stat-label",children:"Bị khóa"})]}),l.jsxs("div",{className:"stats-card",children:[l.jsx("div",{className:"stat-number",children:n.length}),l.jsx("div",{className:"stat-label",children:"Đã chọn"})]})]}),l.jsxs("div",{className:"controls-section",children:[l.jsx("h2",{children:"Cài đặt & Điều khiển"}),l.jsxs("div",{className:"force-scan-option",children:[l.jsxs("label",{className:"checkbox-label",children:[l.jsx("input",{type:"checkbox",checked:s,onChange:k=>a(k.target.checked),disabled:i||u}),l.jsx("span",{className:"checkbox-custom"}),l.jsx("span",{children:"Force Scan - Quét lại danh sách người dùng từ Google Workspace"})]}),l.jsx("div",{className:"force-scan-description",children:"Khi bật tùy chọn này, hệ thống sẽ quét lại toàn bộ danh sách người dùng từ Google Workspace thay vì sử dụng dữ liệu đã cache. Điều này đảm bảo dữ liệu luôn được cập nhật mới nhất nhưng có thể mất nhiều thời gian hơn."})]}),l.jsxs("div",{className:"action-buttons",children:[l.jsx("button",{onClick:()=>m(s),className:"btn btn-secondary",disabled:i||u,children:i?l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"0.5rem"}}),"Đang tải..."]}):"🔄 Tải danh sách người dùng"}),l.jsx("button",{onClick:E,className:"btn btn-primary btn-large",disabled:n.length===0||u,children:u?l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"0.5rem"}}),"Đang bắt đầu scan..."]}):`🚀 Scan files (${n.length} người dùng)`})]})]}),h&&l.jsxs("div",{className:"scan-progress-mini",children:[l.jsx("h4",{children:"📊 Tiến trình Scan"}),h.status==="running"&&l.jsxs("div",{className:"progress-bar-container",children:[l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:"progress-fill",style:{width:`${Math.min(100,(h.processed_files||0)/Math.max(1,h.total_files||1)*100)}%`}})}),l.jsxs("div",{className:"progress-text",children:[h.processed_files||0," /"," ",h.total_files||0," files"]})]}),l.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"1rem",marginTop:"1rem"},children:[l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Trạng thái:"}),l.jsxs("span",{className:`detail-value status-${h.status}`,children:[h.status==="running"&&"🔄 Đang chạy",h.status==="completed"&&"✅ Hoàn thành",h.status==="failed"&&"❌ Thất bại",h.status==="cancelled"&&"⏹️ Đã hủy"]})]}),l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Files:"}),l.jsx("span",{className:"detail-value",children:h.total_files||0})]}),l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Folders:"}),l.jsx("span",{className:"detail-value",children:h.total_folders||0})]}),h.status==="completed"&&l.jsxs("div",{className:"detail-row",children:[l.jsx("span",{className:"detail-label",children:"Kích thước:"}),l.jsx("span",{className:"detail-value",children:C(h.total_size||0)})]})]}),h.error_message&&l.jsxs("div",{style:{marginTop:"1rem",padding:"0.75rem",background:"#fee2e2",borderRadius:"6px",borderLeft:"3px solid #dc2626"},children:[l.jsx("strong",{style:{color:"#991b1b"},children:"Lỗi:"}),l.jsx("span",{style:{color:"#7f1d1d",marginLeft:"0.5rem"},children:h.error_message})]}),h.status==="completed"&&l.jsxs("div",{className:"scan-success",children:[l.jsx("h4",{children:"🎉 Scan hoàn thành thành công!"}),l.jsxs("p",{children:["Đã quét thành công ",h.total_files||0," files từ"," ",n.length," người dùng."]})]})]}),l.jsxs("div",{className:"card",children:[l.jsxs("div",{className:"selection-summary",children:[l.jsxs("div",{className:"selection-info",children:[l.jsxs("span",{children:["Tổng số người dùng: ",e.length]}),l.jsxs("span",{children:["Đã chọn: ",n.length]})]}),l.jsxs("label",{className:"select-all-checkbox",children:[l.jsx("input",{type:"checkbox",checked:n.length===e.length&&e.length>0,onChange:x,disabled:i||u}),l.jsx("span",{className:"checkbox-custom"}),l.jsx("span",{children:"Chọn tất cả"})]})]}),i?l.jsxs("div",{className:"loading-state",children:[l.jsx("div",{className:"spinner"}),l.jsx("p",{children:"Đang tải danh sách người dùng..."})]}):e.length===0?l.jsx("div",{className:"empty-state",children:l.jsx("p",{children:"Không có người dùng nào. Vui lòng thử tải lại với Force Scan."})}):l.jsxs("div",{className:"users-table",children:[l.jsxs("div",{className:"table-header",children:[l.jsx("div",{children:"Chọn"}),l.jsx("div",{children:"Người dùng"}),l.jsx("div",{children:"Email"}),l.jsx("div",{children:"Trạng thái"}),l.jsx("div",{children:"Đăng nhập cuối"}),l.jsx("div",{children:"Ngày tạo"})]}),l.jsx("div",{className:"table-body",children:e.map(k=>l.jsxs("div",{className:`table-row ${n.some(D=>D.userId===k.userId)?"selected":""}`,children:[l.jsx("div",{className:"col-select",children:l.jsxs("label",{className:"file-checkbox",children:[l.jsx("input",{type:"checkbox",checked:n.some(D=>D.userId===k.userId),onChange:()=>w(k),disabled:i||u}),l.jsx("span",{className:"checkbox-custom"})]})}),l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.75rem"},children:[l.jsx("div",{className:"user-avatar",style:{width:"40px",height:"40px",borderRadius:"50%",background:k.suspended?"linear-gradient(135deg, #ef4444, #dc2626)":"linear-gradient(135deg, #3b82f6, #1d4ed8)",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"0.875rem",fontWeight:"600",flexShrink:0},children:y(k.fullName)}),l.jsxs("div",{children:[l.jsx("div",{className:"file-name",children:k.fullName||"Chưa có tên"}),l.jsxs("div",{className:"file-path",children:[k.givenName," ",k.familyName]})]})]}),l.jsxs("div",{children:[l.jsx("div",{className:"file-name",children:k.email}),l.jsxs("div",{className:"file-path",children:["ID: ",k.userId]})]}),l.jsx("div",{children:l.jsx("span",{className:`user-status ${k.suspended?"suspended":"active"}`,style:{padding:"0.25rem 0.75rem",borderRadius:"20px",fontSize:"0.75rem",fontWeight:"500",background:k.suspended?"#fee2e2":"#dcfce7",color:k.suspended?"#991b1b":"#166534"},children:k.suspended?"🔒 Bị khóa":"✅ Hoạt động"})}),l.jsx("div",{children:l.jsx("div",{style:{fontSize:"0.875rem"},children:j(k.lastLoginTime)})}),l.jsx("div",{children:l.jsx("div",{style:{fontSize:"0.875rem"},children:j(k.createdAt)})})]},k.userId))})]})]})]}),l.jsx(Ys,{toasts:N,onRemoveToast:_})]})}function Qm(){const[e,t]=g.useState([]),[n,r]=g.useState(!1),[s,a]=g.useState(null),[i,o]=g.useState(null),[u,c]=g.useState(""),[f,v]=g.useState("drive_usage_bytes"),[h,S]=g.useState("desc"),[N,_]=g.useState(1),[L,p]=g.useState(50),[d,m]=g.useState(null),[w,x]=g.useState("E:\\"),[E,P]=g.useState(new Set),[j,C]=g.useState(!1),[y,k]=g.useState(!1),[D,M]=g.useState(!1),{showError:U,showSuccess:Ye,showWarning:Xe}=_r();g.useEffect(()=>{V()},[u,f,h,N,L]);const V=async()=>{r(!0),a(null);try{const T=new URLSearchParams({search:u,sortBy:f,sortOrder:h,page:N.toString(),pageSize:L.toString()}),I=await oe(`/api/storage/stats?${T}`);t(I.users),m(I.pagination),o(I.overallStats)}catch(T){console.error("Error loading storage data:",T);const I=Ee(T);a(T),U(`Lỗi tải dữ liệu: ${I.message}`,{showDetails:!0,details:I.details})}finally{r(!1)}},R=async()=>{C(!0);try{let T=[];if(e.length>0)T=e.map(G=>G.user_email);else try{const G=await oe("/api/scan/users");if(G.users&&G.users.length>0)T=G.users.map(Ue=>Ue.email);else{Xe("Không có user nào trong hệ thống. Vui lòng scan users trước.");return}}catch{U("Không thể lấy danh sách users. Vui lòng scan users trước.");return}Ye(`Bắt đầu scan storage cho ${T.length} users...`);const I=await Me("/api/storage/scan",{userEmails:T,forceRefresh:!0});Ye(`Scan hoàn thành: ${I.results.successful}/${I.results.totalUsers} thành công`),I.results.errors.length>0&&Xe(`${I.results.errors.length} users gặp lỗi khi scan`),await V()}catch(T){console.error("Error scanning storage:",T);const I=Ee(T);U(`Lỗi scan storage: ${I.message}`,{showDetails:!0,details:I.details})}finally{C(!1)}},$=async()=>{k(!0);try{Ye("Bắt đầu tính toán dung lượng local cho tất cả users...");const T=await Me("/api/storage/calculate-local-all",{folderPath:w});Ye(`Tính toán hoàn thành: ${T.results.successful}/${T.results.totalUsers} thành công. Tổng: ${T.results.totalSizeFormatted}`),T.results.errors.length>0&&Xe(`${T.results.errors.length} users gặp lỗi khi tính toán`),await V()}catch(T){console.error("Error calculating all local sizes:",T);const I=Ee(T);U(`Lỗi tính toán dung lượng local: ${I.message}`,{showDetails:!0,details:I.details})}finally{k(!1)}},O=async T=>{P(I=>new Set(I).add(T));try{const I=await Me(`/api/storage/calculate-local/${T}`,{folderPath:w});Ye(`Đã tính toán dung lượng local cho ${T}: ${I.folderSizeFormatted}`),t(G=>G.map(Ue=>Ue.user_email===T?{...Ue,local_downloaded_bytes:I.folderSize,local_folder_path:w}:Ue))}catch(I){console.error("Error calculating local size:",I);const G=Ee(I);U(`Lỗi tính toán dung lượng local: ${G.message}`,{showDetails:!0,details:G.details})}finally{P(I=>{const G=new Set(I);return G.delete(T),G})}},J=async()=>{M(!0);try{const T=await Me("/api/storage/recalculate-scanned",{});T.success&&(Ye(`Đã tính toán lại scanned storage cho ${T.results.totalUsers} users`),await V())}catch(T){console.error("Error recalculating scanned storage:",T);const I=Ee(T);U(`Lỗi tính toán lại scanned storage: ${I.message}`,{showDetails:!0,details:I.details})}finally{M(!1)}},H=T=>{if(!T||T===0)return"0 B";const I=1024,G=["B","KB","MB","GB","TB"],Ue=Math.floor(Math.log(T)/Math.log(I));return parseFloat((T/Math.pow(I,Ue)).toFixed(2))+" "+G[Ue]},qt=T=>{const I=T.drive_usage_bytes||0,G=T.local_downloaded_bytes||0;return Math.max(0,I-G)},rt=T=>{const I=T.drive_usage_bytes||0,G=T.local_downloaded_bytes||0;return I===0?0:Math.min(100,G/I*100)},Dn=T=>T>=90?"#4CAF50":T>=70?"#FF9800":"#F44336";return l.jsxs("div",{className:"storage-comparison",children:[l.jsxs("div",{className:"page-header",children:[l.jsx("h1",{children:"📊 So sánh dung lượng Storage"}),l.jsx("p",{children:"Đối chiếu dung lượng đã download vs dung lượng trên Google Drive"})]}),l.jsxs("div",{className:"controls-section",children:[l.jsxs("div",{className:"controls-row",children:[l.jsx("div",{className:"search-box",children:l.jsx("input",{type:"text",placeholder:"Tìm kiếm theo email...",value:u,onChange:T=>c(T.target.value),className:"search-input"})}),l.jsxs("div",{className:"sort-controls",children:[l.jsxs("select",{value:f,onChange:T=>v(T.target.value),className:"sort-select",children:[l.jsx("option",{value:"drive_usage_bytes",children:"Dung lượng Drive"}),l.jsx("option",{value:"scanned_storage_bytes",children:"Đã scan"}),l.jsx("option",{value:"local_downloaded_bytes",children:"Đã download"}),l.jsx("option",{value:"user_email",children:"Email"}),l.jsx("option",{value:"last_scanned_at",children:"Lần scan cuối"})]}),l.jsxs("select",{value:h,onChange:T=>S(T.target.value),className:"sort-select",children:[l.jsx("option",{value:"desc",children:"Giảm dần"}),l.jsx("option",{value:"asc",children:"Tăng dần"})]})]}),l.jsx("button",{onClick:R,disabled:j||n,className:"btn btn-primary",children:j?"🔄 Đang scan...":"🔍 Scan Storage"}),l.jsx("button",{onClick:$,disabled:y||n,className:"btn btn-secondary",children:y?"🔄 Đang tính toán...":"📊 Tính toán tất cả Local"})]}),l.jsx("div",{className:"controls-row",children:l.jsxs("div",{className:"folder-path-setting",children:[l.jsx("label",{children:"Đường dẫn thư mục local:"}),l.jsx("input",{type:"text",value:w,onChange:T=>x(T.target.value),placeholder:"E:\\",className:"folder-path-input"})]})})]}),i&&l.jsxs("div",{className:"overall-stats",children:[l.jsxs("div",{className:"stats-header",children:[l.jsx("h3",{children:"📈 Thống kê tổng quan"}),l.jsx("button",{onClick:J,disabled:D,className:"btn btn-small btn-secondary",title:"Tính toán lại scanned storage từ bảng scanned_files",children:D?"🔄 Đang tính...":"🔄 Tính lại Scanned"})]}),l.jsxs("div",{className:"stats-grid",children:[l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Tổng số users:"}),l.jsx("span",{className:"stat-value",children:i.totalUsers})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Tổng dung lượng Drive:"}),l.jsx("span",{className:"stat-value",children:H(i.totalDriveUsed)})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Tổng đã scan:"}),l.jsx("span",{className:"stat-value",children:H(i.totalScannedStorage||0)})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Còn thiếu scan:"}),l.jsxs("span",{className:"stat-value",children:[H(Math.max(0,(i.totalDriveUsed||0)-(i.totalScannedStorage||0))),i.totalDriveUsed>0&&l.jsxs("div",{style:{fontSize:"0.8rem",opacity:.9,color:"#6c757d"},children:["(",(Math.max(0,(i.totalDriveUsed||0)-(i.totalScannedStorage||0))/(i.totalDriveUsed||1)*100).toFixed(1),"%)"]})]})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Tổng đã download:"}),l.jsx("span",{className:"stat-value",children:H(i.totalDownloaded||0)})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Còn thiếu download:"}),l.jsxs("span",{className:"stat-value",children:[H(Math.max(0,(i.totalDriveUsed||0)-(i.totalDownloaded||0))),i.totalDriveUsed>0&&l.jsxs("div",{style:{fontSize:"0.8rem",opacity:.9,color:"#6c757d"},children:["(",(Math.max(0,(i.totalDriveUsed||0)-(i.totalDownloaded||0))/(i.totalDriveUsed||1)*100).toFixed(1),"%)"]})]})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-label",children:"Users có lỗi:"}),l.jsx("span",{className:"stat-value error",children:i.usersWithErrors})]})]})]}),l.jsxs("div",{className:"users-table-container",children:[n&&l.jsx("div",{className:"loading",children:"🔄 Đang tải dữ liệu..."}),s&&l.jsx("div",{className:"error-message",children:"❌ Có lỗi xảy ra khi tải dữ liệu"}),!n&&!s&&e.length===0&&l.jsx("div",{className:"no-data",children:"📭 Không có dữ liệu storage. Vui lòng scan storage trước."}),!n&&!s&&e.length>0&&l.jsxs(l.Fragment,{children:[l.jsxs("table",{className:"users-table",children:[l.jsx("thead",{children:l.jsxs("tr",{children:[l.jsx("th",{children:"Email"}),l.jsx("th",{children:"Drive Usage"}),l.jsx("th",{children:"Scanned Storage"}),l.jsx("th",{children:"% Đã scan"}),l.jsx("th",{children:"Còn thiếu scan"}),l.jsx("th",{children:"Downloaded"}),l.jsx("th",{children:"Missing"}),l.jsx("th",{children:"Progress"}),l.jsx("th",{children:"Actions"})]})}),l.jsx("tbody",{children:e.map(T=>{const I=qt(T),G=rt(T),Ue=E.has(T.user_email);return l.jsxs("tr",{children:[l.jsxs("td",{className:"email-cell",children:[l.jsxs("div",{className:"email-info",children:[l.jsx("span",{className:"email",children:T.user_email}),T.scan_error_message&&l.jsx("span",{className:"error-indicator",title:T.scan_error_message,children:"⚠️"})]}),T.last_scanned_at&&l.jsxs("div",{className:"last-scanned",children:["Scan: ",new Date(T.last_scanned_at).toLocaleString()]})]}),l.jsx("td",{children:H(T.drive_usage_bytes)}),l.jsx("td",{className:"scanned-cell",children:H(T.scanned_storage_bytes||0)}),l.jsx("td",{className:"scan-percentage-cell",children:T.drive_usage_bytes>0?`${((T.scanned_storage_bytes||0)/T.drive_usage_bytes*100).toFixed(1)}%`:"0%"}),l.jsx("td",{className:"missing-scan-cell",children:H(Math.max(0,(T.drive_usage_bytes||0)-(T.scanned_storage_bytes||0)))}),l.jsxs("td",{className:"downloaded-cell",children:[l.jsx("span",{children:H(T.local_downloaded_bytes)}),T.local_folder_path&&l.jsxs("div",{className:"folder-path",children:["📁 ",T.local_folder_path,T.user_email]})]}),l.jsx("td",{className:I>0?"missing-bytes":"complete",children:H(I)}),l.jsx("td",{children:l.jsxs("div",{className:"progress-container",children:[l.jsx("div",{className:"progress-bar",style:{width:`${G}%`,backgroundColor:Dn(G)}}),l.jsxs("span",{className:"progress-text",children:[G.toFixed(1),"%"]})]})}),l.jsx("td",{children:l.jsx("button",{onClick:()=>O(T.user_email),disabled:Ue,className:"btn btn-small",title:"Tính toán dung lượng đã download",children:Ue?"🔄":"📊"})})]},T.user_email)})})]}),d&&d.totalPages>1&&l.jsxs("div",{className:"pagination",children:[l.jsx("button",{onClick:()=>_(N-1),disabled:N<=1,className:"btn btn-small",children:"← Trước"}),l.jsxs("span",{className:"page-info",children:["Trang ",N," / ",d.totalPages,"(",d.totalCount," users)"]}),l.jsx("button",{onClick:()=>_(N+1),disabled:N>=d.totalPages,className:"btn btn-small",children:"Sau →"})]})]})]})]})}function Km(){return l.jsx(Om,{children:l.jsx(vm,{children:l.jsxs("div",{className:"main-app",children:[l.jsx(Cm,{}),l.jsx("div",{className:"main-content",children:l.jsxs(Qp,{children:[l.jsx(Ut,{path:"/",element:l.jsx(Vp,{to:"/google-workspace-scanner",replace:!0})}),l.jsx(Ut,{path:"/google-workspace-scanner",element:l.jsx(bm,{})}),l.jsx(Ut,{path:"/migration",element:l.jsx(zm,{})}),l.jsx(Ut,{path:"/download",element:l.jsx(Bm,{})}),l.jsx(Ut,{path:"/users",element:l.jsx(Vm,{})}),l.jsx(Ut,{path:"/storage",element:l.jsx(Qm,{})})]})})]})})})}kl.createRoot(document.getElementById("root")).render(l.jsx(Dd.StrictMode,{children:l.jsx(Km,{})}));
//# sourceMappingURL=index-BWzh2b1y.js.map
