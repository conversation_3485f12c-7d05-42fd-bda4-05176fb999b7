/* Upload Report Styles */
.upload-report {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
    background-color: white;
}

.upload-report.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Header */
.report-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.session-info h2 {
    margin: 0 0 10px 0;
    color: #1f2937;
    font-size: 1.8rem;
    font-weight: 700;
}

.session-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background-color: #f3f4f6;
    color: #6b7280;
}

.status-running {
    background-color: #dbeafe;
    color: #1d4ed8;
}

.status-paused {
    background-color: #fef3c7;
    color: #d97706;
}

.status-completed {
    background-color: #d1fae5;
    color: #059669;
}

.status-failed {
    background-color: #fee2e2;
    color: #dc2626;
}

.status-cancelled {
    background-color: #f3f4f6;
    color: #6b7280;
}

.session-date {
    font-size: 0.9rem;
    color: #6b7280;
}

.report-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Statistics Overview */
.statistics-overview {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #e2e8f0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-left: 4px solid;
}

.stat-card.success {
    border-left-color: #10b981;
}

.stat-card.error {
    border-left-color: #ef4444;
}

.stat-card.skipped {
    border-left-color: #f59e0b;
}

.stat-card.total {
    border-left-color: #3b82f6;
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.stats-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #374151;
}

.detail-value {
    font-weight: 600;
    color: #1f2937;
}

/* Filters Section */
.filters-section {
    background-color: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e2e8f0;
}

.filters-header {
    margin-bottom: 15px;
}

.filters-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.2rem;
    font-weight: 600;
}

.filters-controls {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 20px;
    flex-wrap: wrap;
}

.filters-left {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filters-right {
    flex: 1;
    max-width: 300px;
    min-width: 200px;
}

.filter-group,
.search-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.filter-group select,
.search-input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
}

.search-input {
    width: 100%;
}

/* Items Table */
.items-table-container {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.table-header {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.table-header h3 {
    margin: 0;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 600;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.items-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
}

.items-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
}

.items-table tr {
    transition: all 0.2s ease;
}

.items-table tr:hover {
    background-color: #f8fafc;
}

/* Table row status styling */
.items-table tr.uploaded {
    border-left: 4px solid #10b981;
}

.items-table tr.failed {
    border-left: 4px solid #ef4444;
}

.items-table tr.skipped {
    border-left: 4px solid #f59e0b;
}

/* Table cell styling */
.status-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
}

.status-icon {
    font-size: 1.1rem;
}

.status-text {
    font-weight: 500;
    text-transform: capitalize;
}

.file-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.file-name {
    font-weight: 600;
    color: #1f2937;
}

.lark-info {
    font-size: 0.8rem;
    color: #6b7280;
    font-family: monospace;
}

.error-cell .error-message {
    color: #dc2626;
    font-size: 0.8rem;
    background-color: #fee2e2;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: help;
}

.no-results {
    padding: 40px;
    text-align: center;
    color: #6b7280;
    font-style: italic;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info-left {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #374151;
}

.items-per-page select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.pagination-info {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .filters-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filters-left {
        justify-content: space-between;
    }

    .filters-right {
        max-width: none;
    }
}

@media (max-width: 768px) {
    .upload-report {
        padding: 20px;
    }

    .report-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .report-actions {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .stats-details {
        grid-template-columns: 1fr;
    }

    .filters-left {
        flex-direction: column;
        gap: 10px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 15px;
    }

    .items-table {
        font-size: 0.8rem;
    }

    .items-table th,
    .items-table td {
        padding: 8px 10px;
    }

    .table-header-row,
    .table-row {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: left;
    }

    .table-cell {
        padding: 5px 0;
        border-bottom: 1px solid #f3f4f6;
    }

    .table-cell:last-child {
        border-bottom: none;
    }

    .table-cell::before {
        content: attr(data-label);
        font-weight: 600;
        color: #6b7280;
        display: block;
        margin-bottom: 2px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .upload-report {
        padding: 15px;
    }

    .session-info h2 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .report-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}