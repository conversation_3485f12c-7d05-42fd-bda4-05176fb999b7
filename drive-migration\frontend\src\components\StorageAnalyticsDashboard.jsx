import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import { formatBytes, formatPercentage, formatDate } from '../utils/formatUtils';
import { useToast } from '../contexts/ToastContext';
import StorageErrorsModal from './StorageErrorsModal';
import StorageDiscrepancyModal from './StorageDiscrepancyModal';
import FileSizeAnalysisModal from './FileSizeAnalysisModal';
import './StorageAnalyticsDashboard.css';

const StorageAnalyticsDashboard = ({ isOpen, onClose, defaultFolderPath = 'E:\\' }) => {
    const [dashboardData, setDashboardData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [activeModal, setActiveModal] = useState(null);
    const { showError, showSuccess } = useToast();

    useEffect(() => {
        if (isOpen) {
            loadDashboardData();
        }
    }, [isOpen]);

    const loadDashboardData = async () => {
        setLoading(true);
        try {
            // Load multiple data sources in parallel
            const [storageStats, storageErrors, discrepancyAnalysis, fileSizeAnalysis] = await Promise.all([
                apiGet('/api/storage/stats?pageSize=1000'),
                apiGet('/api/storage/errors'),
                apiGet(`/api/storage/analyze-discrepancies?folderPath=${encodeURIComponent(defaultFolderPath)}`),
                apiGet('/api/storage/analyze-file-sizes?limit=1000')
            ]);

            const dashboard = {
                storageStats: storageStats.overallStats,
                users: storageStats.users,
                errors: storageErrors.errors,
                errorSummary: storageErrors.summary,
                discrepancy: discrepancyAnalysis.analysis,
                fileSizeAnalysis: fileSizeAnalysis.analysis,
                timestamp: new Date().toISOString()
            };

            // Calculate additional insights
            dashboard.insights = calculateInsights(dashboard);

            setDashboardData(dashboard);
            showSuccess('Dashboard data loaded successfully');
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            showError('Không thể tải dữ liệu dashboard');
        } finally {
            setLoading(false);
        }
    };

    const calculateInsights = (data) => {
        const insights = {
            criticalIssues: [],
            recommendations: [],
            healthScore: 100,
            summary: {
                totalIssues: 0,
                criticalIssues: 0,
                warningIssues: 0,
                infoIssues: 0
            }
        };

        // Check for critical issues
        if (data.errors.length > 0) {
            insights.criticalIssues.push({
                type: 'error',
                severity: 'critical',
                title: `${data.errors.length} users có lỗi scan storage`,
                description: 'Một số users không thể scan được storage, cần kiểm tra quyền truy cập',
                action: 'view_errors'
            });
            insights.healthScore -= 20;
        }

        // Check for large discrepancies
        if (data.discrepancy && Math.abs(data.discrepancy.totalDiscrepancy) > 50 * 1024 * 1024 * 1024) { // 50GB
            insights.criticalIssues.push({
                type: 'discrepancy',
                severity: 'critical',
                title: `Chênh lệch dung lượng lớn: ${formatBytes(Math.abs(data.discrepancy.totalDiscrepancy))}`,
                description: 'Có sự chênh lệch đáng kể giữa dung lượng thống kê và thực tế',
                action: 'view_discrepancy'
            });
            insights.healthScore -= 25;
        }

        // Check for users with significant discrepancies
        if (data.discrepancy && data.discrepancy.summary.usersWithDiscrepancies > 0) {
            insights.recommendations.push({
                type: 'warning',
                severity: 'warning',
                title: `${data.discrepancy.summary.usersWithDiscrepancies} users có chênh lệch đáng kể`,
                description: 'Cần kiểm tra và tính toán lại dung lượng local cho các users này',
                action: 'view_discrepancy'
            });
            insights.healthScore -= 10;
        }

        // Check for Google Docs conversion issues
        if (data.fileSizeAnalysis && data.fileSizeAnalysis.summary.filesWithSizeIncrease > 0) {
            insights.recommendations.push({
                type: 'info',
                severity: 'info',
                title: `${data.fileSizeAnalysis.summary.filesWithSizeIncrease} files tăng size sau conversion`,
                description: 'Google Docs files thường tăng size khi convert sang Office format',
                action: 'view_file_analysis'
            });
            insights.healthScore -= 5;
        }

        // Calculate summary
        insights.summary.criticalIssues = insights.criticalIssues.length;
        insights.summary.warningIssues = insights.recommendations.filter(r => r.severity === 'warning').length;
        insights.summary.infoIssues = insights.recommendations.filter(r => r.severity === 'info').length;
        insights.summary.totalIssues = insights.summary.criticalIssues + insights.summary.warningIssues + insights.summary.infoIssues;

        return insights;
    };

    const getSeverityColor = (severity) => {
        switch (severity) {
            case 'critical': return '#dc3545';
            case 'warning': return '#ffc107';
            case 'info': return '#17a2b8';
            default: return '#6c757d';
        }
    };

    const getSeverityIcon = (severity) => {
        switch (severity) {
            case 'critical': return '🚨';
            case 'warning': return '⚠️';
            case 'info': return 'ℹ️';
            default: return '📋';
        }
    };

    const handleActionClick = (action) => {
        switch (action) {
            case 'view_errors':
                setActiveModal('errors');
                break;
            case 'view_discrepancy':
                setActiveModal('discrepancy');
                break;
            case 'view_file_analysis':
                setActiveModal('file_analysis');
                break;
            default:
                break;
        }
    };

    const getHealthScoreColor = (score) => {
        if (score >= 80) return '#28a745';
        if (score >= 60) return '#ffc107';
        return '#dc3545';
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content storage-analytics-dashboard" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>📊 Storage Analytics Dashboard</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>

                <div className="modal-body">
                    {loading ? (
                        <div className="loading-state">
                            <div className="spinner"></div>
                            <p>Đang tải dữ liệu dashboard...</p>
                        </div>
                    ) : dashboardData ? (
                        <>
                            {/* Health Score */}
                            <div className="health-score-section">
                                <div className="health-score-card">
                                    <div className="health-score-header">
                                        <h3>System Health Score</h3>
                                        <div
                                            className="health-score-value"
                                            style={{ color: getHealthScoreColor(dashboardData.insights.healthScore) }}
                                        >
                                            {dashboardData.insights.healthScore}/100
                                        </div>
                                    </div>
                                    <div className="health-score-bar">
                                        <div
                                            className="health-score-fill"
                                            style={{
                                                width: `${dashboardData.insights.healthScore}%`,
                                                backgroundColor: getHealthScoreColor(dashboardData.insights.healthScore)
                                            }}
                                        ></div>
                                    </div>
                                    <div className="issues-summary">
                                        <span className="critical-count">
                                            🚨 {dashboardData.insights.summary.criticalIssues} Critical
                                        </span>
                                        <span className="warning-count">
                                            ⚠️ {dashboardData.insights.summary.warningIssues} Warning
                                        </span>
                                        <span className="info-count">
                                            ℹ️ {dashboardData.insights.summary.infoIssues} Info
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Key Metrics */}
                            <div className="key-metrics-section">
                                <h3>Key Metrics</h3>
                                <div className="metrics-grid">
                                    <div className="metric-card">
                                        <div className="metric-value">{dashboardData.storageStats.totalUsers}</div>
                                        <div className="metric-label">Total Users</div>
                                    </div>
                                    <div className="metric-card">
                                        <div className="metric-value">{formatBytes(dashboardData.storageStats.totalDriveUsed)}</div>
                                        <div className="metric-label">Total Drive Usage</div>
                                    </div>
                                    <div className="metric-card">
                                        <div className="metric-value">{formatBytes(dashboardData.storageStats.totalScannedStorage)}</div>
                                        <div className="metric-label">Total Scanned</div>
                                    </div>
                                    <div className="metric-card">
                                        <div className="metric-value">{formatBytes(dashboardData.storageStats.totalDownloaded)}</div>
                                        <div className="metric-label">Total Downloaded</div>
                                    </div>
                                    <div className="metric-card">
                                        <div className="metric-value">{dashboardData.storageStats.usersWithErrors}</div>
                                        <div className="metric-label">Users with Errors</div>
                                    </div>
                                    <div className="metric-card">
                                        <div className="metric-value">
                                            {dashboardData.discrepancy ? formatBytes(Math.abs(dashboardData.discrepancy.totalDiscrepancy)) : 'N/A'}
                                        </div>
                                        <div className="metric-label">Storage Discrepancy</div>
                                    </div>
                                </div>
                            </div>

                            {/* Critical Issues */}
                            {dashboardData.insights.criticalIssues.length > 0 && (
                                <div className="issues-section critical-issues">
                                    <h3>🚨 Critical Issues</h3>
                                    {dashboardData.insights.criticalIssues.map((issue, index) => (
                                        <div key={index} className="issue-card critical">
                                            <div className="issue-header">
                                                <span className="issue-icon">{getSeverityIcon(issue.severity)}</span>
                                                <span className="issue-title">{issue.title}</span>
                                                <button
                                                    className="issue-action-btn"
                                                    onClick={() => handleActionClick(issue.action)}
                                                >
                                                    View Details
                                                </button>
                                            </div>
                                            <div className="issue-description">{issue.description}</div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {/* Recommendations */}
                            {dashboardData.insights.recommendations.length > 0 && (
                                <div className="issues-section recommendations">
                                    <h3>💡 Recommendations</h3>
                                    {dashboardData.insights.recommendations.map((rec, index) => (
                                        <div key={index} className={`issue-card ${rec.severity}`}>
                                            <div className="issue-header">
                                                <span className="issue-icon">{getSeverityIcon(rec.severity)}</span>
                                                <span className="issue-title">{rec.title}</span>
                                                <button
                                                    className="issue-action-btn"
                                                    onClick={() => handleActionClick(rec.action)}
                                                >
                                                    View Details
                                                </button>
                                            </div>
                                            <div className="issue-description">{rec.description}</div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {/* Quick Actions */}
                            <div className="quick-actions-section">
                                <h3>Quick Actions</h3>
                                <div className="quick-actions-grid">
                                    <button
                                        className="quick-action-btn errors"
                                        onClick={() => setActiveModal('errors')}
                                    >
                                        <span className="action-icon">🚨</span>
                                        <span className="action-label">View Storage Errors</span>
                                        <span className="action-count">{dashboardData.errors.length}</span>
                                    </button>
                                    <button
                                        className="quick-action-btn discrepancy"
                                        onClick={() => setActiveModal('discrepancy')}
                                    >
                                        <span className="action-icon">📊</span>
                                        <span className="action-label">Analyze Discrepancies</span>
                                        <span className="action-count">
                                            {dashboardData.discrepancy ? dashboardData.discrepancy.summary.usersWithDiscrepancies : 0}
                                        </span>
                                    </button>
                                    <button
                                        className="quick-action-btn file-analysis"
                                        onClick={() => setActiveModal('file_analysis')}
                                    >
                                        <span className="action-icon">📁</span>
                                        <span className="action-label">File Size Analysis</span>
                                        <span className="action-count">
                                            {dashboardData.fileSizeAnalysis ? dashboardData.fileSizeAnalysis.summary.filesWithSizeIncrease : 0}
                                        </span>
                                    </button>
                                </div>
                            </div>

                            <div className="dashboard-footer">
                                <p>Last updated: {formatDate(dashboardData.timestamp)}</p>
                            </div>
                        </>
                    ) : (
                        <div className="no-data">
                            <p>Nhấn "Refresh" để tải dữ liệu dashboard</p>
                        </div>
                    )}
                </div>

                <div className="modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>
                        Đóng
                    </button>
                    <button
                        className="btn btn-primary"
                        onClick={loadDashboardData}
                        disabled={loading}
                    >
                        Refresh
                    </button>
                </div>

                {/* Sub-modals */}
                <StorageErrorsModal
                    isOpen={activeModal === 'errors'}
                    onClose={() => setActiveModal(null)}
                />
                <StorageDiscrepancyModal
                    isOpen={activeModal === 'discrepancy'}
                    onClose={() => setActiveModal(null)}
                    defaultFolderPath={defaultFolderPath}
                />
                <FileSizeAnalysisModal
                    isOpen={activeModal === 'file_analysis'}
                    onClose={() => setActiveModal(null)}
                />
            </div>
        </div>
    );
};

export default StorageAnalyticsDashboard;
