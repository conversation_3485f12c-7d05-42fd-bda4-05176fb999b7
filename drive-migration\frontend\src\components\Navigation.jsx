import { useState } from "react";
import { NavLink } from "react-router-dom";
import "./Navigation.css";

function Navigation() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navItems = [
    {
      path: "/google-workspace-scanner",
      label: "1. Google Workspace Scanner",
      icon: "📁",
      description:
        "Thu thập thông tin người dùng và thực hiện scan files từ Google Workspace",
    },
    {
      path: "/download",
      label: "2. Download Files",
      icon: "📥",
      description: "Download files từ Google Drive về local",
    },
    {
      path: "/upload",
      label: "3. Upload to Lark",
      icon: "📤",
      description: "Upload files từ local lên Lark Drive",
    },
    {
      path: "/storage",
      label: "Storage Comparison",
      icon: "📊",
      description: "So sánh dung lượng đã download vs Google Drive",
    },
    // {
    //     path: "/migration",
    //     label: "Migration Tool",
    //     icon: "🚀",
    //     description: "Công cụ migration file từ Drive sang Lark",
    // },
    // {
    //     path: "/users",
    //     label: "Users Overview",
    //     icon: "👥",
    //     description: "Xem tổng quan users và file structure",
    // },
  ];

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <nav className="navigation">
      <div className="nav-container">
        {/* Logo/Brand */}
        <div className="nav-brand">
          <div className="brand-icon">🏢</div>
          <div className="brand-text">
            <div className="brand-title">Drive to Lark</div>
            <div className="brand-subtitle">Migration Platform</div>
          </div>
        </div>

        {/* Desktop Navigation */}
        <div className="nav-links">
          {navItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `nav-link ${isActive ? "active" : ""}`
              }
              title={item.description}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-text">{item.label}</span>
            </NavLink>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="nav-actions">
          <button className="action-btn" title="Thông báo">
            🔔
          </button>
          <button className="action-btn" title="Cài đặt">
            ⚙️
          </button>
          <div className="user-menu">
            <button className="user-avatar" title="User menu">
              👤
            </button>
          </div>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-btn"
          onClick={toggleMobileMenu}
          aria-label="Toggle mobile menu"
        >
          {mobileMenuOpen ? "✕" : "☰"}
        </button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="mobile-menu">
          <div className="mobile-menu-content">
            {navItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `mobile-nav-link ${isActive ? "active" : ""}`
                }
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="mobile-nav-icon">{item.icon}</span>
                <div className="mobile-nav-text">
                  <div className="mobile-nav-label">{item.label}</div>
                  <div className="mobile-nav-desc">{item.description}</div>
                </div>
              </NavLink>
            ))}

            <div className="mobile-actions">
              <button className="mobile-action-btn">🔔 Thông báo</button>
              <button className="mobile-action-btn">⚙️ Cài đặt</button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}

export default Navigation;
