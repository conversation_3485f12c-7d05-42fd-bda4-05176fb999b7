-- Script to refresh materialized views for performance optimization
-- Run this script periodically (e.g., every hour or after bulk data changes)

-- Refresh file statistics materialized view
REFRESH MATERIALIZED VIEW CONCURRENTLY mv_file_stats;

-- Refresh user summary materialized view  
REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_summary;

-- Check last refresh time
SELECT 
    schemaname,
    matviewname,
    hasindexes,
    ispopulated,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||matviewname)) as size
FROM pg_matviews 
WHERE schemaname = 'public';
