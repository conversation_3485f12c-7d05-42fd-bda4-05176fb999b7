const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

class DuplicateScanner {
    constructor() {
        this.duplicateGroups = [];
        this.processedFiles = 0;
        this.totalFiles = 0;
        this.currentDirectory = '';
    }

    async scanDirectory(rootPath, progressCallback) {
        try {
            this.duplicateGroups = [];
            this.processedFiles = 0;
            this.totalFiles = 0;
            this.currentDirectory = '';

            // First pass: count total files
            await this.countFiles(rootPath);
            
            if (progressCallback) {
                progressCallback({
                    phase: 'counting',
                    message: `Found ${this.totalFiles} files to process`,
                    processed: 0,
                    total: this.totalFiles
                });
            }

            // Second pass: scan for duplicates
            await this.scanDirectoryRecursive(rootPath, progressCallback);

            return this.duplicateGroups;
        } catch (error) {
            throw new Error(`Error scanning directory: ${error.message}`);
        }
    }

    async countFiles(dirPath) {
        try {
            const items = await fs.readdir(dirPath);
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const stats = await fs.stat(fullPath);
                
                if (stats.isDirectory()) {
                    await this.countFiles(fullPath);
                } else if (stats.isFile()) {
                    this.totalFiles++;
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not access ${dirPath}: ${error.message}`);
        }
    }

    async scanDirectoryRecursive(dirPath, progressCallback) {
        try {
            this.currentDirectory = dirPath;
            
            if (progressCallback) {
                progressCallback({
                    phase: 'scanning',
                    message: `Scanning: ${dirPath}`,
                    processed: this.processedFiles,
                    total: this.totalFiles,
                    currentDir: dirPath
                });
            }

            const items = await fs.readdir(dirPath);
            const files = [];
            const subdirectories = [];

            // Separate files and directories
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const stats = await fs.stat(fullPath);
                
                if (stats.isDirectory()) {
                    subdirectories.push(fullPath);
                } else if (stats.isFile()) {
                    files.push({
                        name: item,
                        path: fullPath,
                        size: stats.size
                    });
                    this.processedFiles++;
                }
            }

            // Check for duplicates in current directory
            await this.findDuplicatesInDirectory(files, progressCallback);

            // Recursively scan subdirectories
            for (const subdir of subdirectories) {
                await this.scanDirectoryRecursive(subdir, progressCallback);
            }

        } catch (error) {
            console.warn(`Warning: Could not scan ${dirPath}: ${error.message}`);
        }
    }

    async findDuplicatesInDirectory(files, progressCallback) {
        // Group files by potential duplicate patterns
        const potentialDuplicates = new Map();

        for (const file of files) {
            const baseName = this.getBaseName(file.name);
            if (baseName) {
                if (!potentialDuplicates.has(baseName)) {
                    potentialDuplicates.set(baseName, []);
                }
                potentialDuplicates.get(baseName).push(file);
            }
        }

        // Check each group for actual duplicates
        for (const [baseName, fileGroup] of potentialDuplicates) {
            if (fileGroup.length > 1) {
                await this.checkForDuplicates(fileGroup, progressCallback);
            }
        }
    }

    getBaseName(fileName) {
        // Extract base name for files ending with _1, _2, etc.
        const match = fileName.match(/^(.+)_\d+(\.[^.]+)?$/);
        if (match) {
            return match[1] + (match[2] || '');
        }
        return null;
    }

    async checkForDuplicates(fileGroup, progressCallback) {
        // Group by size first
        const sizeGroups = new Map();
        
        for (const file of fileGroup) {
            if (!sizeGroups.has(file.size)) {
                sizeGroups.set(file.size, []);
            }
            sizeGroups.get(file.size).push(file);
        }

        // Check hash for files with same size
        for (const [size, sameSize] of sizeGroups) {
            if (sameSize.length > 1) {
                const hashGroups = new Map();
                
                for (const file of sameSize) {
                    try {
                        const hash = await this.calculateFileHash(file.path);
                        if (!hashGroups.has(hash)) {
                            hashGroups.set(hash, []);
                        }
                        hashGroups.get(hash).push(file);
                        
                        if (progressCallback) {
                            progressCallback({
                                phase: 'hashing',
                                message: `Calculating hash for: ${file.name}`,
                                processed: this.processedFiles,
                                total: this.totalFiles,
                                currentFile: file.name
                            });
                        }
                    } catch (error) {
                        console.warn(`Warning: Could not hash ${file.path}: ${error.message}`);
                    }
                }

                // Add duplicate groups
                for (const [hash, duplicates] of hashGroups) {
                    if (duplicates.length > 1) {
                        this.duplicateGroups.push({
                            hash,
                            size,
                            directory: path.dirname(duplicates[0].path),
                            files: duplicates.map(f => ({
                                name: f.name,
                                path: f.path,
                                size: f.size
                            }))
                        });
                    }
                }
            }
        }
    }

    async calculateFileHash(filePath) {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('md5');
            const stream = fs.createReadStream(filePath);
            
            stream.on('data', data => hash.update(data));
            stream.on('end', () => resolve(hash.digest('hex')));
            stream.on('error', reject);
        });
    }
}

module.exports = new DuplicateScanner();
