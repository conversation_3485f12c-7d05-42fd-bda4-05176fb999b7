import { googleAuth } from './auth/google-auth.js';

/**
 * Troubleshoot domain-wide delegation issues
 */
async function troubleshootDelegation() {
    console.log('🔧 Troubleshooting Domain-Wide Delegation');
    console.log('='.repeat(60));

    // Get user email from command line argument
    const userEmail = process.argv[2] || '<EMAIL>';

    console.log(`👤 Testing for user: ${userEmail}`);
    console.log(`🔑 Service account: ${googleAuth.serviceAccountEmail}`);
    console.log(`📁 Project: ${googleAuth.projectId}`);
    console.log(`🆔 Client ID: ${googleAuth.clientId}`);

    // Run comprehensive troubleshooting
    const diagnostics = await googleAuth.troubleshootAccess(userEmail);

    console.log('\n' + '='.repeat(60));
    console.log('📋 FINAL DIAGNOSIS');
    console.log('='.repeat(60));

    if (diagnostics.canProceed) {
        console.log('🎉 SUCCESS: Domain-wide delegation is working!');
    } else {
        console.log('❌ FAILED: Domain-wide delegation needs configuration');
        
        console.log('\n🔧 REQUIRED STEPS:');
        console.log('1. Go to Google Cloud Console > IAM & Admin > Service Accounts');
        console.log(`2. Find service account: ${googleAuth.serviceAccountEmail}`);
        console.log('3. Click "Enable Google Workspace Domain-wide Delegation"');
        console.log('4. Go to Google Admin Console > Security > API Controls > Domain-wide Delegation');
        console.log(`5. Add Client ID: ${googleAuth.clientId}`);
        console.log('6. Add these OAuth Scopes:');
        console.log('   - https://www.googleapis.com/auth/drive');
        console.log('   - https://www.googleapis.com/auth/drive.file');
        console.log('   - https://www.googleapis.com/auth/drive.metadata');
        console.log('   - https://www.googleapis.com/auth/drive.readonly');
        console.log('7. Wait 5-10 minutes for changes to propagate');
        console.log('8. Run this script again to verify');
    }
}

troubleshootDelegation();
