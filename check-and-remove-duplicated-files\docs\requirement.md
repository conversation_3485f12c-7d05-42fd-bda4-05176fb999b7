# Yêu cầu
- Viết một tool với input textbox nhập vào là đường dẫn thư mục gốc
- Duyệt vào từng thư mục con (recursive), sau đó check các file đang có trong thư mục đó (chỉ check các file / thư mục cùng cấp, không cần check file của các thư mục khác nhau), nếu thấy hai file có tên start with gi<PERSON><PERSON> nhau, khác số ở cuối (ví dụ _1, _2,...), dung lượng giống nhau, thì check hash của hai file, nếu hash cũng giống nhau thì đánh dấu trùng.
- Tạo và hiển thị báo cáo các file bị trùng
- Tạo button action hỗ trợ xóa các file bị trùng theo báo cáo.
- Sử dụng một giao diện web để thực hiện các hành động xem file / folder bị trùng, và có action xóa các file / folder trùng.
- <PERSON><PERSON> giao diện theo dõi tiến độ realtime, nếu xóa file bị lỗi thì cần show nguyên nhân.