# UAT Test Cases

## Test Case 1: User Authentication
**Objective**: Verify user can authenticate with Google and Lark
**Steps**:
1. Navigate to authentication page
2. Upload Google Service Account credentials
3. Enter Lark App ID and Secret
4. Click "Test Connection"
**Expected**: Both connections successful

## Test Case 2: Drive Scanning
**Objective**: Verify system can scan Google Drive
**Steps**:
1. Complete authentication
2. Select "Full Drive Scan"
3. Start scanning process
4. Review discovered files
**Expected**: Files discovered and listed correctly

## Test Case 3: File Migration
**Objective**: Verify files can be migrated to Lark
**Steps**:
1. Select files for migration
2. Configure migration settings
3. Start migration process
4. Monitor progress
**Expected**: Files migrated successfully

## Test Case 4: Report Generation
**Objective**: Verify migration reports can be generated
**Steps**:
1. Complete a migration
2. Navigate to Reports section
3. Generate CSV and PDF reports
4. Download reports
**Expected**: Reports generated and downloadable

## Test Case 5: Error Handling
**Objective**: Verify system handles errors gracefully
**Steps**:
1. Simulate network errors
2. Test with invalid credentials
3. Test with corrupted files
**Expected**: Errors handled gracefully with clear messages

Generated: 2025-07-14T00:16:20.277Z
