/**
 * Download Worker
 * Worker x<PERSON> lý download đồng thời từ Google Drive
 */

import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';
import { createWriteStream } from 'fs';
import EventEmitter from 'events';
import { googleDriveAPI } from '../api/google-drive-api.js';

export class DownloadWorker extends EventEmitter {
    constructor(sessionId, sessionConfig, supabase) {
        super();
        this.sessionId = sessionId;
        this.sessionConfig = sessionConfig;
        this.supabase = supabase;
        this.isRunning = false;
        this.isPaused = false;
        this.isCancelled = false;
        this.activeDownloads = new Map(); // fileId -> download info
        this.stats = {
            downloadedFiles: 0,
            failedFiles: 0,
            skippedFiles: 0,
            downloadedSize: 0
        };
    }

    /**
     * Bắt đầu download process
     */
    async start() {
        if (this.isRunning) {
            throw new Error('Worker is already running');
        }

        this.isRunning = true;
        this.isPaused = false;
        this.isCancelled = false;

        try {
            console.log(`🚀 Starting download worker for session: ${this.sessionId}`);

            // L<PERSON>y danh sách files cần download - fetch all records in batches
            let allDownloadItems = [];
            let hasMore = true;
            let offset = 0;
            const batchSize = 1000;

            // Determine order based on session processing_order
            let orderBy = 'created_at';
            let ascending = true;

            if (this.sessionConfig.processing_order === 'user_email') {
                orderBy = 'user_email';
            } else if (this.sessionConfig.processing_order === 'size_asc') {
                orderBy = 'file_size';
                ascending = true;
            } else if (this.sessionConfig.processing_order === 'size_desc') {
                orderBy = 'file_size';
                ascending = false;
            }

            while (hasMore) {
                const { data: batchItems, error } = await this.supabase.getServiceClient()
                    .from('download_items')
                    .select('*')
                    .eq('download_session_id', this.sessionId)
                    .eq('status', 'pending')
                    .order(orderBy, { ascending })
                    .range(offset, offset + batchSize - 1);

                if (error) {
                    throw new Error(`Failed to get download items: ${error.message}`);
                }

                if (batchItems && batchItems.length > 0) {
                    allDownloadItems.push(...batchItems);
                    hasMore = batchItems.length === batchSize;
                    offset += batchSize;
                    console.log(`📄 Fetched download items batch: ${batchItems.length} items (Total so far: ${allDownloadItems.length})`);
                } else {
                    hasMore = false;
                }
            }

            const downloadItems = allDownloadItems;

            if (downloadItems.length === 0) {
                console.log('No files to download');
                await this.completeSession();
                return;
            }

            console.log(`📋 Found ${downloadItems.length} files to download`);

            // Tạo thư mục download path nếu chưa tồn tại
            if (!fs.existsSync(this.sessionConfig.download_path)) {
                fs.mkdirSync(this.sessionConfig.download_path, { recursive: true });
            }

            // Xử lý download với concurrency
            await this.processDownloads(downloadItems);

        } catch (error) {
            console.error('❌ Error in download worker:', error.message);
            await this.handleWorkerError(error);
            this.emit('error', error);
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Xử lý downloads với concurrency control
     */
    async processDownloads(downloadItems) {
        const concurrentDownloads = this.sessionConfig.concurrent_downloads || 3;
        const queue = [...downloadItems];
        const activePromises = [];
        let lastRetryCheck = Date.now();

        while (queue.length > 0 || activePromises.length > 0) {
            // Periodically check for retry items (every 5 seconds)
            if (Date.now() - lastRetryCheck > 5000) {
                await this.addRetryItemsToQueue(queue);
                lastRetryCheck = Date.now();
            }
            // Kiểm tra pause/cancel
            if (this.isPaused) {
                console.log('⏸️ Download paused, waiting...');
                await this.waitForResume();
            }

            if (this.isCancelled) {
                console.log('❌ Download cancelled');
                break;
            }

            // Bắt đầu downloads mới nếu có slot
            while (activePromises.length < concurrentDownloads && queue.length > 0) {
                const item = queue.shift();
                const promise = this.downloadFile(item)
                    .then(() => {
                        // Remove from active promises
                        const index = activePromises.indexOf(promise);
                        if (index > -1) {
                            activePromises.splice(index, 1);
                        }
                    })
                    .catch((error) => {
                        console.error(`Error downloading ${item.file_name}:`, error.message);

                        // If stop_on_error is enabled and this is a critical error, stop the process
                        if (this.sessionConfig.stop_on_error && error.message.includes('Stopping download process')) {
                            this.isCancelled = true;
                            throw error; // Re-throw to stop the entire process
                        }

                        // Remove from active promises
                        const index = activePromises.indexOf(promise);
                        if (index > -1) {
                            activePromises.splice(index, 1);
                        }
                    });

                activePromises.push(promise);
            }

            // Chờ ít nhất 1 download hoàn thành
            if (activePromises.length > 0) {
                await Promise.race(activePromises);
            }

            // Emit progress
            await this.emitProgress();
        }

        // Chờ tất cả downloads hoàn thành
        await Promise.all(activePromises);

        // Kiểm tra xem còn pending items với retry_count > 0 không
        let hasRetryItems = true;
        while (hasRetryItems && !this.isCancelled) {
            const { data: retryItems, error } = await this.supabase.getServiceClient()
                .from('download_items')
                .select('id')
                .eq('download_session_id', this.sessionId)
                .eq('status', 'pending')
                .gt('retry_count', 0);

            if (error) {
                console.error('Error checking retry items:', error.message);
                break;
            }

            if (retryItems && retryItems.length > 0) {
                console.log(`⏳ Waiting for ${retryItems.length} retry items...`);
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Add retry items back to queue and process them
                const retryQueue = [];
                await this.addRetryItemsToQueue(retryQueue);
                if (retryQueue.length > 0) {
                    await this.processDownloads(retryQueue);
                }
            } else {
                hasRetryItems = false;
            }
        }

        if (!this.isCancelled) {
            await this.completeSession();
        }
    }

    /**
     * Download một file
     */
    async downloadFile(downloadItem) {
        const startTime = Date.now();

        try {
            // Update status to downloading
            await this.updateDownloadItemStatus(downloadItem.id, 'downloading', {
                download_started_at: new Date().toISOString()
            });

            // Update session current file
            await this.updateSessionCurrentFile(downloadItem.user_email, downloadItem.file_name);

            console.log(`📥 Downloading: ${downloadItem.file_name} (${downloadItem.user_email})`);

            // Xử lý folder
            if (downloadItem.is_folder) {
                await this.createFolder(downloadItem);

                // Update stats for folder completion
                this.stats.downloadedFiles++;
                console.log(`✅ Created folder: ${downloadItem.file_name}`);

                // Emit progress after folder creation
                await this.emitProgress();
                return;
            }

            // Download file
            await this.downloadFileContent(downloadItem);

            // Update success status
            const duration = Date.now() - startTime;
            await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                download_completed_at: new Date().toISOString(),
                download_duration: duration
            });

            this.stats.downloadedFiles++;
            this.stats.downloadedSize += downloadItem.file_size;

            console.log(`✅ Downloaded: ${downloadItem.file_name} (${duration}ms)`);

            // Emit progress after each successful download
            await this.emitProgress();

        } catch (error) {
            console.error(`❌ Failed to download ${downloadItem.file_name}:`, error.message);

            // Xử lý trường hợp skip duplicate file
            if (error.message.startsWith('SKIP_DUPLICATE_FILE')) {
                // Update status to skipped
                await this.updateDownloadItemStatus(downloadItem.id, 'skipped', {
                    error_message: error.message,
                    download_completed_at: new Date().toISOString()
                });

                // Update scanned_files table
                await this.supabase.getServiceClient()
                    .from('scanned_files')
                    .update({
                        download_status: 'skipped',
                        downloaded_at: new Date().toISOString()
                    })
                    .eq('id', downloadItem.scanned_file_id);

                // Emit progress after skipping
                await this.emitProgress();
                return;
            }

            // Log error to session error log if continue_on_error is enabled
            if (this.sessionConfig.continue_on_error) {
                await this.logErrorToSession(downloadItem, error);
            }

            // Handle retry logic
            const newRetryCount = downloadItem.retry_count + 1;
            const maxRetries = this.sessionConfig.max_retries || 3;

            if (newRetryCount <= maxRetries) {
                console.log(`🔄 Retrying ${downloadItem.file_name} (${newRetryCount}/${maxRetries})`);
                await this.updateDownloadItemStatus(downloadItem.id, 'pending', {
                    retry_count: newRetryCount,
                    error_message: error.message
                });

                // Don't increment failedFiles for retries, only for final failures
                // Don't re-throw error, let the process continue
                return;
            } else {
                await this.updateDownloadItemStatus(downloadItem.id, 'failed', {
                    error_message: error.message
                });

                // Update scanned_files table with failed status
                await this.supabase.getServiceClient()
                    .from('scanned_files')
                    .update({
                        download_status: 'failed',
                        downloaded_at: new Date().toISOString()
                    })
                    .eq('id', downloadItem.scanned_file_id);

                this.stats.failedFiles++;

                // Emit progress after failed download
                await this.emitProgress();

                // If stop_on_error is enabled, throw error to stop the entire process
                if (this.sessionConfig.stop_on_error) {
                    throw new Error(`Download failed for ${downloadItem.file_name}: ${error.message}. Stopping download process.`);
                }
            }
        }
    }

    /**
     * Tạo folder
     */
    async createFolder(downloadItem) {
        try {
            // Tạo folder structure từ full_path
            const folderPath = this.buildLocalPath(downloadItem.file_path, downloadItem.user_email);

            if (!fs.existsSync(folderPath)) {
                fs.mkdirSync(folderPath, { recursive: true });
            }

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                local_path: folderPath,
                download_completed_at: new Date().toISOString()
            });

            // Update scanned_files table for folder
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    download_status: 'downloaded',
                    local_path: folderPath,
                    downloaded_at: new Date().toISOString()
                })
                .eq('id', downloadItem.scanned_file_id);

            console.log(`📁 Created folder: ${folderPath}`);

        } catch (error) {
            throw new Error(`Failed to create folder: ${error.message}`);
        }
    }

    /**
     * Download file content
     */
    async downloadFileContent(downloadItem) {
        try {
            const isGoogleDoc = googleDriveAPI.isGoogleDocsFormat(downloadItem.mime_type);

            // For Google Docs, export to downloadable format instead of just exposing
            if (isGoogleDoc) {
                return await this.downloadGoogleDoc(downloadItem);
            }

            // Build local file path từ full_path
            let localPath = this.buildLocalPath(downloadItem.file_path, downloadItem.user_email);
            const localDir = path.dirname(localPath);

            // Tạo thư mục nếu chưa tồn tại
            if (!fs.existsSync(localDir)) {
                fs.mkdirSync(localDir, { recursive: true });
            }

            // Handle file name conflicts
            const finalPath = this.handleFileNameConflict(localPath);

            // Download file từ Google Drive
            const fileStream = await this.getFileStream(downloadItem);
            const writeStream = createWriteStream(finalPath);

            // Stream file to local
            await pipeline(fileStream, writeStream);

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: finalPath
            });

            // Update scanned_files table
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    download_status: 'downloaded',
                    local_path: finalPath,
                    downloaded_at: new Date().toISOString()
                })
                .eq('id', downloadItem.scanned_file_id);

        } catch (error) {
            throw new Error(`Failed to download file content: ${error.message}`);
        }
    }

    /**
     * Download Google Doc by exporting to appropriate format
     */
    async downloadGoogleDoc(downloadItem) {
        try {
            // Define export formats for Google Docs
            const exportFormats = {
                'application/vnd.google-apps.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
                'application/vnd.google-apps.spreadsheet': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
                'application/vnd.google-apps.presentation': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
                'application/vnd.google-apps.drawing': 'image/png', // PNG
                'application/vnd.google-apps.form': 'application/pdf' // PDF
            };

            const exportMimeType = exportFormats[downloadItem.mime_type];
            if (!exportMimeType) {
                // Fallback to expose if no export format available
                return await this.exposeGoogleDoc(downloadItem);
            }

            // Determine file extension
            const extensions = {
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
                'image/png': '.png',
                'application/pdf': '.pdf'
            };

            const fileExtension = extensions[exportMimeType] || '.txt';

            // Build local file path
            let localPath = this.buildLocalPath(downloadItem.file_path, downloadItem.user_email);

            // Add appropriate extension if not present
            if (!localPath.endsWith(fileExtension)) {
                localPath += fileExtension;
            }

            const localDir = path.dirname(localPath);

            // Create directory if not exists
            if (!fs.existsSync(localDir)) {
                fs.mkdirSync(localDir, { recursive: true });
            }

            // Handle file name conflicts
            const finalPath = this.handleFileNameConflict(localPath);

            // Export file from Google Drive
            const fileStream = await googleDriveAPI.exportFile(downloadItem.user_email, downloadItem.file_id, exportMimeType);
            const writeStream = createWriteStream(finalPath);

            // Stream file to local
            await pipeline(fileStream, writeStream);

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: finalPath
            });

            // Update scanned_files table
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    download_status: 'downloaded',
                    local_path: finalPath,
                    downloaded_at: new Date().toISOString()
                })
                .eq('id', downloadItem.scanned_file_id);

            console.log(`📄 Downloaded Google Doc: ${downloadItem.file_name} -> ${finalPath}`);

            return {
                success: true,
                downloaded: true,
                localPath: finalPath,
                exportFormat: exportMimeType
            };

        } catch (error) {
            // Fallback to expose if export fails
            console.warn(`⚠️ Export failed for ${downloadItem.file_name}, falling back to expose: ${error.message}`);
            return await this.exposeGoogleDoc(downloadItem);
        }
    }

    /**
     * Expose Google Doc instead of downloading (fallback method)
     */
    async exposeGoogleDoc(downloadItem) {
        try {
            // Get file details to get webViewLink
            const fileDetails = await googleDriveAPI.getFile(downloadItem.user_email, downloadItem.file_id);

            // Update download status to exposed
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: null,
                web_view_link: fileDetails.webViewLink,
                export_links: JSON.stringify(fileDetails.exportLinks || {})
            });

            // Update scanned_files table
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    download_status: 'exposed',
                    web_view_link: fileDetails.webViewLink,
                    export_links: JSON.stringify(fileDetails.exportLinks || {}),
                    downloaded_at: new Date().toISOString()
                })
                .eq('id', downloadItem.scanned_file_id);

            console.log(`🔗 Exposed Google Doc: ${downloadItem.file_name} - ${fileDetails.webViewLink}`);

            return {
                success: true,
                exposed: true,
                webViewLink: fileDetails.webViewLink,
                exportLinks: fileDetails.exportLinks || {}
            };

        } catch (error) {
            throw new Error(`Failed to expose Google Doc: ${error.message}`);
        }
    }

    /**
     * Lấy file stream từ Google Drive
     */
    async getFileStream(downloadItem) {
        try {
            const isGoogleDoc = googleDriveAPI.isGoogleDocsFormat(downloadItem.mime_type);

            if (isGoogleDoc) {
                // For Google Docs, use expose approach instead of download
                throw new Error(`EXPOSE_REQUIRED:${downloadItem.file_id}`);
            } else {
                // Download binary file
                return await googleDriveAPI.downloadFileStream(downloadItem.user_email, downloadItem.file_id);
            }

        } catch (error) {
            throw new Error(`Failed to get file stream: ${error.message}`);
        }
    }

    /**
     * Lấy export MIME type cho Google Docs
     */
    getExportMimeType(mimeType) {
        const exportMap = {
            'application/vnd.google-apps.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
            'application/vnd.google-apps.spreadsheet': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.google-apps.presentation': 'application/vnd.openxmlformats-officedocument.presentationml.presentation' // .pptx
        };

        return exportMap[mimeType] || 'application/pdf';
    }

    /**
     * Build local file path from full_path
     */
    buildLocalPath(fullPath, userEmail) {
        // Remove leading slash if exists
        let relativePath = fullPath.replace(/^\//, '');

        // Check if full_path already contains user email (shared files)
        const hasUserEmail = relativePath.includes('@');

        let pathParts;
        if (hasUserEmail) {
            // For shared files, use full_path as is
            pathParts = relativePath.split('/').map(part => this.sanitizeFileName(part));
        } else {
            // For user's own files, prepend user email
            const userFolder = this.sanitizeFileName(userEmail);
            pathParts = [userFolder, ...relativePath.split('/').map(part => this.sanitizeFileName(part))];
        }

        // Build full local path
        return path.join(this.sessionConfig.download_path, ...pathParts);
    }

    /**
     * Sanitize file name
     */
    sanitizeFileName(fileName) {
        return fileName
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Handle file name conflicts - skip duplicates
     */
    handleFileNameConflict(filePath) {
        if (!fs.existsSync(filePath)) {
            return filePath;
        }

        // File already exists - skip it
        console.log(`⏭️ Skipping duplicate file: ${filePath}`);
        this.stats.skippedFiles++;
        throw new Error(`SKIP_DUPLICATE_FILE: File already exists at ${filePath}`);
    }

    /**
     * Update download item status
     */
    async updateDownloadItemStatus(itemId, status, additionalFields = {}) {
        const updateData = { ...additionalFields };
        if (status) {
            updateData.status = status;
        }

        await this.supabase.getServiceClient()
            .from('download_items')
            .update(updateData)
            .eq('id', itemId);
    }

    /**
     * Update session current file
     */
    async updateSessionCurrentFile(userEmail, fileName) {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                current_user_email: userEmail,
                current_file_name: fileName
            })
            .eq('id', this.sessionId);
    }

    /**
     * Emit progress event
     */
    async emitProgress() {
        // Update session stats
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                downloaded_files: this.stats.downloadedFiles,
                failed_files: this.stats.failedFiles,
                downloaded_size: this.stats.downloadedSize
            })
            .eq('id', this.sessionId);

        this.emit('progress', {
            downloadedFiles: this.stats.downloadedFiles,
            failedFiles: this.stats.failedFiles,
            skippedFiles: this.stats.skippedFiles,
            downloadedSize: this.stats.downloadedSize
        });
    }

    /**
     * Add retry items back to queue
     */
    async addRetryItemsToQueue(queue) {
        try {
            const { data: retryItems, error } = await this.supabase.getServiceClient()
                .from('download_items')
                .select('*')
                .eq('download_session_id', this.sessionId)
                .eq('status', 'pending')
                .gt('retry_count', 0)
                .order('created_at');

            if (error) {
                console.error('Failed to get retry items:', error.message);
                return;
            }

            if (retryItems.length > 0) {
                console.log(`🔄 Adding ${retryItems.length} retry items back to queue`);
                queue.push(...retryItems);
            }
        } catch (error) {
            console.error('Error adding retry items to queue:', error.message);
        }
    }

    /**
     * Complete session
     */
    async completeSession() {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                status: 'completed',
                completed_at: new Date().toISOString(),
                downloaded_files: this.stats.downloadedFiles,
                failed_files: this.stats.failedFiles,
                downloaded_size: this.stats.downloadedSize
            })
            .eq('id', this.sessionId);

        this.emit('completed', this.stats);
        console.log(`✅ Download session completed: ${this.sessionId}`);
    }

    /**
     * Handle worker error
     */
    async handleWorkerError(error) {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                status: 'failed',
                error_message: error.message,
                completed_at: new Date().toISOString()
            })
            .eq('id', this.sessionId);
    }

    /**
     * Log error to session error log for continue_on_error mode
     */
    async logErrorToSession(downloadItem, error) {
        try {
            // Get current session to read existing error log
            const { data: session, error: sessionError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('error_log')
                .eq('id', this.sessionId)
                .single();

            if (sessionError) {
                console.error('Failed to get session for error logging:', sessionError.message);
                return;
            }

            // Create error entry
            const errorEntry = {
                timestamp: new Date().toISOString(),
                file_id: downloadItem.file_id,
                file_name: downloadItem.file_name,
                file_path: downloadItem.file_path,
                user_email: downloadItem.user_email,
                error_message: error.message,
                retry_count: downloadItem.retry_count || 0
            };

            // Add to existing error log
            const currentErrorLog = session.error_log || [];
            const updatedErrorLog = [...currentErrorLog, errorEntry];

            // Update session with new error log
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    error_log: updatedErrorLog
                })
                .eq('id', this.sessionId);

            console.log(`📝 Logged error for ${downloadItem.file_name} to session error log`);
        } catch (logError) {
            console.error('Failed to log error to session:', logError.message);
        }
    }

    /**
     * Pause worker
     */
    async pause() {
        this.isPaused = true;
        console.log(`⏸️ Pausing download worker: ${this.sessionId}`);
    }

    /**
     * Resume worker
     */
    async resume() {
        this.isPaused = false;
        console.log(`▶️ Resuming download worker: ${this.sessionId}`);
    }

    /**
     * Cancel worker
     */
    async cancel() {
        this.isCancelled = true;
        console.log(`❌ Cancelling download worker: ${this.sessionId}`);
    }

    /**
     * Wait for resume
     */
    async waitForResume() {
        return new Promise((resolve) => {
            const checkResume = () => {
                if (!this.isPaused || this.isCancelled) {
                    resolve();
                } else {
                    setTimeout(checkResume, 1000);
                }
            };
            checkResume();
        });
    }
}
