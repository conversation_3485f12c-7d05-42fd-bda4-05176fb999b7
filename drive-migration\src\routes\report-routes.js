/**
 * Report Routes - API endpoints cho hệ thống báo cáo
 */

import express from 'express';
import path from 'path';
import fs from 'fs/promises';
import ReportService from '../services/report-service.js';

const router = express.Router();
const reportService = new ReportService();

/**
 * POST /api/reports/generate
 * Tạo báo cáo migration
 */
router.post('/generate', async (req, res) => {
    try {
        const { migrationTaskId, format = 'csv' } = req.body;

        if (!migrationTaskId) {
            return res.status(400).json({
                success: false,
                error: 'Migration task ID is required'
            });
        }

        if (!['csv', 'pdf'].includes(format)) {
            return res.status(400).json({
                success: false,
                error: 'Format must be csv or pdf'
            });
        }

        console.log(`📊 Generating ${format.toUpperCase()} report for migration task ${migrationTaskId}`);

        const report = await reportService.generateMigrationSummaryReport(migrationTaskId, format);

        res.json({
            success: true,
            data: {
                report,
                message: `${format.toUpperCase()} report generated successfully`
            }
        });

    } catch (error) {
        console.error('❌ Error generating report:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/reports/list
 * Lấy danh sách báo cáo có sẵn
 */
router.get('/list', async (req, res) => {
    try {
        const reports = await reportService.getAvailableReports();

        res.json({
            success: true,
            data: {
                reports,
                count: reports.length
            }
        });

    } catch (error) {
        console.error('❌ Error listing reports:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/reports/download/:filename
 * Tải xuống báo cáo
 */
router.get('/download/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        
        // Validate filename để tránh path traversal
        if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
            return res.status(400).json({
                success: false,
                error: 'Invalid filename'
            });
        }

        const filepath = path.join(process.cwd(), 'reports', filename);

        // Kiểm tra file có tồn tại không
        try {
            await fs.access(filepath);
        } catch (error) {
            return res.status(404).json({
                success: false,
                error: 'Report not found'
            });
        }

        // Xác định content type
        const ext = path.extname(filename).toLowerCase();
        let contentType = 'application/octet-stream';
        if (ext === '.csv') {
            contentType = 'text/csv';
        } else if (ext === '.pdf') {
            contentType = 'application/pdf';
        }

        // Set headers cho download
        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        // Stream file
        const fileStream = (await import('fs')).createReadStream(filepath);
        fileStream.pipe(res);

        fileStream.on('error', (error) => {
            console.error('❌ Error streaming file:', error);
            if (!res.headersSent) {
                res.status(500).json({
                    success: false,
                    error: 'Error downloading file'
                });
            }
        });

    } catch (error) {
        console.error('❌ Error downloading report:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * DELETE /api/reports/cleanup
 * Dọn dẹp báo cáo cũ
 */
router.delete('/cleanup', async (req, res) => {
    try {
        const { keepCount = 10 } = req.body;

        await reportService.cleanupOldReports(keepCount);

        res.json({
            success: true,
            data: {
                message: `Old reports cleaned up, keeping ${keepCount} most recent reports`
            }
        });

    } catch (error) {
        console.error('❌ Error cleaning up reports:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/reports/stats/:migrationTaskId
 * Lấy thống kê migration cho báo cáo
 */
router.get('/stats/:migrationTaskId', async (req, res) => {
    try {
        const { migrationTaskId } = req.params;
        const { supabaseClient } = await import('../database/supabase.js');

        // Lấy thông tin migration task
        const { data: migrationTask, error: taskError } = await supabaseClient.getServiceClient()
            .from('migration_tasks')
            .select('*')
            .eq('id', migrationTaskId)
            .single();

        if (taskError) throw taskError;

        // Lấy thống kê chi tiết
        const { data: stats, error: statsError } = await supabaseClient.getServiceClient()
            .rpc('get_migration_stats', { task_id: migrationTaskId });

        if (statsError) throw statsError;

        // Lấy thống kê theo status
        const { data: statusStats, error: statusError } = await supabaseClient.getServiceClient()
            .from('migration_items')
            .select('status')
            .eq('migration_task_id', migrationTaskId);

        if (statusError) throw statusError;

        // Tính toán thống kê status
        const statusCounts = statusStats.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
        }, {});

        // Lấy thống kê theo file type
        const { data: typeStats, error: typeError } = await supabaseClient.getServiceClient()
            .from('migration_items')
            .select('file_type, file_size')
            .eq('migration_task_id', migrationTaskId);

        if (typeError) throw typeError;

        // Tính toán thống kê file type
        const typeCounts = typeStats.reduce((acc, item) => {
            const type = item.file_type || 'unknown';
            if (!acc[type]) {
                acc[type] = { count: 0, totalSize: 0 };
            }
            acc[type].count++;
            acc[type].totalSize += item.file_size || 0;
            return acc;
        }, {});

        res.json({
            success: true,
            data: {
                migrationTask,
                stats: stats[0] || {},
                statusCounts,
                typeCounts,
                totalItems: statusStats.length
            }
        });

    } catch (error) {
        console.error('❌ Error getting migration stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

export default router;
