import React, { useState, useEffect, useRef } from 'react';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for realtime
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Migration Dashboard Component
 * Real-time dashboard cho migration progress tracking với:
 * - Overall migration progress
 * - File-by-file progress
 * - Error notifications
 * - Performance metrics
 * - ETA calculations
 */
const MigrationDashboard = ({ migrationId, onClose }) => {
  // State management
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [overallProgress, setOverallProgress] = useState(0);
  const [currentFile, setCurrentFile] = useState(null);
  const [recentFiles, setRecentFiles] = useState([]);
  const [errors, setErrors] = useState([]);
  const [metrics, setMetrics] = useState({
    speed: 0,
    eta: null,
    throughput: 0
  });
  const [isConnected, setIsConnected] = useState(false);
  const [startTime] = useState(Date.now());
  
  // Refs for cleanup
  const channelRef = useRef(null);
  const unsubscribeRef = useRef(null);

  // Initialize realtime connection
  useEffect(() => {
    if (!migrationId) return;

    console.log(`📡 Connecting to migration channel: ${migrationId}`);
    
    // Create channel
    const channel = supabase.channel(`migration_${migrationId}`, {
      config: {
        broadcast: { self: true },
        presence: { key: migrationId }
      }
    });

    channelRef.current = channel;

    // Setup event handlers
    channel
      .on('broadcast', { event: 'migration_progress' }, (payload) => {
        handleRealtimeUpdate(payload.payload);
      })
      .on('presence', { event: 'sync' }, () => {
        console.log('📡 Presence synced');
      })
      .subscribe((status) => {
        console.log(`📡 Channel status: ${status}`);
        setIsConnected(status === 'SUBSCRIBED');
      });

    // Cleanup function
    unsubscribeRef.current = () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
        channelRef.current = null;
      }
    };

    return unsubscribeRef.current;
  }, [migrationId]);

  // Handle realtime updates
  const handleRealtimeUpdate = (payload) => {
    console.log('📨 Realtime update:', payload.type);

    switch (payload.type) {
      case 'batch_progress':
        handleBatchProgress(payload);
        break;
      case 'file_progress':
        handleFileProgress(payload);
        break;
      case 'error':
        handleError(payload);
        break;
      case 'status_change':
        handleStatusChange(payload);
        break;
      case 'migration_complete':
        handleMigrationComplete(payload);
        break;
      default:
        console.log('Unknown update type:', payload.type);
    }
  };

  // Handle batch progress updates
  const handleBatchProgress = (payload) => {
    const progress = (payload.processedFiles / payload.totalFiles) * 100;
    setOverallProgress(progress);
    
    // Update metrics
    const elapsed = (Date.now() - startTime) / 1000; // seconds
    const speed = payload.processedFiles / elapsed; // files per second
    const remaining = payload.totalFiles - payload.processedFiles;
    const eta = remaining > 0 ? remaining / speed : 0;

    setMetrics({
      speed: speed.toFixed(2),
      eta: eta > 0 ? formatETA(eta) : null,
      throughput: payload.processedFiles
    });

    setMigrationStatus({
      totalFiles: payload.totalFiles,
      processedFiles: payload.processedFiles,
      successfulFiles: payload.successfulFiles,
      failedFiles: payload.failedFiles,
      status: 'running'
    });
  };

  // Handle file progress updates
  const handleFileProgress = (payload) => {
    const fileProgress = {
      fileId: payload.fileId,
      fileName: payload.fileName,
      phase: payload.phase,
      progress: payload.progress,
      speed: payload.speed,
      timestamp: payload.timestamp
    };

    setCurrentFile(fileProgress);

    // Add to recent files (keep last 10)
    setRecentFiles(prev => {
      const updated = [fileProgress, ...prev.filter(f => f.fileId !== payload.fileId)];
      return updated.slice(0, 10);
    });
  };

  // Handle error notifications
  const handleError = (payload) => {
    const error = {
      id: `${payload.fileId}_${Date.now()}`,
      severity: payload.severity,
      fileName: payload.fileName,
      message: payload.errorMessage,
      retryable: payload.retryable,
      timestamp: payload.timestamp
    };

    setErrors(prev => [error, ...prev.slice(0, 19)]); // Keep last 20 errors
  };

  // Handle status changes
  const handleStatusChange = (payload) => {
    setMigrationStatus(prev => ({
      ...prev,
      status: payload.status,
      reason: payload.reason
    }));
  };

  // Handle migration completion
  const handleMigrationComplete = (payload) => {
    setMigrationStatus({
      status: 'completed',
      totalFiles: payload.totalFiles,
      successfulFiles: payload.successfulFiles,
      failedFiles: payload.failedFiles,
      duration: payload.duration
    });
    setOverallProgress(100);
    setCurrentFile(null);
  };

  // Format ETA
  const formatETA = (seconds) => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'cancelled': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  // Get phase icon
  const getPhaseIcon = (phase) => {
    switch (phase) {
      case 'downloading': return '⬇️';
      case 'uploading': return '⬆️';
      case 'completed': return '✅';
      case 'failed': return '❌';
      default: return '⏳';
    }
  };

  if (!migrationId) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">No migration selected</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Migration Dashboard</h1>
          <p className="text-gray-600">Migration ID: {migrationId}</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm">{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>

      {/* Overall Progress */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Overall Progress</h2>
        
        {migrationStatus && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className={`font-medium ${getStatusColor(migrationStatus.status)}`}>
                Status: {migrationStatus.status?.toUpperCase()}
              </span>
              <span className="text-gray-600">
                {migrationStatus.processedFiles || 0} / {migrationStatus.totalFiles || 0} files
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div
                className="bg-blue-600 h-4 rounded-full transition-all duration-300"
                style={{ width: `${overallProgress}%` }}
              ></div>
            </div>
            
            <div className="text-center text-2xl font-bold text-blue-600">
              {overallProgress.toFixed(1)}%
            </div>
          </div>
        )}
      </div>

      {/* Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Processing Speed</h3>
          <p className="text-2xl font-bold text-blue-600">{metrics.speed} files/s</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Estimated Time</h3>
          <p className="text-2xl font-bold text-green-600">{metrics.eta || 'Calculating...'}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Files Processed</h3>
          <p className="text-2xl font-bold text-purple-600">{metrics.throughput}</p>
        </div>
      </div>

      {/* Current File */}
      {currentFile && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Current File</h2>
          <div className="flex items-center space-x-4">
            <span className="text-2xl">{getPhaseIcon(currentFile.phase)}</span>
            <div className="flex-1">
              <p className="font-medium text-gray-900">{currentFile.fileName}</p>
              <p className="text-sm text-gray-600">Phase: {currentFile.phase}</p>
              {currentFile.progress && (
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${currentFile.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{currentFile.progress?.toFixed(1)}%</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Recent Files */}
      {recentFiles.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Recent Files</h2>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {recentFiles.map((file, index) => (
              <div key={`${file.fileId}_${index}`} className="flex items-center justify-between py-2 border-b border-gray-100">
                <div className="flex items-center space-x-3">
                  <span>{getPhaseIcon(file.phase)}</span>
                  <span className="text-sm text-gray-900">{file.fileName}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(file.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4 text-red-600">Errors ({errors.length})</h2>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {errors.map((error) => (
              <div key={error.id} className="p-3 bg-red-50 border border-red-200 rounded">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-red-800">{error.fileName}</p>
                    <p className="text-sm text-red-600">{error.message}</p>
                  </div>
                  <div className="text-xs text-red-500">
                    {new Date(error.timestamp).toLocaleTimeString()}
                  </div>
                </div>
                {error.retryable && (
                  <button className="mt-2 text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700">
                    Retry
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MigrationDashboard;
