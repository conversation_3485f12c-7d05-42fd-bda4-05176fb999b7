import React, { useState, useEffect } from "react";
import UserList from "../components/UserList";
import TreeView from "../components/TreeView";
import Statistics from "../components/Statistics";
import UserSearch from "../components/UserSearch";
import UserStats from "../components/UserStats";
import ErrorDisplay from "../components/ErrorDisplay";
import { useToast } from "../contexts/ToastContext";
import { apiGet, formatError } from "../utils/apiUtils";
import "./UsersOverview.css";

function UsersOverview() {
    const [users, setUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);
    const [tree, setTree] = useState([]);
    const [stats, setStats] = useState(null);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingFiles, setLoadingFiles] = useState(false);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState("");
    const [viewMode, setViewMode] = useState("grid"); // grid, list
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

    // Toast notifications
    const { showError, showSuccess, showWarning } = useToast();

    // Load users on component mount
    useEffect(() => {
        loadUsers();
    }, []);

    const loadUsers = async () => {
        setLoading(true);
        setError(null);

        try {
            const result = await apiGet("/api/scan/users");
            setUsers(result.users || []);
            showSuccess(`Đã tải ${result.users?.length || 0} người dùng`);
        } catch (error) {
            console.error("Error loading users:", error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi tải danh sách người dùng: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000,
            });
        } finally {
            setLoading(false);
        }
    };

    const loadUserFiles = async (user) => {
        setLoadingFiles(true);
        setError(null);

        try {
            const result = await apiGet(`/api/scan/users/${user.email}/files`);
            setTree(Array.isArray(result.tree) ? result.tree : []);
            setStats(result.stats || null);
            setSelectedUser(user);
            setSelectedFiles([]);
            showSuccess(`Đã tải cấu trúc thư mục của ${user.email}`);
        } catch (error) {
            console.error("Error loading user files:", error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi tải thư mục của ${user.email}: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000,
            });
            setTree([]);
            setStats(null);
            setSelectedFiles([]);
        } finally {
            setLoadingFiles(false);
        }
    };

    const handleUserSelect = (user) => {
        if (selectedUser?.email === user.email) {
            // Toggle selection
            setSelectedUser(null);
            setUserFiles([]);
        } else {
            loadUserFiles(user);
        }
    };

    const handleRefreshUsers = () => {
        loadUsers();
    };

    const handleRefreshUserFiles = () => {
        if (selectedUser) {
            loadUserFiles(selectedUser);
        }
    };

    const filteredUsers = users.filter(
        (user) =>
            user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.name?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
        <div className="users-overview">
            <header className="users-overview-header">
                <div className="header-content">
                    <h1>📁 Quản lý Users & Files</h1>
                    <div className="header-actions">
                        <UserSearch
                            value={searchQuery}
                            onChange={setSearchQuery}
                            placeholder="Tìm kiếm theo email hoặc tên..."
                        />
                        <div className="view-controls">
                            <button
                                className={`btn-icon ${viewMode === "grid" ? "active" : ""}`}
                                onClick={() => setViewMode("grid")}
                                title="Chế độ lưới"
                            >
                                ⊞
                            </button>
                            <button
                                className={`btn-icon ${viewMode === "list" ? "active" : ""}`}
                                onClick={() => setViewMode("list")}
                                title="Chế độ danh sách"
                            >
                                ☰
                            </button>
                            <button
                                className="btn-icon"
                                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                                title={sidebarCollapsed ? "Mở rộng sidebar" : "Thu gọn sidebar"}
                            >
                                {sidebarCollapsed ? "▶" : "◀"}
                            </button>
                        </div>
                        <button
                            className="btn btn-secondary"
                            onClick={handleRefreshUsers}
                            disabled={loading}
                        >
                            🔄 Làm mới
                        </button>
                    </div>
                </div>

                {/* Stats */}
                <UserStats users={users} selectedUser={selectedUser} userFiles={tree} />
            </header>

            <main
                className={`users-overview-main ${sidebarCollapsed ? "sidebar-collapsed" : ""
                    }`}
            >
                {/* Users Sidebar */}
                <aside className="users-sidebar">
                    <div className="sidebar-header">
                        <h2>👥 Người dùng ({filteredUsers.length})</h2>
                        {selectedUser && (
                            <button
                                className="btn-small btn-ghost"
                                onClick={() => {
                                    setSelectedUser(null);
                                    setUserFiles([]);
                                }}
                                title="Bỏ chọn user"
                            >
                                ✕
                            </button>
                        )}
                    </div>

                    {error && (
                        <ErrorDisplay
                            error={error}
                            title="Lỗi tải dữ liệu"
                            onDismiss={() => setError(null)}
                            onRetry={() => {
                                if (selectedUser) {
                                    loadUserFiles(selectedUser);
                                } else {
                                    loadUsers();
                                }
                            }}
                            compact
                        />
                    )}

                    <UserList
                        users={filteredUsers}
                        selectedUser={selectedUser}
                        onUserSelect={handleUserSelect}
                        loading={loading}
                        viewMode={viewMode}
                    />
                </aside>

                {/* File Tree Content */}
                <section className="file-tree-content">
                    {selectedUser ? (
                        <div className="file-tree-container">
                            <div className="file-tree-header">
                                <h2>📂 Thư mục của {selectedUser.email}</h2>
                                <div className="file-tree-actions">
                                    <span className="file-count">
                                        {Array.isArray(tree) ? tree.length : 0} mục
                                    </span>
                                    <button
                                        className="btn-small btn-secondary"
                                        onClick={handleRefreshUserFiles}
                                        disabled={loadingFiles}
                                    >
                                        🔄 Làm mới
                                    </button>
                                </div>
                            </div>

                            {/* Statistics */}
                            <Statistics stats={stats} selectedFiles={selectedFiles} />

                            {/* Tree View */}
                            <TreeView
                                tree={Array.isArray(tree) ? tree : []}
                                selectedFiles={selectedFiles}
                                onFileSelect={(file, isSelected) => {
                                    let newSelection;
                                    if (isSelected) {
                                        newSelection = [...selectedFiles, file];
                                    } else {
                                        newSelection = selectedFiles.filter(
                                            (f) => f.id !== file.id
                                        );
                                    }
                                    setSelectedFiles(newSelection);
                                }}
                                onSelectAll={(allFiles) => setSelectedFiles(allFiles)}
                            />
                        </div>
                    ) : (
                        <div className="empty-state">
                            <div className="empty-state-content">
                                <div className="empty-icon">👆</div>
                                <h3>Chọn một người dùng</h3>
                                <p>
                                    Chọn một người dùng từ danh sách bên trái để xem cấu trúc thư
                                    mục của họ
                                </p>
                                {users.length === 0 && !loading && (
                                    <div className="empty-users">
                                        <p>Không có người dùng nào. Hãy kiểm tra kết nối API.</p>
                                        <button
                                            className="btn btn-primary"
                                            onClick={handleRefreshUsers}
                                        >
                                            Thử lại
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </section>
            </main>


        </div>
    );
}

export default UsersOverview;
