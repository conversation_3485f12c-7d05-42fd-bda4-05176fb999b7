/**
 * Test Configuration và Setup
 * <PERSON><PERSON><PERSON> hình chung cho tất cả unit tests
 */

import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load test environment variables
config({ path: path.join(__dirname, '../config/.env.test') });

// Test configuration
export const testConfig = {
    // Database
    database: {
        url: process.env.SUPABASE_URL || 'http://localhost:54321',
        key: process.env.SUPABASE_ANON_KEY || 'test-key',
        timeout: 5000
    },

    // Google Drive API - credentials loaded from JSON file by GoogleAuth class
    google: {
        // Google credentials are now loaded from google-service-account.json
        // No need for environment variables
    },

    // Lark API
    lark: {
        appId: process.env.LARK_APP_ID || 'test-app-id',
        appSecret: process.env.LARK_APP_SECRET || 'test-secret',
        baseUrl: process.env.LARK_BASE_URL || 'https://open.feishu.cn'
    },

    // Test settings
    test: {
        timeout: 10000,
        retries: 3,
        mockData: true,
        skipIntegration: process.env.SKIP_INTEGRATION_TESTS === 'true'
    }
};

// Test utilities
export class TestUtils {
    static generateUUID() {
        return 'test-' + Math.random().toString(36).substr(2, 9);
    }

    static generateEmail() {
        return `test-${Date.now()}@example.com`;
    }

    static generateFileName() {
        return `test-file-${Date.now()}.txt`;
    }

    static createMockFile(size = 1024) {
        return {
            id: this.generateUUID(),
            name: this.generateFileName(),
            mimeType: 'text/plain',
            size: size,
            parents: ['root'],
            createdTime: new Date().toISOString(),
            modifiedTime: new Date().toISOString(),
            webViewLink: `https://drive.google.com/file/d/${this.generateUUID()}/view`
        };
    }

    static createMockFolder() {
        return {
            id: this.generateUUID(),
            name: `test-folder-${Date.now()}`,
            mimeType: 'application/vnd.google-apps.folder',
            parents: ['root'],
            createdTime: new Date().toISOString(),
            modifiedTime: new Date().toISOString()
        };
    }

    static async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    static createMockScanSession() {
        return {
            id: this.generateUUID(),
            user_email: this.generateEmail(),
            scan_type: 'full_drive',
            status: 'running',
            total_files: 0,
            scanned_files: 0,
            started_at: new Date().toISOString()
        };
    }

    static createMockMigrationTask() {
        return {
            id: this.generateUUID(),
            user_email: this.generateEmail(),
            source_folder_id: 'root',
            target_folder_token: 'test-folder-token',
            status: 'pending',
            total_items: 0,
            completed_items: 0,
            created_at: new Date().toISOString()
        };
    }
}

// Mock data generators
export const mockData = {
    // Google Drive mock responses
    googleDrive: {
        fileList: {
            files: [
                TestUtils.createMockFile(1024),
                TestUtils.createMockFile(2048),
                TestUtils.createMockFolder()
            ],
            nextPageToken: null
        },

        fileMetadata: TestUtils.createMockFile(1024),

        folderMetadata: TestUtils.createMockFolder()
    },

    // Lark Drive mock responses
    larkDrive: {
        uploadResponse: {
            code: 0,
            msg: 'success',
            data: {
                file_token: 'test-file-token',
                name: 'test-file.txt',
                size: 1024
            }
        },

        folderResponse: {
            code: 0,
            msg: 'success',
            data: {
                token: 'test-folder-token',
                name: 'test-folder',
                parent_token: 'root'
            }
        }
    },

    // Database mock data
    database: {
        scanSession: TestUtils.createMockScanSession(),
        migrationTask: TestUtils.createMockMigrationTask(),
        scannedFile: {
            id: Math.floor(Math.random() * 1000000), // Changed from UUID to bigint
            scan_session_id: TestUtils.generateUUID(),
            file_id: TestUtils.generateUUID(),
            name: TestUtils.generateFileName(),
            mime_type: 'text/plain',
            size: 1024,
            full_path: '/test-folder/test-file.txt',
            depth: 1,
            is_selected: false
        }
    }
};

// Test assertions helpers
export class TestAssertions {
    static assertValidUUID(uuid, message = 'Should be valid UUID') {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(uuid)) {
            throw new Error(`${message}: ${uuid}`);
        }
    }

    static assertValidEmail(email, message = 'Should be valid email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error(`${message}: ${email}`);
        }
    }

    static assertValidTimestamp(timestamp, message = 'Should be valid timestamp') {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            throw new Error(`${message}: ${timestamp}`);
        }
    }

    static assertObjectHasKeys(obj, keys, message = 'Object should have required keys') {
        for (const key of keys) {
            if (!(key in obj)) {
                throw new Error(`${message}: missing key '${key}'`);
            }
        }
    }

    static assertArrayNotEmpty(arr, message = 'Array should not be empty') {
        if (!Array.isArray(arr) || arr.length === 0) {
            throw new Error(message);
        }
    }

    static assertNumberInRange(num, min, max, message = 'Number should be in range') {
        if (typeof num !== 'number' || num < min || num > max) {
            throw new Error(`${message}: ${num} not in range [${min}, ${max}]`);
        }
    }
}

// Test cleanup utilities
export class TestCleanup {
    static async cleanupDatabase() {
        // Implementation sẽ được thêm khi có database connection
        console.log('🧹 Cleaning up test database...');
    }

    static async cleanupFiles() {
        // Implementation sẽ được thêm khi có file operations
        console.log('🧹 Cleaning up test files...');
    }

    static async cleanupAll() {
        await this.cleanupDatabase();
        await this.cleanupFiles();
    }
}

export default {
    testConfig,
    TestUtils,
    mockData,
    TestAssertions,
    TestCleanup
};
