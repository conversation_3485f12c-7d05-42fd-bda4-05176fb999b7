/**
 * Error Utilities for Backend
 * Utility functions for error handling and formatting in backend services
 */

/**
 * Format error for API responses
 * @param {Error|string|object} error - Error to format
 * @returns {object} - Formatted error info
 */
export const formatError = (error) => {
    if (typeof error === 'string') {
        return { message: error, details: null, code: null };
    }

    if (error instanceof Error) {
        // Build detailed error info
        let details = [];

        if (error.originalMessage && error.originalMessage !== error.message) {
            details.push(`Original message: ${error.originalMessage}`);
        }

        if (error.status) {
            details.push(`HTTP status: ${error.status}`);
        }

        if (error.response?.data) {
            details.push(`API details: ${JSON.stringify(error.response.data, null, 2)}`);
        }

        // Add stack trace for development
        if (process.env.NODE_ENV === 'development' && error.stack) {
            details.push(`Stack trace:\n${error.stack}`);
        }

        return {
            message: error.message,
            details: details.length > 0 ? details.join('\n\n') : null,
            code: error.status || error.code || null
        };
    }

    // Handle API error responses
    if (error.response) {
        return {
            message: error.response.data?.message || error.response.statusText || 'API Error',
            details: error.response.data?.details || `HTTP ${error.response.status}`,
            code: error.response.status
        };
    }

    return {
        message: error.message || 'Unknown error',
        details: JSON.stringify(error, null, 2),
        code: null
    };
};

/**
 * Create a standardized API error
 * @param {string} message - Error message
 * @param {number} status - HTTP status code
 * @param {string} details - Additional details
 * @returns {Error} - Standardized error
 */
export const createApiError = (message, status = 500, details = null) => {
    const error = new Error(message);
    error.status = status;
    error.details = details;
    return error;
};

/**
 * Create a validation error
 * @param {string} field - Field name
 * @param {string} message - Validation message
 * @returns {Error} - Validation error
 */
export const createValidationError = (field, message) => {
    const error = new Error(`Validation error: ${field} - ${message}`);
    error.status = 400;
    error.field = field;
    error.type = 'validation';
    return error;
};

/**
 * Create a not found error
 * @param {string} resource - Resource name
 * @param {string} id - Resource ID
 * @returns {Error} - Not found error
 */
export const createNotFoundError = (resource, id = null) => {
    const message = id 
        ? `${resource} with ID '${id}' not found`
        : `${resource} not found`;
    const error = new Error(message);
    error.status = 404;
    error.resource = resource;
    error.resourceId = id;
    return error;
};

/**
 * Create an unauthorized error
 * @param {string} message - Error message
 * @returns {Error} - Unauthorized error
 */
export const createUnauthorizedError = (message = 'Unauthorized') => {
    const error = new Error(message);
    error.status = 401;
    return error;
};

/**
 * Create a forbidden error
 * @param {string} message - Error message
 * @returns {Error} - Forbidden error
 */
export const createForbiddenError = (message = 'Forbidden') => {
    const error = new Error(message);
    error.status = 403;
    return error;
};

/**
 * Wrap async function with error handling
 * @param {Function} fn - Async function to wrap
 * @returns {Function} - Wrapped function
 */
export const asyncErrorHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};

/**
 * Express error handler middleware
 * @param {Error} err - Error object
 * @param {object} req - Express request
 * @param {object} res - Express response
 * @param {Function} next - Express next function
 */
export const errorHandler = (err, req, res, next) => {
    const errorInfo = formatError(err);
    
    // Log error for debugging
    console.error('API Error:', {
        url: req.url,
        method: req.method,
        error: errorInfo,
        timestamp: new Date().toISOString()
    });

    // Send error response
    res.status(errorInfo.code || 500).json({
        error: true,
        message: errorInfo.message,
        details: errorInfo.details,
        code: errorInfo.code,
        timestamp: new Date().toISOString()
    });
};

/**
 * Handle database errors
 * @param {Error} error - Database error
 * @returns {Error} - Formatted database error
 */
export const handleDatabaseError = (error) => {
    if (error.code === '23505') { // Unique violation
        return createApiError('Duplicate entry', 409, error.detail);
    }
    
    if (error.code === '23503') { // Foreign key violation
        return createApiError('Referenced record not found', 400, error.detail);
    }
    
    if (error.code === '23502') { // Not null violation
        return createApiError('Required field missing', 400, error.detail);
    }
    
    if (error.code === '42P01') { // Undefined table
        return createApiError('Database table not found', 500, error.detail);
    }
    
    // Generic database error
    return createApiError('Database error', 500, error.message);
};

/**
 * Validate required fields
 * @param {object} data - Data to validate
 * @param {string[]} requiredFields - Required field names
 * @throws {Error} - Validation error if fields are missing
 */
export const validateRequiredFields = (data, requiredFields) => {
    const missingFields = requiredFields.filter(field => 
        data[field] === undefined || data[field] === null || data[field] === ''
    );
    
    if (missingFields.length > 0) {
        throw createValidationError(
            missingFields.join(', '), 
            'Required fields are missing'
        );
    }
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @throws {Error} - Validation error if email is invalid
 */
export const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        throw createValidationError('email', 'Invalid email format');
    }
};

/**
 * Validate number range
 * @param {number} value - Value to validate
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @param {string} fieldName - Field name for error message
 * @throws {Error} - Validation error if value is out of range
 */
export const validateNumberRange = (value, min, max, fieldName) => {
    if (typeof value !== 'number' || value < min || value > max) {
        throw createValidationError(
            fieldName, 
            `Must be a number between ${min} and ${max}`
        );
    }
};

/**
 * Validate array length
 * @param {Array} array - Array to validate
 * @param {number} minLength - Minimum length
 * @param {number} maxLength - Maximum length
 * @param {string} fieldName - Field name for error message
 * @throws {Error} - Validation error if array length is invalid
 */
export const validateArrayLength = (array, minLength, maxLength, fieldName) => {
    if (!Array.isArray(array) || array.length < minLength || array.length > maxLength) {
        throw createValidationError(
            fieldName, 
            `Must be an array with ${minLength}-${maxLength} items`
        );
    }
};

export default {
    formatError,
    createApiError,
    createValidationError,
    createNotFoundError,
    createUnauthorizedError,
    createForbiddenError,
    asyncErrorHandler,
    errorHandler,
    handleDatabaseError,
    validateRequiredFields,
    validateEmail,
    validateNumberRange,
    validateArrayLength
};
