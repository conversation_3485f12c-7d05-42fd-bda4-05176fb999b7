import express from 'express';
import { pathResolver } from '../services/path-resolver.js';

const router = express.Router();

/**
 * List folders in a directory
 * GET /api/folders/list
 */
router.get('/list', async (req, res) => {
    try {
        const { userEmail, parentId = 'root' } = req.query;

        if (!userEmail) {
            return res.status(400).json({
                error: 'User email is required'
            });
        }

        console.log(`📁 Listing folders for ${userEmail} in parent: ${parentId}`);

        const folders = await pathResolver.listFolders(userEmail, parentId);

        res.json({
            folders,
            parentId,
            count: folders.length
        });

    } catch (error) {
        console.error('❌ Error listing folders:', error.message);
        res.status(500).json({
            error: 'Failed to list folders',
            details: error.message
        });
    }
});

/**
 * Resolve folder path to ID
 * GET /api/folders/resolve-path
 */
router.get('/resolve-path', async (req, res) => {
    try {
        const { userEmail, path } = req.query;

        if (!userEmail || !path) {
            return res.status(400).json({
                error: 'User email and path are required'
            });
        }

        console.log(`📁 Resolving path for ${userEmail}: ${path}`);

        const folderId = await pathResolver.resolvePath(userEmail, path);

        res.json({
            path,
            folderId,
            success: true
        });

    } catch (error) {
        console.error('❌ Error resolving path:', error.message);
        res.status(500).json({
            error: 'Failed to resolve path',
            details: error.message,
            path: req.query.path
        });
    }
});

/**
 * Resolve folder ID to path
 * GET /api/folders/resolve-id
 */
router.get('/resolve-id', async (req, res) => {
    try {
        const { userEmail, folderId } = req.query;

        if (!userEmail || !folderId) {
            return res.status(400).json({
                error: 'User email and folder ID are required'
            });
        }

        console.log(`📁 Resolving ID for ${userEmail}: ${folderId}`);

        const path = await pathResolver.resolveId(userEmail, folderId);

        res.json({
            folderId,
            path,
            success: true
        });

    } catch (error) {
        console.error('❌ Error resolving folder ID:', error.message);
        res.status(500).json({
            error: 'Failed to resolve folder ID',
            details: error.message,
            folderId: req.query.folderId
        });
    }
});

/**
 * Get folder tree structure
 * GET /api/folders/tree
 */
router.get('/tree', async (req, res) => {
    try {
        const { userEmail, rootId = 'root', maxDepth = 3 } = req.query;

        if (!userEmail) {
            return res.status(400).json({
                error: 'User email is required'
            });
        }

        console.log(`🌳 Building folder tree for ${userEmail} from ${rootId} with depth ${maxDepth}`);

        const tree = await pathResolver.getFolderTree(userEmail, rootId, parseInt(maxDepth));

        res.json({
            tree,
            rootId,
            maxDepth: parseInt(maxDepth)
        });

    } catch (error) {
        console.error('❌ Error building folder tree:', error.message);
        res.status(500).json({
            error: 'Failed to build folder tree',
            details: error.message
        });
    }
});

/**
 * Search folders by name
 * GET /api/folders/search
 */
router.get('/search', async (req, res) => {
    try {
        const { userEmail, query, maxResults = 20 } = req.query;

        if (!userEmail || !query) {
            return res.status(400).json({
                error: 'User email and search query are required'
            });
        }

        console.log(`🔍 Searching folders for ${userEmail}: "${query}"`);

        // Use Google Drive API to search for folders
        const searchQuery = `mimeType='application/vnd.google-apps.folder' and name contains '${query}' and trashed=false`;
        
        const response = await pathResolver.driveAPI.listFiles(userEmail, {
            q: searchQuery,
            fields: 'files(id, name, parents, createdTime, modifiedTime)',
            supportsAllDrives: true,
            includeItemsFromAllDrives: true,
            pageSize: parseInt(maxResults)
        });

        const folders = response.files || [];

        // Resolve paths for found folders
        const foldersWithPaths = await Promise.all(
            folders.map(async (folder) => {
                try {
                    const path = await pathResolver.resolveId(userEmail, folder.id);
                    return {
                        ...folder,
                        path
                    };
                } catch (error) {
                    console.warn(`⚠️ Could not resolve path for folder ${folder.id}: ${error.message}`);
                    return {
                        ...folder,
                        path: `/${folder.name}` // Fallback path
                    };
                }
            })
        );

        res.json({
            folders: foldersWithPaths,
            query,
            count: foldersWithPaths.length
        });

    } catch (error) {
        console.error('❌ Error searching folders:', error.message);
        res.status(500).json({
            error: 'Failed to search folders',
            details: error.message
        });
    }
});

/**
 * Get folder information
 * GET /api/folders/info/:folderId
 */
router.get('/info/:folderId', async (req, res) => {
    try {
        const { folderId } = req.params;
        const { userEmail } = req.query;

        if (!userEmail) {
            return res.status(400).json({
                error: 'User email is required'
            });
        }

        console.log(`📁 Getting folder info for ${userEmail}: ${folderId}`);

        const folderInfo = await pathResolver.getFolderInfo(userEmail, folderId);

        if (!folderInfo) {
            return res.status(404).json({
                error: 'Folder not found'
            });
        }

        // Resolve path
        const path = await pathResolver.resolveId(userEmail, folderId);

        res.json({
            ...folderInfo,
            path
        });

    } catch (error) {
        console.error('❌ Error getting folder info:', error.message);
        res.status(500).json({
            error: 'Failed to get folder info',
            details: error.message
        });
    }
});

/**
 * Clear path resolver cache
 * POST /api/folders/clear-cache
 */
router.post('/clear-cache', async (req, res) => {
    try {
        pathResolver.clearCache();

        res.json({
            success: true,
            message: 'Path resolver cache cleared'
        });

    } catch (error) {
        console.error('❌ Error clearing cache:', error.message);
        res.status(500).json({
            error: 'Failed to clear cache',
            details: error.message
        });
    }
});

/**
 * Get cache statistics
 * GET /api/folders/cache-stats
 */
router.get('/cache-stats', async (req, res) => {
    try {
        const stats = pathResolver.getCacheStats();

        res.json({
            cacheStats: stats,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ Error getting cache stats:', error.message);
        res.status(500).json({
            error: 'Failed to get cache stats',
            details: error.message
        });
    }
});

export default router;
